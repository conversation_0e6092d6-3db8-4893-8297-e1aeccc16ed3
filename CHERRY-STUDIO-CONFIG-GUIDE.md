# Cherry Studio MCP Chart Server 配置指南

本指南详细说明如何在 Cherry Studio 中配置 MCP Chart Server，支持局域网访问和多种传输方式。

## 🎯 配置概述

MCP Chart Server 支持两种传输方式：
- **SSE (Server-Sent Events)** - 推荐，更稳定
- **Streamable** - 流式传输，适合大数据量

## 📋 前提条件

1. ✅ MCP Chart Server 已成功部署
2. ✅ 服务器IP已正确配置 (例如: **************)
3. ✅ 防火墙已开放相关端口 (80, 1122, 1123, 3000)
4. ✅ Cherry Studio 已安装并可访问服务器

## 🔧 配置步骤

### 步骤1: 获取配置文件

部署完成后，在服务器的 `configs/` 目录下会自动生成配置文件：

```bash
configs/
├── cherry-studio-sse-config.json        # SSE传输配置
├── cherry-studio-streamable-config.json # Streamable传输配置
└── cherry-studio-http-config.json       # HTTP配置
```

### 步骤2: 选择配置方式

#### 方式一：SSE传输 (推荐)

```json
{
  "mcpServers": {
    "mcp-chart-server-sse": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/sdk",
        "client",
        "http://**************/sse"
      ],
      "env": {
        "MCP_SERVER_URL": "http://**************/sse"
      }
    }
  }
}
```

#### 方式二：Streamable传输

```json
{
  "mcpServers": {
    "mcp-chart-server-streamable": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/sdk",
        "client",
        "http://**************/mcp"
      ],
      "env": {
        "MCP_SERVER_URL": "http://**************/mcp"
      }
    }
  }
}
```

### 步骤3: 在Cherry Studio中应用配置

1. **打开Cherry Studio设置**
   - 点击设置图标
   - 找到 "MCP Servers" 或 "插件配置" 选项

2. **添加MCP服务器**
   - 点击 "添加服务器" 或 "+"
   - 选择 "从配置文件导入" 或 "手动配置"

3. **导入配置**
   - 如果支持文件导入：选择对应的 `.json` 配置文件
   - 如果需要手动配置：复制粘贴配置内容

4. **验证配置**
   - 保存配置
   - 重启Cherry Studio
   - 检查MCP服务器状态

## 🌐 网络配置说明

### 服务端口说明

| 端口 | 服务 | 用途 |
|------|------|------|
| 80 | Nginx | 反向代理，统一入口 |
| 1122 | MCP SSE | SSE传输服务 |
| 1123 | MCP Streamable | Streamable传输服务 |
| 3000 | GPT-Vis-SSR | 图表渲染服务 |

### 访问地址

假设服务器IP为 `**************`：

- **MCP SSE端点**: `http://**************/sse`
- **MCP Streamable端点**: `http://**************/mcp`
- **图表渲染API**: `http://**************/api/gpt-vis`
- **健康检查**: `http://**************/health`

## 🔍 故障排除

### 常见问题

#### 1. 连接超时或拒绝连接

**症状**: Cherry Studio显示连接失败
**解决方案**:
```bash
# 检查服务状态
./manage-services.sh status

# 检查端口是否开放
netstat -tlnp | grep -E ':(80|1122|1123|3000)'

# 测试网络连通性
curl http://**************/health
```

#### 2. 图片URL无法访问

**症状**: 图表生成成功但图片链接打不开，返回类似 `http://gpt-vis-ssr:3000/images/xxx.png` 的内部地址
**原因**: 服务器IP配置不正确，返回的是容器内部地址而不是局域网可访问的地址
**解决方案**:
```bash
# 重新配置服务器IP和端口
./configure-server-ip.sh --ip **************

# 如果使用自定义端口
./configure-server-ip.sh --ip ************** --port 8080

# 重启服务
docker-compose restart
```

**验证修复**:
```bash
# 测试图表渲染，检查返回的图片URL
curl -X POST http://**************/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{"type": "line", "data": [{"time": "2023-01", "value": 100}]}'

# 应该返回类似: http://**************/images/xxx.png (可访问的地址)
```

#### 3. MCP服务器未响应

**症状**: Cherry Studio中MCP服务器显示离线
**解决方案**:
```bash
# 检查MCP服务日志
docker-compose logs mcp-server-chart-sse
docker-compose logs mcp-server-chart-streamable

# 测试MCP端点
curl http://**************/sse
curl http://**************/mcp
```

### 调试命令

```bash
# 完整测试
./test-services.sh

# 局域网访问测试
./test-lan-access.sh

# 查看服务日志
./manage-services.sh logs

# 重启所有服务
./manage-services.sh restart
```

## 📱 Cherry Studio 具体配置步骤

### 对于不同版本的Cherry Studio

#### Cherry Studio v1.x

1. 打开设置 → MCP配置
2. 添加新的MCP服务器
3. 填写配置信息：
   - **名称**: `MCP Chart Server`
   - **命令**: `npx`
   - **参数**: `["-y", "@modelcontextprotocol/sdk", "client", "http://**************/sse"]`
   - **环境变量**: `MCP_SERVER_URL=http://**************/sse`

#### Cherry Studio v2.x

1. 设置 → 插件 → MCP服务器
2. 导入配置文件或手动添加
3. 使用生成的JSON配置文件

### 验证配置成功

配置成功后，在Cherry Studio中应该能看到：

1. ✅ MCP服务器状态为"已连接"
2. ✅ 可用工具列表包含图表生成工具
3. ✅ 能够成功生成图表并显示

## 🎨 使用示例

配置成功后，在Cherry Studio中可以这样使用：

```
请帮我生成一个折线图，数据如下：
- 2023年1月: 100
- 2023年2月: 120  
- 2023年3月: 150
- 2023年4月: 130
- 2023年5月: 180
```

系统会自动调用MCP Chart Server生成图表，并返回可访问的图片链接。

## 🔧 高级配置

### HTTPS配置

如果需要使用HTTPS：

```bash
# 配置HTTPS
./configure-server-ip.sh --ip ************** --https

# 更新Cherry Studio配置中的URL
# 将 http:// 改为 https://
```

### 自定义端口

如果需要使用自定义端口：

```bash
# 配置自定义端口
./configure-server-ip.sh --ip ************** --port 8080

# 更新访问地址为 http://**************:8080
```

### 负载均衡

对于高并发场景，可以配置多个MCP服务实例：

```yaml
# 在docker-compose.yml中添加更多实例
mcp-server-chart-sse-2:
  # ... 配置类似，使用不同端口
```

## 📞 技术支持

如果遇到问题：

1. **查看日志**: `./manage-services.sh logs`
2. **运行测试**: `./test-services.sh`
3. **检查网络**: `ping **************`
4. **验证配置**: `./configure-server-ip.sh --ip **************`

---

**🎉 配置完成后，您就可以在Cherry Studio中愉快地使用MCP Chart Server了！**
