#!/bin/bash

# 测试MCP图表生成功能
# 验证服务是否正常工作

echo "=== MCP图表生成测试 ==="

echo "1. 检查服务状态..."
if ! docker ps | grep -q gpt-vis-ssr; then
    echo "❌ 渲染服务未运行"
    exit 1
fi

if ! docker ps | grep -q mcp-chart-server; then
    echo "❌ MCP服务未运行"
    exit 1
fi

echo "✅ 服务容器正在运行"

echo ""
echo "2. 测试渲染服务健康检查..."
if curl -s --connect-timeout 5 http://localhost:3000/health > /dev/null; then
    echo "✅ 渲染服务响应正常"
    curl -s http://localhost:3000/health | jq . 2>/dev/null || curl -s http://localhost:3000/health
else
    echo "❌ 渲染服务无响应"
    echo "查看渲染服务日志:"
    docker logs --tail=10 gpt-vis-ssr
    exit 1
fi

echo ""
echo "3. 测试MCP服务连接..."
if curl -s --connect-timeout 5 http://localhost:1122/sse > /dev/null; then
    echo "✅ MCP服务响应正常"
else
    echo "❌ MCP服务无响应"
    echo "查看MCP服务日志:"
    docker logs --tail=10 mcp-chart-server
    exit 1
fi

echo ""
echo "4. 测试容器间网络连接..."
# 先检查容器中是否有curl
if docker exec mcp-chart-server which curl >/dev/null 2>&1; then
    echo "curl工具已安装，测试网络连接..."
    if docker exec mcp-chart-server curl -s --connect-timeout 5 http://gpt-vis-ssr:3000/health 2>/dev/null; then
        echo "✅ 容器间网络连接正常"
    else
        echo "❌ 容器间网络连接失败"
        echo "尝试安装curl并重试..."
        docker exec mcp-chart-server sh -c "apk add --no-cache curl" 2>/dev/null || true
        if docker exec mcp-chart-server curl -s --connect-timeout 5 http://gpt-vis-ssr:3000/health 2>/dev/null; then
            echo "✅ 安装curl后网络连接正常"
        else
            echo "❌ 网络连接仍然失败"
        fi
    fi
else
    echo "curl工具未安装，尝试安装..."
    docker exec mcp-chart-server sh -c "apk add --no-cache curl" 2>/dev/null || true
    if docker exec mcp-chart-server curl -s --connect-timeout 5 http://gpt-vis-ssr:3000/health 2>/dev/null; then
        echo "✅ 安装curl后网络连接正常"
    else
        echo "❌ 网络连接失败，使用替代方法测试..."
        # 使用nc (netcat) 测试端口连接
        if docker exec mcp-chart-server nc -z gpt-vis-ssr 3000 2>/dev/null; then
            echo "✅ 端口连接正常（使用nc测试）"
        else
            echo "❌ 端口连接失败"
            echo "检查网络配置:"
            docker network ls
        fi
    fi
fi

echo ""
echo "5. 测试图表生成API..."
echo "发送饼图生成请求..."

RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "苹果", "value": 30},
      {"category": "香蕉", "value": 25},
      {"category": "橙子", "value": 20},
      {"category": "葡萄", "value": 15},
      {"category": "其他", "value": 10}
    ],
    "title": "水果销量分布"
  }' \
  --connect-timeout 10 \
  --max-time 30)

if echo "$RESPONSE" | grep -q '"success":true'; then
    echo "✅ 图表生成成功"
    echo "响应长度: $(echo "$RESPONSE" | wc -c) 字符"
else
    echo "❌ 图表生成失败"
    echo "响应内容: $RESPONSE"
    exit 1
fi

echo ""
echo "6. 测试MCP工具调用..."
echo "模拟MCP工具调用..."

# 创建临时测试文件
cat > /tmp/mcp_test_request.json << 'EOF'
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "generate_pie_chart",
    "arguments": {
      "data": [
        {"category": "苹果", "value": 30},
        {"category": "香蕉", "value": 25},
        {"category": "橙子", "value": 20},
        {"category": "葡萄", "value": 15},
        {"category": "其他", "value": 10}
      ],
      "title": "水果销量分布"
    }
  }
}
EOF

# 注意：这个测试可能不会成功，因为MCP协议需要特定的握手过程
# 但我们可以检查服务是否至少在监听
if nc -z localhost 1122 2>/dev/null; then
    echo "✅ MCP端口1122可访问"
else
    echo "❌ MCP端口1122不可访问"
fi

echo ""
echo "7. 检查环境变量配置..."
VIS_SERVER=$(docker exec mcp-chart-server env | grep VIS_REQUEST_SERVER || echo "未设置")
echo "VIS_REQUEST_SERVER: $VIS_SERVER"

if echo "$VIS_SERVER" | grep -q "gpt-vis-ssr:3000"; then
    echo "✅ 环境变量配置正确"
else
    echo "❌ 环境变量配置可能有问题"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如果所有测试都通过，但Cherry Studio仍然报错，可能的原因："
echo "1. Cherry Studio配置的URL不正确"
echo "2. 防火墙阻止了连接"
echo "3. MCP协议握手问题"
echo ""
echo "建议的Cherry Studio配置："
echo "  服务器URL: http://localhost:1122/sse"
echo "  或者: http://YOUR_SERVER_IP:1122/sse"
echo ""
echo "如果问题仍然存在，请："
echo "1. 检查Cherry Studio的错误日志"
echo "2. 确认服务器IP地址是否正确"
echo "3. 运行修复脚本: sudo ./fix-mcp-connection.sh"

# 清理临时文件
rm -f /tmp/mcp_test_request.json
