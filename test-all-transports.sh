#!/bin/bash

# 测试所有MCP传输方式
# 验证stdio、SSE和Streamable传输是否正常工作

echo "=== 测试所有MCP传输方式 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

success_count=0
total_tests=0

print_result() {
    local test_name="$1"
    local result="$2"
    total_tests=$((total_tests + 1))
    
    if [ "$result" = "success" ]; then
        echo -e "${GREEN}✅ $test_name${NC}"
        success_count=$((success_count + 1))
    else
        echo -e "${RED}❌ $test_name${NC}"
    fi
}

echo "1. 检查基础环境..."

# 检查Node.js
if command -v node &> /dev/null; then
    print_result "Node.js已安装 ($(node --version))" "success"
else
    print_result "Node.js未安装" "fail"
fi

# 检查npm
if command -v npm &> /dev/null; then
    print_result "npm已安装 ($(npm --version))" "success"
else
    print_result "npm未安装" "fail"
fi

# 检查全局包
if npm list -g @antv/mcp-server-chart &> /dev/null; then
    print_result "mcp-server-chart全局包已安装" "success"
else
    print_result "mcp-server-chart全局包未安装" "fail"
fi

# 检查命令行工具
if command -v mcp-server-chart &> /dev/null; then
    print_result "mcp-server-chart命令行工具可用" "success"
else
    print_result "mcp-server-chart命令行工具不可用" "fail"
fi

echo ""
echo "2. 测试stdio传输..."

# 创建临时测试脚本
cat > /tmp/test_stdio.js << 'EOF'
const { spawn } = require('child_process');

const child = spawn('mcp-server-chart', ['--transport', 'stdio'], {
    stdio: ['pipe', 'pipe', 'pipe']
});

const initMessage = {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
        protocolVersion: "2024-11-05",
        capabilities: {
            roots: { listChanged: true },
            sampling: {}
        }
    }
};

child.stdin.write(JSON.stringify(initMessage) + '\n');

let output = '';
child.stdout.on('data', (data) => {
    output += data.toString();
});

setTimeout(() => {
    child.kill();
    if (output.includes('jsonrpc') && output.includes('result')) {
        console.log('SUCCESS');
    } else {
        console.log('FAIL');
    }
}, 3000);
EOF

if node /tmp/test_stdio.js 2>/dev/null | grep -q "SUCCESS"; then
    print_result "stdio传输测试" "success"
else
    print_result "stdio传输测试" "fail"
fi

rm -f /tmp/test_stdio.js

echo ""
echo "3. 测试HTTP服务..."

# 检查渲染服务
if curl -s --connect-timeout 5 http://localhost:3000/health > /dev/null; then
    print_result "渲染服务 (端口3000)" "success"
else
    print_result "渲染服务 (端口3000)" "fail"
fi

# 检查SSE服务
if curl -s --connect-timeout 5 http://localhost:1122/sse > /dev/null; then
    print_result "SSE服务 (端口1122)" "success"
else
    print_result "SSE服务 (端口1122)" "fail"
fi

# 检查Streamable服务
if curl -s --connect-timeout 5 http://localhost:1123/mcp > /dev/null; then
    print_result "Streamable服务 (端口1123)" "success"
else
    print_result "Streamable服务 (端口1123)" "fail"
fi

echo ""
echo "4. 测试Docker服务..."

# 检查Docker容器
if docker ps --format "{{.Names}}" | grep -q "gpt-vis-ssr"; then
    print_result "渲染服务容器运行中" "success"
else
    print_result "渲染服务容器运行中" "fail"
fi

if docker ps --format "{{.Names}}" | grep -q "mcp-sse-server"; then
    print_result "SSE服务容器运行中" "success"
else
    print_result "SSE服务容器运行中" "fail"
fi

if docker ps --format "{{.Names}}" | grep -q "mcp-streamable-server"; then
    print_result "Streamable服务容器运行中" "success"
else
    print_result "Streamable服务容器运行中" "fail"
fi

echo ""
echo "5. 测试图表生成..."

# 测试渲染服务API
RENDER_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "测试1", "value": 60},
      {"category": "测试2", "value": 40}
    ]
  }' \
  --connect-timeout 10 \
  --max-time 30 2>/dev/null || echo "")

if echo "$RENDER_RESPONSE" | grep -q '"success":true'; then
    print_result "图表生成API测试" "success"
else
    print_result "图表生成API测试" "fail"
fi

echo ""
echo "6. 检查网络连接..."

# 检查容器间网络
if docker exec gpt-vis-ssr curl -s http://localhost:3000/health > /dev/null 2>&1; then
    print_result "容器内部网络连接" "success"
else
    print_result "容器内部网络连接" "fail"
fi

# 检查容器间通信
if docker exec mcp-sse-server curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
    print_result "容器间网络通信" "success"
else
    print_result "容器间网络通信" "fail"
fi

echo ""
echo "7. 检查配置文件..."

# 检查配置示例
if [ -d "$HOME/mcp-configs" ]; then
    print_result "配置示例目录存在" "success"
else
    print_result "配置示例目录存在" "fail"
fi

# 检查Claude Desktop配置（macOS）
if [ -f "$HOME/Library/Application Support/Claude/claude_desktop_config.json" ]; then
    print_result "Claude Desktop配置文件存在 (macOS)" "success"
else
    print_result "Claude Desktop配置文件存在 (macOS)" "fail"
fi

echo ""
echo "=== 测试结果汇总 ==="
echo -e "总测试数: $total_tests"
echo -e "成功数: ${GREEN}$success_count${NC}"
echo -e "失败数: ${RED}$((total_tests - success_count))${NC}"
echo -e "成功率: $(( success_count * 100 / total_tests ))%"

echo ""
echo "=== 传输方式状态 ==="

# stdio状态
if command -v mcp-server-chart &> /dev/null; then
    echo -e "${GREEN}📱 stdio传输: 可用${NC}"
    echo "   用于: Claude Desktop, VSCode, Cursor"
    echo "   配置: 见 ~/mcp-configs/claude_desktop_config_*.json"
else
    echo -e "${RED}📱 stdio传输: 不可用${NC}"
    echo "   解决: 运行 sudo ./setup-stdio-mcp.sh"
fi

# SSE状态
if curl -s --connect-timeout 2 http://localhost:1122/sse > /dev/null; then
    echo -e "${GREEN}🌐 SSE传输: 可用${NC}"
    echo "   地址: http://localhost:1122/sse"
    echo "   用于: Cherry Studio, Web客户端"
else
    echo -e "${RED}🌐 SSE传输: 不可用${NC}"
    echo "   解决: 运行 sudo ./deploy-multi-transport.sh"
fi

# Streamable状态
if curl -s --connect-timeout 2 http://localhost:1123/mcp > /dev/null; then
    echo -e "${GREEN}🚀 Streamable传输: 可用${NC}"
    echo "   地址: http://localhost:1123/mcp"
    echo "   用于: 高性能客户端"
else
    echo -e "${RED}🚀 Streamable传输: 不可用${NC}"
    echo "   解决: 运行 sudo ./deploy-multi-transport.sh"
fi

echo ""
echo "=== 推荐操作 ==="

if [ $success_count -lt $((total_tests * 3 / 4)) ]; then
    echo -e "${YELLOW}⚠️ 检测到多个问题，建议重新部署:${NC}"
    echo "   sudo ./deploy-multi-transport.sh"
    echo "   sudo ./setup-stdio-mcp.sh"
else
    echo -e "${GREEN}✅ 大部分功能正常，可以开始使用${NC}"
fi

echo ""
echo "📖 详细配置说明: mcp-client-configs.md"
echo "🔧 故障排除脚本: diagnose-mcp-connection.sh"
