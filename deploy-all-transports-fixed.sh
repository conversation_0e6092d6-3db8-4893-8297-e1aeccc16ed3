#!/bin/bash

# 部署所有传输方式的MCP解决方案
# 支持SSE、Streamable和stdio传输，配置外部IP **************

set -e

SERVER_IP="**************"
echo "=== 部署所有传输方式MCP解决方案 (外部IP: $SERVER_IP) ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 安装Node.js（如果没有安装）
if ! command -v node &> /dev/null; then
    echo "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    apt-get install -y nodejs
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

APP_DIR="/opt/mcp-all-transports"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# 停止现有服务
echo "停止现有Docker服务..."
for dir in "/opt/mcp-server-chart" "/opt/mcp-prebuilt" "/opt/mcp-npm-solution" "/opt/mcp-multi-transport"; do
    if [ -d "$dir" ]; then
        cd "$dir" 2>/dev/null && docker compose down 2>/dev/null || true
    fi
done
docker stop $(docker ps -aq) 2>/dev/null || true
docker rm $(docker ps -aq) 2>/dev/null || true

cd $APP_DIR

# 全局安装mcp-server-chart（用于stdio）
echo "全局安装mcp-server-chart..."
npm install -g @antv/mcp-server-chart

# 创建渲染服务目录
echo "创建渲染服务..."
mkdir -p render-service
cd render-service

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service with all transports",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0",
    "cors": "^2.8.5"
  }
}
EOF

# 创建渲染服务器代码
cat > server.js << EOF
const express = require('express');
const cors = require('cors');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();

// 配置外部IP
const EXTERNAL_IP = '$SERVER_IP';
const PORT = process.env.PORT || 3000;

console.log(\`配置外部IP地址: \${EXTERNAL_IP}\`);

// 配置CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false
}));

app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

// 生成外部可访问的URL
function getExternalImageUrl(filename) {
  return \`http://\${EXTERNAL_IP}:\${PORT}/images/\${filename}\`;
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = \`chart_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}.png\`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    // 生成外部可访问的图片URL
    const imageUrl = getExternalImageUrl(filename);
    
    console.log('图表生成成功:', filename);
    console.log('外部图片URL:', imageUrl);
    
    // 统一的响应格式，适配所有传输方式
    const response = {
      success: true,
      resultObj: imageUrl,           // 主要字段，返回图片URL
      imageUrl: imageUrl,            // 明确的图片URL字段
      filename: filename,
      size: buffer.length,
      timestamp: new Date().toISOString(),
      externalIP: EXTERNAL_IP,
      transport: 'http',
      // 兼容性：同时提供base64
      base64Url: \`data:image/png;base64,\${buffer.toString('base64')}\`
    };
    
    console.log('返回响应 URL:', response.imageUrl);
    
    res.json(response);
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr',
    externalIP: EXTERNAL_IP,
    port: PORT,
    imageBaseUrl: \`http://\${EXTERNAL_IP}:\${PORT}/images/\`,
    imageCount: fs.readdirSync(imageDir).filter(f => f.endsWith('.png')).length,
    version: '1.0.0-all-transports',
    supportedTransports: ['sse', 'streamable', 'stdio']
  });
});

// 静态文件服务
app.use('/images', express.static(imageDir, {
  maxAge: '1d',
  setHeaders: (res, path) => {
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cache-Control', 'public, max-age=86400');
  }
}));

// 图片列表API
app.get('/api/images', (req, res) => {
  try {
    const files = fs.readdirSync(imageDir)
      .filter(f => f.endsWith('.png'))
      .map(f => {
        const filepath = path.join(imageDir, f);
        const stats = fs.statSync(filepath);
        return {
          filename: f,
          url: getExternalImageUrl(f),
          size: stats.size,
          created: stats.birthtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));
    
    res.json({
      success: true,
      images: files,
      total: files.length,
      externalIP: EXTERNAL_IP,
      baseUrl: \`http://\${EXTERNAL_IP}:\${PORT}/images/\`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(\`GPT-Vis-SSR服务运行在端口 \${PORT}\`);
  console.log(\`外部访问地址: http://\${EXTERNAL_IP}:\${PORT}\`);
  console.log(\`健康检查: http://\${EXTERNAL_IP}:\${PORT}/health\`);
  console.log(\`API端点: http://\${EXTERNAL_IP}:\${PORT}/api/gpt-vis\`);
  console.log(\`图片访问: http://\${EXTERNAL_IP}:\${PORT}/images/\`);
});
EOF

cd $APP_DIR

# 创建Docker Compose配置
echo "创建Docker Compose配置..."
cat > docker-compose.yml << EOF
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
      - EXTERNAL_IP=$SERVER_IP
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        echo "安装系统依赖..."
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++ curl
        echo "安装npm依赖..."
        npm install
        echo "启动渲染服务..."
        npm start
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # SSE传输方式
  mcp-sse-server:
    image: node:18-alpine
    container_name: mcp-sse-server
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..30}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... (\$i/30)"
          sleep 2
        done
        echo "启动MCP SSE服务器..."
        mcp-server-chart --transport sse --port 1122
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

  # Streamable传输方式
  mcp-streamable-server:
    image: node:18-alpine
    container_name: mcp-streamable-server
    working_dir: /app
    ports:
      - "1123:1123"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..30}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... (\$i/30)"
          sleep 2
        done
        echo "启动MCP Streamable服务器..."
        mcp-server-chart --transport streamable --port 1123
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

# 创建图片目录
mkdir -p images

echo "启动Docker服务..."
docker compose up -d

echo "等待服务启动..."
sleep 60

echo "检查服务状态..."
docker compose ps

echo ""
echo "=== 部署完成 ==="
echo ""
echo "🌐 服务地址 (外部IP: $SERVER_IP):"
echo "  - 渲染服务: http://$SERVER_IP:3000"
echo "  - 健康检查: http://$SERVER_IP:3000/health"
echo "  - MCP SSE服务: http://$SERVER_IP:1122/sse"
echo "  - MCP Streamable服务: http://$SERVER_IP:1123/mcp"
echo "  - 图片访问: http://$SERVER_IP:3000/images/"
echo ""
echo "📋 客户端配置请查看: client-configs-complete.md"
