#!/bin/bash

# 设置stdio传输的MCP本地环境
# 用于Claude Desktop、VSCode、Cursor等本地客户端

set -e

echo "=== 设置stdio传输MCP环境 ==="

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "错误：需要先安装Node.js"
    echo "请运行: curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo bash - && sudo apt-get install -y nodejs"
    exit 1
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 全局安装mcp-server-chart
echo "1. 全局安装mcp-server-chart..."
npm install -g @antv/mcp-server-chart

# 验证安装
echo "2. 验证安装..."
if command -v mcp-server-chart &> /dev/null; then
    echo "✅ mcp-server-chart命令行工具安装成功"
    mcp-server-chart --help
else
    echo "❌ 命令行工具安装失败，尝试使用npx"
fi

# 启动本地渲染服务（如果需要）
echo "3. 检查是否需要启动本地渲染服务..."
if curl -s http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 本地渲染服务已运行"
else
    echo "⚠️ 本地渲染服务未运行"
    echo "如果需要使用本地渲染服务，请运行:"
    echo "  sudo ./deploy-multi-transport.sh"
    echo ""
    echo "或者使用默认的在线渲染服务（推荐）"
fi

# 创建配置示例
echo "4. 创建配置示例..."
mkdir -p ~/mcp-configs

# Claude Desktop配置（macOS）
cat > ~/mcp-configs/claude_desktop_config_macos.json << 'EOF'
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "mcp-server-chart",
      "args": ["--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "https://antv-studio.alipay.com/api/gpt-vis"
      }
    }
  }
}
EOF

# Claude Desktop配置（Windows）
cat > ~/mcp-configs/claude_desktop_config_windows.json << 'EOF'
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "cmd",
      "args": ["/c", "npx", "-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "https://antv-studio.alipay.com/api/gpt-vis"
      }
    }
  }
}
EOF

# 本地渲染服务配置
cat > ~/mcp-configs/claude_desktop_config_local.json << 'EOF'
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "mcp-server-chart",
      "args": ["--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://localhost:3000/api/gpt-vis"
      }
    }
  }
}
EOF

# VSCode Cline配置
cat > ~/mcp-configs/vscode_cline_settings.json << 'EOF'
{
  "cline.mcpServers": {
    "mcp-server-chart": {
      "command": "mcp-server-chart",
      "args": ["--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "https://antv-studio.alipay.com/api/gpt-vis"
      }
    }
  }
}
EOF

# Cursor配置
cat > ~/mcp-configs/cursor_settings.json << 'EOF'
{
  "mcp.servers": {
    "mcp-server-chart": {
      "command": "mcp-server-chart",
      "args": ["--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "https://antv-studio.alipay.com/api/gpt-vis"
      }
    }
  }
}
EOF

echo "✅ 配置示例已创建在 ~/mcp-configs/ 目录"

# 测试stdio传输
echo "5. 测试stdio传输..."
echo "测试命令: echo '{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"roots\":{\"listChanged\":true},\"sampling\":{}}}}' | mcp-server-chart --transport stdio"

# 创建测试脚本
cat > ~/mcp-configs/test_stdio.sh << 'EOF'
#!/bin/bash
echo "测试MCP stdio传输..."
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{"roots":{"listChanged":true},"sampling":{}}}}' | timeout 10 mcp-server-chart --transport stdio
EOF

chmod +x ~/mcp-configs/test_stdio.sh

echo "6. 运行stdio测试..."
if ~/mcp-configs/test_stdio.sh 2>/dev/null; then
    echo "✅ stdio传输测试成功"
else
    echo "⚠️ stdio传输测试可能有问题，但这是正常的（需要完整的MCP握手）"
fi

echo ""
echo "=== 安装完成 ==="
echo ""
echo "📁 配置文件位置:"
echo "  配置示例: ~/mcp-configs/"
echo ""
echo "🔧 Claude Desktop配置:"
echo "  macOS: ~/Library/Application Support/Claude/claude_desktop_config.json"
echo "  Windows: %APPDATA%\\Claude\\claude_desktop_config.json"
echo ""
echo "📋 推荐配置步骤:"
echo "1. 复制适合你系统的配置文件内容"
echo "2. 粘贴到Claude Desktop配置文件中"
echo "3. 重启Claude Desktop"
echo "4. 测试图表生成功能"
echo ""
echo "🌐 渲染服务选择:"
echo "  - 在线服务（推荐）: https://antv-studio.alipay.com/api/gpt-vis"
echo "  - 本地服务: http://localhost:3000/api/gpt-vis（需要先启动）"
echo ""
echo "🔍 故障排除:"
echo "  - 检查Node.js版本: node --version"
echo "  - 检查包安装: npm list -g @antv/mcp-server-chart"
echo "  - 测试命令: mcp-server-chart --help"
echo "  - 查看配置示例: ls ~/mcp-configs/"
echo ""
echo "📖 详细配置说明请查看: mcp-client-configs.md"
