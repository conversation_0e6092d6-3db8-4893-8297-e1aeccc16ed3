#!/bin/bash

# 修复APT软件源问题
# 解决bullseye-backports源失效问题

set -e

echo "=== 修复APT软件源脚本 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

echo "备份当前软件源配置..."
cp /etc/apt/sources.list /etc/apt/sources.list.backup.$(date +%Y%m%d_%H%M%S)

echo "检查并修复软件源配置..."

# 检查是否存在失效的backports源
if grep -q "bullseye-backports" /etc/apt/sources.list; then
    echo "发现失效的bullseye-backports源，正在注释..."
    sed -i 's/^deb.*bullseye-backports.*/#&/' /etc/apt/sources.list
fi

# 检查sources.list.d目录中的文件
if [ -d "/etc/apt/sources.list.d" ]; then
    for file in /etc/apt/sources.list.d/*.list; do
        if [ -f "$file" ] && grep -q "bullseye-backports" "$file"; then
            echo "在 $file 中发现失效的bullseye-backports源，正在注释..."
            sed -i 's/^deb.*bullseye-backports.*/#&/' "$file"
        fi
    done
fi

# 创建一个干净的sources.list
echo "创建标准的Debian 11 (bullseye) 软件源配置..."
cat > /etc/apt/sources.list << 'EOF'
# Debian 11 (bullseye) 官方源
deb http://deb.debian.org/debian bullseye main
deb-src http://deb.debian.org/debian bullseye main

deb http://security.debian.org/debian-security bullseye-security main
deb-src http://security.debian.org/debian-security bullseye-security main

deb http://deb.debian.org/debian bullseye-updates main
deb-src http://deb.debian.org/debian bullseye-updates main

# 如果需要backports，可以取消下面的注释
# deb http://deb.debian.org/debian bullseye-backports main
# deb-src http://deb.debian.org/debian bullseye-backports main
EOF

echo "清理APT缓存..."
apt clean
rm -rf /var/lib/apt/lists/*

echo "更新软件包列表..."
apt update

echo "=== 软件源修复完成 ==="
echo "如果您之前使用的是中科大镜像源，可以选择恢复："
echo "1. 保持当前官方源（推荐，稳定性更好）"
echo "2. 恢复中科大镜像源（速度可能更快）"
echo ""
read -p "是否要恢复中科大镜像源？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "配置中科大镜像源..."
    cat > /etc/apt/sources.list << 'EOF'
# 中科大镜像源
deb https://mirrors.ustc.edu.cn/debian/ bullseye main
deb-src https://mirrors.ustc.edu.cn/debian/ bullseye main

deb https://mirrors.ustc.edu.cn/debian-security/ bullseye-security main
deb-src https://mirrors.ustc.edu.cn/debian-security/ bullseye-security main

deb https://mirrors.ustc.edu.cn/debian/ bullseye-updates main
deb-src https://mirrors.ustc.edu.cn/debian/ bullseye-updates main
EOF
    
    echo "更新软件包列表..."
    apt update
    echo "中科大镜像源配置完成"
else
    echo "保持官方源配置"
fi

echo "软件源配置完成！"
