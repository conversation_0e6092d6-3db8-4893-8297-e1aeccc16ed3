#!/bin/bash

# Canvas依赖修复脚本
# 解决node-canvas编译问题

set -e

echo "=== Canvas依赖修复脚本 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

echo "更新包管理器..."
apt update

echo "安装Canvas编译所需的系统依赖..."
apt install -y \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    libpixman-1-dev \
    pkg-config \
    python3-dev \
    build-essential \
    make \
    g++

echo "验证关键依赖是否安装成功..."

# 检查pkg-config
if pkg-config --exists pixman-1; then
    echo "✓ pixman-1 已正确安装"
else
    echo "✗ pixman-1 安装失败"
    exit 1
fi

if pkg-config --exists cairo; then
    echo "✓ cairo 已正确安装"
else
    echo "✗ cairo 安装失败"
    exit 1
fi

if pkg-config --exists pangocairo; then
    echo "✓ pangocairo 已正确安装"
else
    echo "✗ pangocairo 安装失败"
    exit 1
fi

echo "清理npm缓存..."
npm cache clean --force 2>/dev/null || true

# 如果渲染服务目录存在，重新安装依赖
RENDER_DIR="/opt/gpt-vis-ssr"
if [ -d "$RENDER_DIR" ]; then
    echo "重新安装渲染服务依赖..."
    cd $RENDER_DIR
    
    # 删除node_modules和package-lock.json
    rm -rf node_modules package-lock.json
    
    # 重新安装
    npm install
    
    if [ $? -eq 0 ]; then
        echo "✓ 渲染服务依赖安装成功"
    else
        echo "✗ 渲染服务依赖安装失败"
        exit 1
    fi
else
    echo "渲染服务目录不存在，跳过重新安装"
fi

echo "=== Canvas依赖修复完成 ==="
