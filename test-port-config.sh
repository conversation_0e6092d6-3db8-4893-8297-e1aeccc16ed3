#!/bin/bash

# 端口配置测试脚本
# 用于验证不同端口配置下的URL生成是否正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 测试URL生成逻辑
test_url_generation() {
    local server_ip="$1"
    local nginx_port="$2"
    local use_https="$3"
    local expected_url="$4"
    
    log_step "测试配置: IP=$server_ip, Port=$nginx_port, HTTPS=$use_https"
    
    # 模拟URL生成逻辑
    local protocol="http"
    if [[ "$use_https" == "true" ]]; then
        protocol="https"
    fi
    
    local port_suffix=""
    if [[ "$protocol" == "http" && "$nginx_port" != "80" ]] || [[ "$protocol" == "https" && "$nginx_port" != "443" ]]; then
        port_suffix=":${nginx_port}"
    fi
    
    local generated_url="${protocol}://${server_ip}${port_suffix}"
    
    if [[ "$generated_url" == "$expected_url" ]]; then
        log_info "✓ URL生成正确: $generated_url"
        return 0
    else
        log_error "✗ URL生成错误: 期望 $expected_url, 实际 $generated_url"
        return 1
    fi
}

# 测试图表渲染API返回的图片URL
test_image_url_generation() {
    local server_ip="$1"
    local nginx_port="$2"
    local use_https="$3"
    
    log_step "测试图片URL生成..."
    
    # 模拟环境变量
    export SERVER_IP="$server_ip"
    export NGINX_PORT="$nginx_port"
    export USE_HTTPS="$use_https"
    
    # 创建临时测试脚本
    cat > /tmp/test_image_url.js << 'EOF'
const serverIP = process.env.SERVER_IP || '**************'
const serverPort = process.env.NGINX_PORT || '80'
const protocol = process.env.USE_HTTPS === 'true' ? 'https' : 'http'
const filename = 'test-image.png'

// 构建完整的图片URL，包含端口信息
let imageUrl
if ((protocol === 'http' && serverPort === '80') || (protocol === 'https' && serverPort === '443')) {
  // 标准端口不需要显示端口号
  imageUrl = `${protocol}://${serverIP}/images/${filename}`
} else {
  // 非标准端口需要显示端口号
  imageUrl = `${protocol}://${serverIP}:${serverPort}/images/${filename}`
}

console.log(imageUrl)
EOF

    # 运行测试
    if command -v node >/dev/null 2>&1; then
        local result=$(node /tmp/test_image_url.js)
        log_info "生成的图片URL: $result"
        
        # 验证URL格式
        if [[ "$result" =~ ^https?://[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+(:[0-9]+)?/images/test-image\.png$ ]]; then
            log_info "✓ 图片URL格式正确"
        else
            log_error "✗ 图片URL格式错误"
        fi
    else
        log_error "Node.js 未安装，跳过图片URL测试"
    fi
    
    # 清理临时文件
    rm -f /tmp/test_image_url.js
    
    # 清理环境变量
    unset SERVER_IP NGINX_PORT USE_HTTPS
}

# 主测试函数
main() {
    echo "========================================"
    echo "  端口配置测试"
    echo "========================================"
    echo
    
    local test_failed=0
    
    # 测试用例1: HTTP + 标准端口80
    test_url_generation "**************" "80" "false" "http://**************" || ((test_failed++))
    echo
    
    # 测试用例2: HTTPS + 标准端口443
    test_url_generation "**************" "443" "true" "https://**************" || ((test_failed++))
    echo
    
    # 测试用例3: HTTP + 自定义端口8080
    test_url_generation "**************" "8080" "false" "http://**************:8080" || ((test_failed++))
    echo
    
    # 测试用例4: HTTPS + 自定义端口8443
    test_url_generation "**************" "8443" "true" "https://**************:8443" || ((test_failed++))
    echo
    
    # 测试用例5: HTTP + 端口443 (应该显示端口)
    test_url_generation "**************" "443" "false" "http://**************:443" || ((test_failed++))
    echo
    
    # 测试用例6: HTTPS + 端口80 (应该显示端口)
    test_url_generation "**************" "80" "true" "https://**************:80" || ((test_failed++))
    echo
    
    # 测试图片URL生成
    test_image_url_generation "**************" "80" "false"
    echo
    
    test_image_url_generation "**************" "8080" "false"
    echo
    
    test_image_url_generation "**************" "443" "true"
    echo
    
    # 总结
    echo "========================================"
    if [[ $test_failed -eq 0 ]]; then
        log_info "🎉 所有端口配置测试通过！"
    else
        log_error "⚠ 有 $test_failed 个测试失败"
    fi
    echo "========================================"
    
    return $test_failed
}

# 显示帮助信息
show_help() {
    echo "端口配置测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo
    echo "功能:"
    echo "  - 测试不同端口配置下的URL生成逻辑"
    echo "  - 验证标准端口和自定义端口的处理"
    echo "  - 测试HTTP和HTTPS协议的端口显示"
    echo "  - 验证图片URL生成逻辑"
    echo
    echo "测试用例:"
    echo "  1. HTTP + 端口80  → http://IP (不显示端口)"
    echo "  2. HTTPS + 端口443 → https://IP (不显示端口)"
    echo "  3. HTTP + 自定义端口 → http://IP:PORT"
    echo "  4. HTTPS + 自定义端口 → https://IP:PORT"
    echo "  5. 协议端口不匹配时显示端口"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
