# MCP Chart Server 私有化部署方案

这是一个完整的 MCP Chart Server 私有化部署方案，包含 GPT-Vis-SSR 图表渲染服务。

## 项目结构

```
mcp-chart-private/
├── mcp-chart-deploy.sh          # 主部署脚本
├── docker-compose.yml           # Docker Compose 配置
├── nginx.conf                   # Nginx 反向代理配置
├── gpt-vis-ssr/                 # GPT-Vis-SSR 服务
│   ├── package.json
│   ├── index.js
│   └── Dockerfile
├── charts-storage/              # 图片存储目录
└── README.md                    # 说明文档
```

## 快速开始

### 方式一：一键部署（推荐）

```bash
# 下载部署脚本
chmod +x mcp-chart-deploy.sh

# 运行部署脚本
sudo ./mcp-chart-deploy.sh
```

### 方式二：Docker Compose 部署

```bash
# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 方式三：手动部署

```bash
# 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 部署 GPT-Vis-SSR 服务
cd gpt-vis-ssr
npm install
npm start &

# 安装并启动 MCP Server Chart
npm install -g @antv/mcp-server-chart
export VIS_REQUEST_SERVER="http://localhost:3000/api/gpt-vis"
mcp-server-chart --transport sse --port 1122 &
```

## 服务访问

部署完成后，可以通过以下地址访问服务：

- **MCP Server (SSE)**: `http://localhost/sse`
- **MCP Server (Streamable)**: `http://localhost/mcp`
- **GPT-Vis-SSR API**: `http://localhost/api/gpt-vis`
- **健康检查**: `http://localhost/health`
- **图片存储**: `http://localhost/images/`

## 服务管理

### Docker Compose 方式

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart
```

### systemd 方式

```bash
# 查看服务状态
sudo systemctl status gpt-vis-ssr
sudo systemctl status mcp-chart-server

# 重启服务
sudo systemctl restart gpt-vis-ssr
sudo systemctl restart mcp-chart-server

# 查看日志
sudo journalctl -u gpt-vis-ssr -f
sudo journalctl -u mcp-chart-server -f
```

## 测试服务

### 测试 GPT-Vis-SSR 服务

```bash
curl -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "line",
    "data": [
      {"time": "2023-01", "value": 100},
      {"time": "2023-02", "value": 120},
      {"time": "2023-03", "value": 150}
    ]
  }'
```

### 测试 MCP Server

```bash
curl http://localhost:1122/sse
```

## 环境变量配置

| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `VIS_REQUEST_SERVER` | GPT-Vis-SSR 服务地址 | - | `http://localhost:3000/api/gpt-vis` |
| `PORT` | GPT-Vis-SSR 服务端口 | `3000` | `3000` |
| `NODE_ENV` | 运行环境 | `development` | `production` |

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用：`netstat -tlnp | grep :3000`
   - 查看服务日志：`docker-compose logs gpt-vis-ssr`

2. **图表渲染失败**
   - 检查 Canvas 依赖是否安装完整
   - 查看 GPT-Vis-SSR 服务日志

3. **网络连接问题**
   - 检查防火墙设置
   - 确认服务间网络连通性

### 日志查看

```bash
# Docker 方式
docker-compose logs -f gpt-vis-ssr
docker-compose logs -f mcp-server-chart

# systemd 方式
sudo journalctl -u gpt-vis-ssr -f
sudo journalctl -u mcp-chart-server -f
```

## 性能优化

### 图片清理

创建定时任务清理旧图片：

```bash
# 创建清理脚本
cat > cleanup-images.sh << 'EOF'
#!/bin/bash
find ./charts-storage -name "*.png" -mtime +7 -delete
echo "清理完成: $(date)"
EOF

chmod +x cleanup-images.sh

# 添加到 crontab（每天凌晨2点执行）
echo "0 2 * * * /path/to/cleanup-images.sh" | crontab -
```

## 许可证

MIT License
