#!/bin/bash

# MCP Chart Server 服务测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 获取服务器IP
get_server_ip() {
    local server_ip=""

    # 从docker-compose.yml获取配置的IP
    if [[ -f "docker-compose.yml" ]]; then
        server_ip=$(grep "SERVER_IP=" docker-compose.yml 2>/dev/null | cut -d'=' -f2 | head -1)
    fi

    # 如果没有配置，尝试自动检测
    if [[ -z "$server_ip" ]]; then
        if command -v ip >/dev/null 2>&1; then
            server_ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
        fi
    fi

    # 默认使用localhost
    if [[ -z "$server_ip" ]]; then
        server_ip="localhost"
    fi

    echo "$server_ip"
}

# 测试 GPT-Vis-SSR 健康检查
test_gpt_vis_ssr_health() {
    log_step "测试 GPT-Vis-SSR 健康检查..."

    local server_ip=$(get_server_ip)
    local health_url="http://${server_ip}:3000/health"  # 直接访问GPT-Vis-SSR端口

    log_info "测试地址: $health_url"
    local response=$(curl -s -w "%{http_code}" "$health_url" -o /tmp/health_response.json)
    
    if [[ "$response" == "200" ]]; then
        log_info "✓ GPT-Vis-SSR 健康检查通过"
        cat /tmp/health_response.json | jq . 2>/dev/null || cat /tmp/health_response.json
    else
        log_error "✗ GPT-Vis-SSR 健康检查失败 (HTTP $response)"
        return 1
    fi
}

# 测试图表渲染
test_chart_rendering() {
    log_step "测试图表渲染功能..."

    local server_ip=$(get_server_ip)
    local render_url="http://${server_ip}:3000/api/gpt-vis"  # 直接访问GPT-Vis-SSR端口

    local test_data='{
        "type": "line",
        "data": [
            {"time": "2023-01", "value": 100},
            {"time": "2023-02", "value": 120},
            {"time": "2023-03", "value": 150},
            {"time": "2023-04", "value": 130},
            {"time": "2023-05", "value": 180}
        ]
    }'

    log_info "测试地址: $render_url"
    log_info "发送图表渲染请求..."
    local response=$(curl -s -w "%{http_code}" \
        -X POST "$render_url" \
        -H "Content-Type: application/json" \
        -d "$test_data" \
        -o /tmp/chart_response.json)
    
    if [[ "$response" == "200" ]]; then
        local success=$(cat /tmp/chart_response.json | jq -r '.success' 2>/dev/null)
        if [[ "$success" == "true" ]]; then
            log_info "✓ 图表渲染成功"
            local image_url=$(cat /tmp/chart_response.json | jq -r '.resultObj' 2>/dev/null)
            log_info "图片URL: $image_url"
        else
            log_error "✗ 图表渲染失败"
            cat /tmp/chart_response.json
            return 1
        fi
    else
        log_error "✗ 图表渲染请求失败 (HTTP $response)"
        cat /tmp/chart_response.json
        return 1
    fi
}

# 测试 MCP Server
test_mcp_server() {
    log_step "测试 MCP Server..."

    local server_ip=$(get_server_ip)

    # 测试 SSE 端点
    local sse_url="http://${server_ip}/sse"
    log_info "测试 SSE 端点: $sse_url"
    local sse_response=$(timeout 5 curl -s -w "%{http_code}" "$sse_url" -o /tmp/sse_response.txt)

    if [[ "$sse_response" == "200" ]] || [[ "$sse_response" == "101" ]]; then
        log_info "✓ MCP Server SSE 端点响应正常"
    else
        log_warn "⚠ MCP Server SSE 端点响应异常 (HTTP $sse_response)"
    fi

    # 测试 Streamable 端点
    local stream_url="http://${server_ip}/mcp"
    log_info "测试 Streamable 端点: $stream_url"
    local stream_response=$(timeout 5 curl -s -w "%{http_code}" "$stream_url" -o /tmp/stream_response.txt 2>/dev/null || echo "000")

    if [[ "$stream_response" == "200" ]] || [[ "$stream_response" == "101" ]]; then
        log_info "✓ MCP Server Streamable 端点响应正常"
    else
        log_warn "⚠ MCP Server Streamable 端点可能未启用或响应异常"
    fi
}

# 测试 Nginx 代理（如果使用 Docker Compose）
test_nginx_proxy() {
    log_step "测试 Nginx 反向代理..."
    
    # 测试根路径
    local root_response=$(curl -s -w "%{http_code}" http://localhost/ -o /tmp/root_response.txt)
    
    if [[ "$root_response" == "200" ]]; then
        log_info "✓ Nginx 根路径响应正常"
        cat /tmp/root_response.txt
    else
        log_warn "⚠ Nginx 根路径响应异常 (HTTP $root_response)"
    fi
    
    # 测试健康检查代理
    local health_proxy_response=$(curl -s -w "%{http_code}" http://localhost/health -o /tmp/health_proxy_response.json)
    
    if [[ "$health_proxy_response" == "200" ]]; then
        log_info "✓ Nginx 健康检查代理正常"
    else
        log_warn "⚠ Nginx 健康检查代理异常 (HTTP $health_proxy_response)"
    fi
}

# 测试图片访问
test_image_access() {
    log_step "测试图片访问..."
    
    # 首先渲染一个图表获取图片URL
    local test_data='{
        "type": "pie",
        "data": [
            {"category": "A", "value": 30},
            {"category": "B", "value": 25},
            {"category": "C", "value": 45}
        ]
    }'
    
    local response=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
        -H "Content-Type: application/json" \
        -d "$test_data")
    
    local success=$(echo "$response" | jq -r '.success' 2>/dev/null)
    if [[ "$success" == "true" ]]; then
        local image_url=$(echo "$response" | jq -r '.resultObj' 2>/dev/null)
        log_info "获取到图片URL: $image_url"
        
        # 测试图片是否可访问
        local image_response=$(curl -s -w "%{http_code}" "$image_url" -o /tmp/test_image.png)
        
        if [[ "$image_response" == "200" ]]; then
            log_info "✓ 图片访问正常"
            local file_size=$(stat -f%z /tmp/test_image.png 2>/dev/null || stat -c%s /tmp/test_image.png 2>/dev/null)
            log_info "图片大小: $file_size bytes"
        else
            log_error "✗ 图片访问失败 (HTTP $image_response)"
            return 1
        fi
    else
        log_error "✗ 无法获取测试图片"
        return 1
    fi
}

# 性能测试
performance_test() {
    log_step "执行性能测试..."
    
    log_info "测试并发图表渲染..."
    
    local test_data='{
        "type": "column",
        "data": [
            {"category": "Jan", "value": 100},
            {"category": "Feb", "value": 120},
            {"category": "Mar", "value": 150},
            {"category": "Apr", "value": 130},
            {"category": "May", "value": 180}
        ]
    }'
    
    # 并发测试（5个请求）
    local start_time=$(date +%s)
    
    for i in {1..5}; do
        curl -s -X POST http://localhost:3000/api/gpt-vis \
            -H "Content-Type: application/json" \
            -d "$test_data" > /tmp/perf_test_$i.json &
    done
    
    wait
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_info "5个并发请求完成，耗时: ${duration}秒"
    
    # 检查结果
    local success_count=0
    for i in {1..5}; do
        local success=$(cat /tmp/perf_test_$i.json | jq -r '.success' 2>/dev/null)
        if [[ "$success" == "true" ]]; then
            ((success_count++))
        fi
    done
    
    log_info "成功渲染: $success_count/5"
    
    if [[ $success_count -eq 5 ]]; then
        log_info "✓ 性能测试通过"
    else
        log_warn "⚠ 性能测试部分失败"
    fi
}

# 清理测试文件
cleanup() {
    rm -f /tmp/health_response.json
    rm -f /tmp/chart_response.json
    rm -f /tmp/sse_response.txt
    rm -f /tmp/stream_response.txt
    rm -f /tmp/root_response.txt
    rm -f /tmp/health_proxy_response.json
    rm -f /tmp/test_image.png
    rm -f /tmp/perf_test_*.json
}

# 主测试函数
main() {
    echo "========================================"
    echo "  MCP Chart Server 服务测试"
    echo "========================================"
    echo
    
    # 检查必要的工具
    if ! command -v curl >/dev/null 2>&1; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq >/dev/null 2>&1; then
        log_warn "jq 未安装，JSON 解析可能不完整"
    fi
    
    local test_failed=0
    
    # 执行测试
    test_gpt_vis_ssr_health || ((test_failed++))
    echo
    
    test_chart_rendering || ((test_failed++))
    echo
    
    test_mcp_server || ((test_failed++))
    echo
    
    # 如果是 Docker Compose 部署，测试 Nginx
    if docker-compose ps >/dev/null 2>&1; then
        test_nginx_proxy || ((test_failed++))
        echo
    fi
    
    test_image_access || ((test_failed++))
    echo
    
    performance_test || ((test_failed++))
    echo
    
    # 清理
    cleanup
    
    # 总结
    echo "========================================"
    if [[ $test_failed -eq 0 ]]; then
        log_info "🎉 所有测试通过！"
        echo
        log_info "服务运行正常，可以开始使用 MCP Chart Server"
    else
        log_warn "⚠ 有 $test_failed 个测试失败"
        echo
        log_warn "请检查服务配置和日志"
    fi
    echo "========================================"
    
    exit $test_failed
}

# 如果脚本被直接执行，则运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
