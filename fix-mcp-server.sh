#!/bin/bash

# 修复MCP服务器问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "========================================"
echo "  修复MCP服务器"
echo "========================================"
echo

# 1. 检查当前状态
log_step "1. 检查当前状态"
docker-compose ps
echo

# 2. 重新构建MCP服务器镜像
log_step "2. 重新构建MCP服务器镜像"

# 停止服务
docker-compose down

# 删除旧镜像
docker rmi mcp-chart-deploy_mcp-server-chart-sse 2>/dev/null || true
docker rmi mcp-chart-deploy_mcp-server-chart-streamable 2>/dev/null || true

# 重新构建
docker-compose build --no-cache mcp-server-chart-sse
docker-compose build --no-cache mcp-server-chart-streamable

log_info "镜像重新构建完成"
echo

# 3. 启动服务
log_step "3. 启动服务"
docker-compose up -d

# 等待服务启动
log_info "等待服务启动..."
sleep 30

# 4. 检查服务状态
log_step "4. 检查服务状态"
docker-compose ps
echo

# 5. 测试服务
log_step "5. 测试服务"

echo "测试GPT-Vis-SSR..."
if curl -s http://192.168.50.105:3000/health >/dev/null 2>&1; then
    log_info "✓ GPT-Vis-SSR正常"
else
    log_error "✗ GPT-Vis-SSR异常"
fi

echo "测试MCP Streamable..."
if curl -s http://192.168.50.105:1123 >/dev/null 2>&1; then
    log_info "✓ MCP Streamable正常"
else
    log_error "✗ MCP Streamable异常"
fi

echo "测试MCP SSE..."
if curl -s http://192.168.50.105:1122 >/dev/null 2>&1; then
    log_info "✓ MCP SSE正常"
else
    log_error "✗ MCP SSE异常"
fi

echo "测试nginx代理..."
if curl -s http://192.168.50.105/mcp >/dev/null 2>&1; then
    log_info "✓ nginx代理正常"
else
    log_error "✗ nginx代理异常"
fi
echo

# 6. 测试JSON-RPC协议
log_step "6. 测试JSON-RPC协议"

echo "测试initialize方法..."
response=$(curl -s -X POST http://192.168.50.105:1123 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {},
      "clientInfo": {
        "name": "test-client",
        "version": "1.0.0"
      }
    }
  }' 2>/dev/null)

if echo "$response" | jq . >/dev/null 2>&1; then
    log_info "✓ JSON-RPC协议正常"
    echo "响应: $(echo "$response" | jq -c .)"
else
    log_error "✗ JSON-RPC协议异常"
    echo "响应: $response"
fi
echo

# 7. 生成配置建议
log_step "7. 配置建议"

echo "LobeChat配置建议："
echo

# 检查哪种方式可用
if curl -s http://192.168.50.105:1123 >/dev/null 2>&1; then
    echo "✓ 方式一：直接连接（推荐）"
    echo '{'
    echo '  "name": "Chart Generator",'
    echo '  "url": "http://192.168.50.105:1123",'
    echo '  "type": "streamable-http"'
    echo '}'
    echo
fi

if curl -s http://192.168.50.105/mcp >/dev/null 2>&1; then
    echo "✓ 方式二：nginx代理"
    echo '{'
    echo '  "name": "Chart Generator",'
    echo '  "url": "http://192.168.50.105/mcp",'
    echo '  "type": "streamable-http"'
    echo '}'
    echo
fi

echo "Cherry Studio配置（已验证可用）："
echo '{'
echo '  "mcpServers": {'
echo '    "mcp-server-chart": {'
echo '      "command": "npx",'
echo '      "args": ["-y", "@antv/mcp-server-chart"],'
echo '      "env": {'
echo '        "VIS_REQUEST_SERVER": "http://192.168.50.105:3000/api/gpt-vis"'
echo '      }'
echo '    }'
echo '  }'
echo '}'
echo

echo "========================================"
echo "  修复完成"
echo "========================================"
