# MCP Chart Server 私有化部署脚本包

这是一个完整的 MCP Chart Server 私有化部署解决方案，包含 GPT-Vis-SSR 图表渲染服务。

## 📁 项目文件结构

```
mcp-chart-deployment/
├── 🚀 mcp-chart-deploy.sh          # 主部署脚本 (核心)
├── 🐳 docker-compose.yml           # Docker Compose 配置
├── 🌐 nginx.conf                   # Nginx 反向代理配置
├── 📊 gpt-vis-ssr/                 # GPT-Vis-SSR 服务目录
│   ├── package.json                # Node.js 依赖配置
│   ├── index.js                    # 服务主程序
│   └── Dockerfile                  # Docker 镜像构建文件
├── 🛠️ manage-services.sh           # 服务管理脚本
├── 🧪 test-services.sh             # 服务测试脚本
├── 📦 package-deployment.sh        # 打包脚本
├── 🌐 configure-server-ip.sh       # 服务器IP配置脚本 (新增)
├── 🔧 test-port-config.sh          # 端口配置测试脚本 (新增)
├── 📖 MCP-CHART-DEPLOY-README.md   # 详细部署文档
├── 🍒 CHERRY-STUDIO-CONFIG-GUIDE.md # Cherry Studio配置指南 (新增)
├── 📊 PORT-CONFIG-UPDATE-SUMMARY.md # 端口配置更新总结 (新增)
└── 📋 PROJECT-OVERVIEW.md          # 项目总览 (本文件)
```

## 🎯 核心功能

### 1. 多种部署方式
- **Docker Compose 部署** (推荐) - 一键容器化部署
- **systemd 服务部署** - 系统服务管理
- **手动分离部署** - 灵活的手动配置

### 2. 完整的服务栈
- **MCP Server Chart** - 主图表生成服务
- **GPT-Vis-SSR** - 服务端图表渲染
- **Nginx** - 反向代理和负载均衡
- **图片存储** - 自动图片管理和清理

### 3. 运维管理工具
- **自动化部署** - 一键安装配置
- **服务管理** - 启动/停止/重启/监控
- **健康检查** - 自动服务状态检测
- **日志管理** - 集中日志查看和分析

### 4. 局域网支持 🆕
- **智能IP配置** - 自动检测和配置服务器IP
- **端口自适应** - 智能处理标准端口和自定义端口
- **图片URL修复** - 解决局域网图片访问问题
- **多传输协议** - 同时支持SSE和Streamable传输

## 🚀 快速开始

### 方式一：一键部署 (推荐)

```bash
# 1. 下载脚本 (在 Linux 服务器上)
wget https://your-server/mcp-chart-deploy.sh
chmod +x mcp-chart-deploy.sh

# 2. 运行部署
sudo ./mcp-chart-deploy.sh

# 3. 选择部署方式 (推荐选择 1 - Docker Compose)
```

### 方式二：使用部署包

```bash
# 1. 打包 (在开发环境)
./package-deployment.sh

# 2. 上传到服务器
scp mcp-chart-deployment-*.tar.gz user@server:/tmp/

# 3. 解压并部署
tar -xzf mcp-chart-deployment-*.tar.gz
cd mcp-chart-deployment-*
sudo ./quick-start.sh
```

## 📋 部署脚本详解

### 🚀 mcp-chart-deploy.sh (主部署脚本)
- **功能**: 自动化部署 MCP Chart Server 和 GPT-Vis-SSR
- **支持**: 3种部署方式，自动依赖安装，环境检查
- **用法**: `./mcp-chart-deploy.sh -t docker` (Docker部署)

### 🛠️ manage-services.sh (服务管理)
- **功能**: 统一管理所有服务的启动、停止、重启
- **支持**: 自动检测部署方式，日志查看，状态监控
- **用法**: `./manage-services.sh status` (查看状态)

### 🧪 test-services.sh (服务测试)
- **功能**: 全面测试服务功能和性能
- **包含**: 健康检查、图表渲染测试、并发测试
- **用法**: `./test-services.sh` (运行所有测试)

### 📦 package-deployment.sh (打包工具)
- **功能**: 创建完整的部署包
- **包含**: 所有脚本、配置文件、文档、示例
- **用法**: `./package-deployment.sh` (生成部署包)

## 🌐 服务访问地址

部署完成后，可通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| MCP Server (SSE) | `http://**************/sse` | 主要 MCP 服务端点 |
| MCP Server (Stream) | `http://**************/mcp` | 流式传输端点 🆕 |
| GPT-Vis-SSR API | `http://**************:3000/api/gpt-vis` | 图表渲染 API |
| 健康检查 | `http://**************:3000/health` | 服务健康状态 |
| 图片存储 | `http://**************:3000/images/` | 生成的图片访问 ✅ |

> 💡 **注意**:
> - MCP服务通过Nginx代理访问 (端口80/443)
> - GPT-Vis-SSR服务直接访问 (端口3000)
> - 图片URL格式: `http://**************:3000/images/xxx.png`

## 🔧 配置说明

### 环境变量
```bash
VIS_REQUEST_SERVER=http://localhost:3000/api/gpt-vis  # GPT-Vis-SSR 服务地址
SERVICE_ID=your-service-id                            # 服务标识符 (可选)
DISABLED_TOOLS=generate_fishbone_diagram              # 禁用的工具 (可选)
```

### 端口配置
- **3000**: GPT-Vis-SSR 服务
- **1122**: MCP Server Chart (SSE)
- **1123**: MCP Server Chart (Streamable) 🆕
- **80/443**: Nginx 反向代理

## 🍒 Cherry Studio 配置

部署完成后，系统会自动生成 Cherry Studio 配置文件：

```bash
configs/
├── cherry-studio-sse-config.json        # SSE传输配置 (推荐)
├── cherry-studio-streamable-config.json # Streamable传输配置
└── cherry-studio-http-config.json       # HTTP配置
```

### 快速配置步骤

1. **获取配置文件**
   ```bash
   # 配置文件会自动生成在 configs/ 目录
   ls configs/cherry-studio-*.json
   ```

2. **在Cherry Studio中导入**
   - 打开Cherry Studio设置
   - 找到MCP服务器配置
   - 导入对应的JSON配置文件

3. **验证连接**
   ```bash
   # 测试服务连接
   ./test-services.sh
   ```

详细配置指南请参考: `CHERRY-STUDIO-CONFIG-GUIDE.md`

## 🔧 端口配置说明

### 智能端口处理 🆕

系统会根据协议和端口自动决定URL格式：

| 协议 | 端口 | 生成的URL | 说明 |
|------|------|-----------|------|
| HTTP | 80 | `http://**************` | 标准端口，不显示端口号 |
| HTTPS | 443 | `https://**************` | 标准端口，不显示端口号 |
| HTTP | 8080 | `http://**************:8080` | 自定义端口，显示端口号 |
| HTTPS | 8443 | `https://**************:8443` | 自定义端口，显示端口号 |

### 图片URL问题解决 ✅

**问题**: 之前返回 `http://gpt-vis-ssr:3000/images/xxx.png` (局域网无法访问)
**解决**: 现在返回 `http://**************:3000/images/xxx.png` (局域网可正常访问)

### 端口配置工具

```bash
# 自动检测并配置IP
./configure-server-ip.sh

# 指定IP和端口
./configure-server-ip.sh --ip ************** --port 8080

# 启用HTTPS
./configure-server-ip.sh --ip ************** --https

# 测试端口配置
./test-port-config.sh
```

## 🛡️ 安全建议

1. **网络安全**
   - 配置防火墙规则
   - 使用 HTTPS (SSL 证书)
   - 限制访问 IP 范围

2. **服务安全**
   - 使用非 root 用户运行
   - 定期更新依赖包
   - 配置日志轮转

3. **数据安全**
   - 定期备份图片数据
   - 设置合理的文件权限
   - 监控磁盘使用情况

## 📊 监控和维护

### 服务监控
```bash
# 实时监控服务状态
./manage-services.sh monitor

# 查看服务日志
./manage-services.sh logs

# 检查服务健康状态
./test-services.sh
```

### 数据维护
```bash
# 清理 7 天前的图片
./manage-services.sh cleanup 7

# 备份配置和数据
./manage-services.sh backup
```

## 🔍 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :3000
   
   # 查看详细日志
   ./manage-services.sh logs
   ```

2. **图表渲染失败**
   ```bash
   # 检查 GPT-Vis-SSR 服务
   curl http://localhost:3000/health
   
   # 测试渲染功能
   ./test-services.sh
   ```

3. **网络连接问题**
   ```bash
   # 检查防火墙
   sudo ufw status
   
   # 检查服务间连通性
   curl http://localhost:1122/sse
   ```

### 日志位置

| 部署方式 | 日志位置 |
|----------|----------|
| Docker Compose | `docker-compose logs -f` |
| systemd | `journalctl -u gpt-vis-ssr -f` |
| 手动部署 | `logs/gpt-vis-ssr.log` |

## 🎨 支持的图表类型

MCP Server Chart 支持 25+ 种图表类型：

- 📈 **基础图表**: 折线图、柱状图、饼图、散点图
- 📊 **统计图表**: 箱线图、小提琴图、直方图
- 🗺️ **地理图表**: 地区分布图、路径图、标点图
- 🔄 **流程图表**: 流程图、思维导图、组织架构图
- 🎯 **专业图表**: 桑基图、漏斗图、雷达图、词云图

## 📈 性能优化

### 图片存储优化
- 自动清理过期图片
- 支持 CDN 集成
- 图片压缩和缓存

### 服务性能优化
- Nginx 负载均衡
- 连接池配置
- 内存使用优化

## 🤝 技术支持

### 获取帮助
- 查看详细文档: `MCP-CHART-DEPLOY-README.md`
- 运行测试诊断: `./test-services.sh`
- 查看服务状态: `./manage-services.sh status`

### 社区支持
- GitHub Issues: 提交问题和建议
- 技术文档: 查看最新文档
- 示例代码: 参考部署示例

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

**🎉 现在您已经拥有了一个完整的 MCP Chart Server 私有化部署解决方案！**

开始使用：`sudo ./mcp-chart-deploy.sh`
