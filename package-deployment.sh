#!/bin/bash

# MCP Chart Server 部署包打包脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 创建部署包
create_deployment_package() {
    local package_name="mcp-chart-deployment-$(date +%Y%m%d_%H%M%S)"
    local package_dir="$package_name"
    
    log_step "创建部署包: $package_name"
    
    # 创建目录结构
    mkdir -p "$package_dir"/{gpt-vis-ssr,scripts,docs,examples}
    
    # 复制主要文件
    log_info "复制部署文件..."
    
    # 主部署脚本
    if [[ -f "mcp-chart-deploy.sh" ]]; then
        cp mcp-chart-deploy.sh "$package_dir/"
        chmod +x "$package_dir/mcp-chart-deploy.sh"
    fi
    
    # Docker 配置文件
    if [[ -f "docker-compose.yml" ]]; then
        cp docker-compose.yml "$package_dir/"
    fi
    
    if [[ -f "nginx.conf" ]]; then
        cp nginx.conf "$package_dir/"
    fi
    
    # GPT-Vis-SSR 服务文件
    if [[ -d "gpt-vis-ssr" ]]; then
        cp gpt-vis-ssr/* "$package_dir/gpt-vis-ssr/" 2>/dev/null || true
    fi
    
    # 管理脚本
    if [[ -f "manage-services.sh" ]]; then
        cp manage-services.sh "$package_dir/scripts/"
        chmod +x "$package_dir/scripts/manage-services.sh"
    fi
    
    if [[ -f "test-services.sh" ]]; then
        cp test-services.sh "$package_dir/scripts/"
        chmod +x "$package_dir/scripts/test-services.sh"
    fi
    
    # 文档
    if [[ -f "MCP-CHART-DEPLOY-README.md" ]]; then
        cp MCP-CHART-DEPLOY-README.md "$package_dir/docs/README.md"
    fi
    
    # 创建快速开始脚本
    create_quick_start_script "$package_dir"
    
    # 创建示例配置
    create_example_configs "$package_dir"
    
    # 创建安装说明
    create_install_instructions "$package_dir"
    
    # 打包
    log_info "打包文件..."
    tar -czf "${package_name}.tar.gz" "$package_dir"
    
    # 清理临时目录
    rm -rf "$package_dir"
    
    log_info "部署包创建完成: ${package_name}.tar.gz"
    
    # 显示包信息
    local size=$(ls -lh "${package_name}.tar.gz" | awk '{print $5}')
    log_info "包大小: $size"
    
    echo
    log_info "使用方法:"
    echo "  1. 上传到目标服务器: scp ${package_name}.tar.gz user@server:/tmp/"
    echo "  2. 解压: tar -xzf ${package_name}.tar.gz"
    echo "  3. 进入目录: cd $package_name"
    echo "  4. 运行安装: sudo ./quick-start.sh"
}

# 创建快速开始脚本
create_quick_start_script() {
    local package_dir="$1"
    
    cat > "$package_dir/quick-start.sh" << 'EOF'
#!/bin/bash

# MCP Chart Server 快速开始脚本

set -e

echo "========================================"
echo "  MCP Chart Server 快速部署"
echo "========================================"
echo

# 检查权限
if [[ $EUID -ne 0 ]] && ! command -v sudo >/dev/null 2>&1; then
    echo "错误: 需要 root 权限或 sudo 权限"
    exit 1
fi

# 选择部署方式
echo "请选择部署方式:"
echo "1) Docker Compose 部署 (推荐)"
echo "2) systemd 服务部署"
echo "3) 手动部署"
echo

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "使用 Docker Compose 部署..."
        ./mcp-chart-deploy.sh -t docker
        ;;
    2)
        echo "使用 systemd 服务部署..."
        ./mcp-chart-deploy.sh -t systemd
        ;;
    3)
        echo "使用手动部署..."
        ./mcp-chart-deploy.sh -t separate
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo
echo "部署完成！"
echo
echo "管理命令:"
echo "  ./scripts/manage-services.sh status    # 查看服务状态"
echo "  ./scripts/manage-services.sh logs      # 查看日志"
echo "  ./scripts/test-services.sh             # 测试服务"
echo
EOF

    chmod +x "$package_dir/quick-start.sh"
}

# 创建示例配置
create_example_configs() {
    local package_dir="$1"
    
    # 创建环境变量示例
    cat > "$package_dir/examples/env-example" << 'EOF'
# MCP Chart Server 环境变量配置示例

# GPT-Vis-SSR 服务配置
VIS_REQUEST_SERVER=http://localhost:3000/api/gpt-vis
PORT=3000
NODE_ENV=production

# MCP Server 配置
MCP_PORT=1122
MCP_TRANSPORT=sse

# 可选配置
SERVICE_ID=your-service-id-here
DISABLED_TOOLS=generate_fishbone_diagram,generate_mind_map

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/mcp-chart/app.log
EOF

    # 创建 systemd 服务示例
    cat > "$package_dir/examples/systemd-service-example" << 'EOF'
# GPT-Vis-SSR systemd 服务配置示例
# 文件位置: /etc/systemd/system/gpt-vis-ssr.service

[Unit]
Description=GPT-Vis-SSR Rendering Service
After=network.target

[Service]
Type=simple
User=mcp-chart
Group=mcp-chart
WorkingDirectory=/opt/mcp-chart-service/gpt-vis-ssr
Environment=NODE_ENV=production
Environment=PORT=3000
ExecStart=/usr/bin/node index.js
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # 创建 Nginx 配置示例
    cat > "$package_dir/examples/nginx-ssl-example.conf" << 'EOF'
# Nginx SSL 配置示例

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # MCP Server Chart 服务
    location /sse {
        proxy_pass http://localhost:1122/sse;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # SSE特定配置
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 24h;
    }

    # GPT-Vis-SSR 服务
    location /api/gpt-vis {
        proxy_pass http://localhost:3000/api/gpt-vis;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 图片静态文件服务
    location /images/ {
        alias /path/to/charts-storage/;
        expires 7d;
        add_header Cache-Control "public, immutable";
    }
}
EOF
}

# 创建安装说明
create_install_instructions() {
    local package_dir="$1"
    
    cat > "$package_dir/INSTALL.md" << 'EOF'
# MCP Chart Server 安装说明

## 系统要求

- Linux 操作系统 (Ubuntu 18.04+, CentOS 7+, Debian 9+)
- 2GB+ 内存
- 10GB+ 磁盘空间
- 网络连接

## 快速安装

```bash
# 1. 解压部署包
tar -xzf mcp-chart-deployment-*.tar.gz
cd mcp-chart-deployment-*

# 2. 运行快速安装脚本
sudo ./quick-start.sh
```

## 手动安装步骤

### 1. 安装依赖

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y curl wget git build-essential

# CentOS/RHEL
sudo yum update
sudo yum install -y curl wget git gcc gcc-c++ make
```

### 2. 选择部署方式

#### Docker Compose 部署 (推荐)

```bash
# 安装 Docker
curl -fsSL https://get.docker.com | sudo sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动服务
docker-compose up -d
```

#### systemd 服务部署

```bash
# 运行部署脚本
sudo ./mcp-chart-deploy.sh -t systemd
```

#### 手动部署

```bash
# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 运行部署脚本
./mcp-chart-deploy.sh -t separate
```

### 3. 验证安装

```bash
# 测试服务
./scripts/test-services.sh

# 查看服务状态
./scripts/manage-services.sh status
```

## 配置说明

### 环境变量

参考 `examples/env-example` 文件配置环境变量。

### SSL 配置

参考 `examples/nginx-ssl-example.conf` 配置 HTTPS。

### 防火墙配置

```bash
# Ubuntu/Debian
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 1122
sudo ufw allow 3000

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=1122/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo netstat -tlnp | grep :3000
   sudo lsof -i :3000
   ```

2. **权限问题**
   ```bash
   sudo chown -R $USER:$USER /path/to/project
   ```

3. **依赖安装失败**
   ```bash
   # 清理 npm 缓存
   npm cache clean --force
   
   # 重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

### 日志查看

```bash
# Docker 方式
docker-compose logs -f

# systemd 方式
sudo journalctl -u gpt-vis-ssr -f
sudo journalctl -u mcp-chart-server -f

# 手动方式
tail -f logs/gpt-vis-ssr.log
tail -f logs/mcp-chart.log
```

## 维护

### 定期清理

```bash
# 清理旧图片
./scripts/manage-services.sh cleanup 7

# 备份数据
./scripts/manage-services.sh backup
```

### 更新服务

```bash
# Docker 方式
docker-compose pull
docker-compose up -d

# 其他方式
npm update -g @antv/mcp-server-chart
```

## 支持

如有问题，请查看日志文件或联系技术支持。
EOF
}

# 主函数
main() {
    echo "========================================"
    echo "  MCP Chart Server 部署包打包工具"
    echo "========================================"
    echo
    
    # 检查必要文件
    local required_files=("mcp-chart-deploy.sh" "docker-compose.yml" "nginx.conf")
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "缺少必要文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    # 创建部署包
    create_deployment_package
    
    echo
    log_info "打包完成！"
}

# 如果脚本被直接执行，则运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
