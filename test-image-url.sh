#!/bin/bash

# 测试图片URL功能
# 验证MCP服务是否正确返回图片URL

echo "=== 测试图片URL功能 ==="

# 检查服务状态
echo "1. 检查服务状态..."
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ 渲染服务未运行"
    echo "请先运行: sudo ./fix-image-response.sh"
    exit 1
fi

echo "✅ 渲染服务正在运行"

# 测试图表生成
echo ""
echo "2. 测试图表生成..."

# 生成饼图
echo "生成饼图..."
PIE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "Chrome", "value": 45},
      {"category": "Firefox", "value": 25},
      {"category": "Safari", "value": 20},
      {"category": "Edge", "value": 10}
    ],
    "title": "浏览器市场份额"
  }')

echo "饼图响应:"
echo "$PIE_RESPONSE" | jq . 2>/dev/null || echo "$PIE_RESPONSE"

# 检查响应格式
if echo "$PIE_RESPONSE" | grep -q '"imageUrl"'; then
    echo "✅ 返回了imageUrl字段"
    PIE_URL=$(echo "$PIE_RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    echo "图片URL: $PIE_URL"
    
    # 测试URL访问
    if curl -s --head "$PIE_URL" | grep -q "200 OK"; then
        echo "✅ 图片URL可访问"
    else
        echo "❌ 图片URL无法访问"
    fi
else
    echo "❌ 未返回imageUrl字段"
fi

# 生成柱状图
echo ""
echo "生成柱状图..."
BAR_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "column",
    "data": [
      {"category": "一月", "value": 120},
      {"category": "二月", "value": 150},
      {"category": "三月", "value": 180},
      {"category": "四月", "value": 200}
    ],
    "title": "月度销售额"
  }')

echo "柱状图响应:"
echo "$BAR_RESPONSE" | jq . 2>/dev/null || echo "$BAR_RESPONSE"

if echo "$BAR_RESPONSE" | grep -q '"imageUrl"'; then
    echo "✅ 返回了imageUrl字段"
    BAR_URL=$(echo "$BAR_RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    echo "图片URL: $BAR_URL"
else
    echo "❌ 未返回imageUrl字段"
fi

# 生成折线图
echo ""
echo "生成折线图..."
LINE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "line",
    "data": [
      {"time": "2024-01", "value": 100},
      {"time": "2024-02", "value": 120},
      {"time": "2024-03", "value": 150},
      {"time": "2024-04", "value": 180}
    ],
    "title": "增长趋势"
  }')

echo "折线图响应:"
echo "$LINE_RESPONSE" | jq . 2>/dev/null || echo "$LINE_RESPONSE"

if echo "$LINE_RESPONSE" | grep -q '"imageUrl"'; then
    echo "✅ 返回了imageUrl字段"
    LINE_URL=$(echo "$LINE_RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    echo "图片URL: $LINE_URL"
else
    echo "❌ 未返回imageUrl字段"
fi

# 测试图片列表API
echo ""
echo "3. 测试图片列表API..."
IMAGES_RESPONSE=$(curl -s http://localhost:3000/api/images)
echo "图片列表:"
echo "$IMAGES_RESPONSE" | jq . 2>/dev/null || echo "$IMAGES_RESPONSE"

# 检查图片目录
echo ""
echo "4. 检查图片目录..."
echo "图片文件列表:"
ls -la images/*.png 2>/dev/null | head -10 || echo "没有找到图片文件"

# 测试静态文件访问
echo ""
echo "5. 测试静态文件访问..."
FIRST_IMAGE=$(ls images/*.png 2>/dev/null | head -1)
if [ -n "$FIRST_IMAGE" ]; then
    FILENAME=$(basename "$FIRST_IMAGE")
    IMAGE_URL="http://localhost:3000/images/$FILENAME"
    echo "测试图片URL: $IMAGE_URL"
    
    if curl -s --head "$IMAGE_URL" | grep -q "200 OK"; then
        echo "✅ 静态文件访问正常"
        # 获取文件大小
        SIZE=$(curl -s --head "$IMAGE_URL" | grep -i content-length | cut -d' ' -f2 | tr -d '\r')
        echo "文件大小: $SIZE bytes"
    else
        echo "❌ 静态文件访问失败"
    fi
else
    echo "⚠️ 没有图片文件可测试"
fi

# 测试CORS
echo ""
echo "6. 测试CORS设置..."
CORS_RESPONSE=$(curl -s -H "Origin: http://localhost:8080" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type" \
  -X OPTIONS http://localhost:3000/api/gpt-vis)

if curl -s -H "Origin: http://localhost:8080" http://localhost:3000/health | grep -q "ok"; then
    echo "✅ CORS设置正常"
else
    echo "❌ CORS设置可能有问题"
fi

# 测试MCP服务连接
echo ""
echo "7. 测试MCP服务连接..."
if curl -s http://localhost:1122/sse > /dev/null; then
    echo "✅ MCP SSE服务正常"
else
    echo "❌ MCP SSE服务异常"
fi

# 生成测试报告
echo ""
echo "=== 测试报告 ==="

# 统计生成的图片
IMAGE_COUNT=$(ls images/*.png 2>/dev/null | wc -l)
echo "📊 生成的图片数量: $IMAGE_COUNT"

# 检查响应格式
echo "📋 响应格式检查:"
if echo "$PIE_RESPONSE" | grep -q '"resultObj".*http'; then
    echo "  ✅ resultObj 包含URL"
else
    echo "  ❌ resultObj 不包含URL"
fi

if echo "$PIE_RESPONSE" | grep -q '"imageUrl".*http'; then
    echo "  ✅ imageUrl 字段正确"
else
    echo "  ❌ imageUrl 字段错误"
fi

if echo "$PIE_RESPONSE" | grep -q '"base64Url".*data:image'; then
    echo "  ✅ base64Url 字段正确（兼容性）"
else
    echo "  ❌ base64Url 字段错误"
fi

echo ""
echo "🎯 Cherry Studio使用建议:"
echo "1. 确保Cherry Studio可以访问图片URL"
echo "2. 如果是远程服务器，检查防火墙设置"
echo "3. 图片URL格式: http://localhost:3000/images/{filename}"
echo "4. 如果仍显示base64，检查Cherry Studio的图片显示设置"

echo ""
echo "🔧 如果问题仍然存在:"
echo "1. 重启MCP服务: docker compose restart"
echo "2. 检查Cherry Studio日志"
echo "3. 确认网络连接: ping localhost"
echo "4. 测试图片直接访问: 在浏览器中打开图片URL"

# 创建测试HTML页面
echo ""
echo "8. 创建测试页面..."
cat > images/test.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>MCP图表测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart { margin: 20px 0; border: 1px solid #ddd; padding: 10px; }
        img { max-width: 100%; height: auto; }
    </style>
</head>
<body>
    <h1>MCP图表生成测试</h1>
    <div class="chart">
        <h3>最新生成的图片:</h3>
EOF

# 添加最新的图片到HTML
ls images/*.png 2>/dev/null | head -5 | while read img; do
    filename=$(basename "$img")
    echo "        <div><img src=\"$filename\" alt=\"$filename\"><br><small>$filename</small></div>" >> images/test.html
done

cat >> images/test.html << EOF
    </div>
    <p>访问地址: <a href="http://localhost:3000/images/test.html">http://localhost:3000/images/test.html</a></p>
</body>
</html>
EOF

echo "✅ 测试页面已创建: http://localhost:3000/images/test.html"
echo "   在浏览器中打开此页面查看生成的图片"
