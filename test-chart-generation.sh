#!/bin/bash

# 测试图表生成功能
# 验证各种图表类型是否正常工作

echo "=== 测试图表生成功能 ==="

# 测试渲染服务
echo "1. 测试渲染服务健康状态..."
HEALTH_RESPONSE=$(curl -s http://localhost:3000/health)
if echo "$HEALTH_RESPONSE" | grep -q '"status":"ok"'; then
    echo "✅ 渲染服务正常"
else
    echo "❌ 渲染服务异常"
    exit 1
fi

# 测试饼图生成
echo ""
echo "2. 测试饼图生成..."
PIE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "苹果", "value": 30},
      {"category": "香蕉", "value": 25},
      {"category": "橙子", "value": 20},
      {"category": "葡萄", "value": 15},
      {"category": "其他", "value": 10}
    ],
    "title": "水果销量分布"
  }')

if echo "$PIE_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 饼图生成成功"
    IMAGE_SIZE=$(echo "$PIE_RESPONSE" | grep -o '"resultObj":"[^"]*"' | wc -c)
    echo "   图片大小: $IMAGE_SIZE 字符"
else
    echo "❌ 饼图生成失败"
    echo "   响应: $PIE_RESPONSE"
fi

# 测试柱状图生成
echo ""
echo "3. 测试柱状图生成..."
COLUMN_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "column",
    "data": [
      {"category": "一月", "value": 100},
      {"category": "二月", "value": 120},
      {"category": "三月", "value": 90},
      {"category": "四月", "value": 150},
      {"category": "五月", "value": 130}
    ],
    "title": "月度销售额"
  }')

if echo "$COLUMN_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 柱状图生成成功"
else
    echo "❌ 柱状图生成失败"
fi

# 测试折线图生成
echo ""
echo "4. 测试折线图生成..."
LINE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "line",
    "data": [
      {"time": "2024-01", "value": 100},
      {"time": "2024-02", "value": 120},
      {"time": "2024-03", "value": 90},
      {"time": "2024-04", "value": 150},
      {"time": "2024-05", "value": 130}
    ],
    "title": "趋势分析"
  }')

if echo "$LINE_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 折线图生成成功"
else
    echo "❌ 折线图生成失败"
fi

# 测试散点图生成
echo ""
echo "5. 测试散点图生成..."
SCATTER_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "scatter",
    "data": [
      {"x": 10, "y": 15},
      {"x": 20, "y": 25},
      {"x": 30, "y": 35},
      {"x": 40, "y": 28},
      {"x": 50, "y": 42}
    ],
    "title": "相关性分析"
  }')

if echo "$SCATTER_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 散点图生成成功"
else
    echo "❌ 散点图生成失败"
fi

# 测试MCP服务连接
echo ""
echo "6. 测试MCP服务连接..."
if curl -s --connect-timeout 5 http://localhost:1122/sse > /dev/null; then
    echo "✅ MCP SSE服务连接正常"
else
    echo "❌ MCP SSE服务连接失败"
fi

if curl -s --connect-timeout 5 http://localhost:1123/mcp > /dev/null; then
    echo "✅ MCP Streamable服务连接正常"
else
    echo "❌ MCP Streamable服务连接失败"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "🎉 恭喜！图表生成功能测试成功！"
echo ""
echo "Cherry Studio配置信息："
echo "  SSE连接: http://localhost:1122/sse"
echo "  Streamable连接: http://localhost:1123/mcp"
echo ""
echo "支持的图表类型："
echo "  - 饼图 (pie)"
echo "  - 柱状图 (column)"
echo "  - 折线图 (line)"
echo "  - 散点图 (scatter)"
echo "  - 面积图 (area)"
echo "  - 条形图 (bar)"
echo "  - 雷达图 (radar)"
echo "  - 漏斗图 (funnel)"
echo "  - 箱线图 (boxplot)"
echo "  - 小提琴图 (violin)"
echo "  - 直方图 (histogram)"
echo "  - 热力图 (heatmap)"
echo "  - 树状图 (treemap)"
echo "  - 桑基图 (sankey)"
echo "  - 词云图 (word_cloud)"
echo "  - 网络图 (network_graph)"
echo "  - 组织架构图 (organization_chart)"
echo "  - 思维导图 (mind_map)"
echo "  - 流程图 (flow_diagram)"
echo "  - 鱼骨图 (fishbone_diagram)"
echo "  - 韦恩图 (venn_chart)"
echo "  - 液体填充图 (liquid_chart)"
echo "  - 双轴图 (dual_axes_chart)"
echo "  - 地图相关图表 (district_map, pin_map, path_map)"
echo ""
echo "现在你可以在Cherry Studio中使用这些图表功能了！"
