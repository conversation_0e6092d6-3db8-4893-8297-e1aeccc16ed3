#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import openpyxl

def check_prediction():
    """检查生成的Excel文件中的预测分析内容"""
    
    # 读取最新生成的文件
    result_file = "2025年省抽任务分析结果_20250801_111208.xlsx"
    
    try:
        wb = openpyxl.load_workbook(result_file)
        ws = wb['汇总统计']
        
        print("=== 汇总统计sheet预测分析检查 ===")
        print(f"总行数: {ws.max_row}")
        print(f"总列数: {ws.max_column}")
        print(f"图表数量: {len(ws._charts)}")
        
        print("\n=== 完成时间预测分析 ===")
        # 查找预测分析标题
        prediction_start_row = None
        for row in range(1, ws.max_row + 1):
            cell_value = ws.cell(row=row, column=1).value
            if cell_value and "完成时间预测分析" in str(cell_value):
                prediction_start_row = row
                break
        
        if prediction_start_row:
            print(f"预测分析开始行: {prediction_start_row}")
            
            # 显示预测逻辑说明
            logic_row = prediction_start_row + 2
            logic_title = ws.cell(row=logic_row, column=1).value
            logic_content = ws.cell(row=logic_row + 1, column=1).value
            
            if logic_title:
                print(f"\n{logic_title}")
            if logic_content:
                print(f"{logic_content}")
            
            # 显示预测数据
            print(f"\n预测分析结果:")
            for row in range(logic_row + 3, logic_row + 9):
                indicator = ws.cell(row=row, column=1).value
                value = ws.cell(row=row, column=2).value
                if indicator and value:
                    print(f"  {indicator}: {value}")
        else:
            print("未找到预测分析内容")
        
        print("\n=== 图表信息 ===")
        for i, chart in enumerate(ws._charts):
            print(f"图表 {i+1}: {type(chart).__name__}")
            print(f"  标题: 任务批数累计完成趋势")
            print(f"  位置: A列第22行")
        
        print("\n=== 检查完成 ===")
        
    except FileNotFoundError:
        print(f"文件 {result_file} 不存在")
    except Exception as e:
        print(f"检查过程中出现错误: {str(e)}")

if __name__ == "__main__":
    check_prediction()
