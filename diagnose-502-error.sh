#!/bin/bash

# 502错误诊断脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "========================================"
echo "  502错误诊断"
echo "========================================"
echo

# 1. 检查容器状态
log_step "1. 检查容器状态"
docker-compose ps
echo

# 2. 检查MCP服务器容器是否在运行
log_step "2. 检查MCP服务器容器"
if docker-compose ps | grep -q "mcp-server-chart-streamable.*Up"; then
    log_info "✓ mcp-server-chart-streamable 容器正在运行"
else
    log_error "✗ mcp-server-chart-streamable 容器未运行"
fi

if docker-compose ps | grep -q "mcp-server-chart-sse.*Up"; then
    log_info "✓ mcp-server-chart-sse 容器正在运行"
else
    log_error "✗ mcp-server-chart-sse 容器未运行"
fi
echo

# 3. 检查容器日志
log_step "3. 检查容器日志"
echo "=== MCP Streamable 日志 ==="
docker-compose logs --tail=10 mcp-server-chart-streamable
echo
echo "=== MCP SSE 日志 ==="
docker-compose logs --tail=10 mcp-server-chart-sse
echo
echo "=== Nginx 日志 ==="
docker-compose logs --tail=10 nginx
echo

# 4. 检查网络连通性
log_step "4. 检查容器间网络连通性"
if docker exec nginx-proxy ping -c 1 mcp-server-chart-streamable >/dev/null 2>&1; then
    log_info "✓ nginx可以ping通mcp-server-chart-streamable"
else
    log_error "✗ nginx无法ping通mcp-server-chart-streamable"
fi

if docker exec nginx-proxy ping -c 1 mcp-server-chart-sse >/dev/null 2>&1; then
    log_info "✓ nginx可以ping通mcp-server-chart-sse"
else
    log_error "✗ nginx无法ping通mcp-server-chart-sse"
fi
echo

# 5. 检查端口监听
log_step "5. 检查端口监听"
echo "=== MCP Streamable 端口 ==="
docker exec mcp-server-chart-streamable netstat -tlnp 2>/dev/null || echo "netstat不可用"
echo
echo "=== MCP SSE 端口 ==="
docker exec mcp-server-chart-sse netstat -tlnp 2>/dev/null || echo "netstat不可用"
echo

# 6. 直接测试MCP服务器
log_step "6. 直接测试MCP服务器"
echo "测试Streamable服务器..."
if docker exec mcp-server-chart-streamable curl -s http://localhost:1123 >/dev/null 2>&1; then
    log_info "✓ Streamable服务器响应正常"
else
    log_error "✗ Streamable服务器无响应"
fi

echo "测试SSE服务器..."
if docker exec mcp-server-chart-sse curl -s http://localhost:1122 >/dev/null 2>&1; then
    log_info "✓ SSE服务器响应正常"
else
    log_error "✗ SSE服务器无响应"
fi
echo

# 7. 测试从nginx到MCP的连接
log_step "7. 测试从nginx到MCP的连接"
echo "测试nginx到Streamable..."
if docker exec nginx-proxy curl -s http://mcp-server-chart-streamable:1123 >/dev/null 2>&1; then
    log_info "✓ nginx可以连接到Streamable服务"
else
    log_error "✗ nginx无法连接到Streamable服务"
fi

echo "测试nginx到SSE..."
if docker exec nginx-proxy curl -s http://mcp-server-chart-sse:1122 >/dev/null 2>&1; then
    log_info "✓ nginx可以连接到SSE服务"
else
    log_error "✗ nginx无法连接到SSE服务"
fi
echo

# 8. 检查nginx配置
log_step "8. 检查nginx配置"
echo "检查nginx配置语法..."
if docker exec nginx-proxy nginx -t >/dev/null 2>&1; then
    log_info "✓ nginx配置语法正确"
else
    log_error "✗ nginx配置语法错误"
    docker exec nginx-proxy nginx -t
fi
echo

# 9. 建议的修复步骤
log_step "9. 建议的修复步骤"
echo "如果发现问题，请尝试以下步骤："
echo "1. 重启有问题的容器: docker-compose restart <container-name>"
echo "2. 重启所有服务: docker-compose down && docker-compose up -d"
echo "3. 检查docker-compose.yml配置"
echo "4. 检查防火墙设置"
echo

echo "========================================"
echo "  诊断完成"
echo "========================================"
