import pandas as pd

def analyze_excel_structure():
    """分析Excel文件结构"""
    print("=== 分析Excel文件结构 ===")
    
    # 使用正确的header
    df = pd.read_excel('YPdatas.xls', header=1)
    print(f'数据形状: {df.shape}')
    
    print('\n实际的列名:')
    for i, col in enumerate(df.columns):
        print(f'{i:2d}: {col}')
    
    print('\n脚本期望的关键列名检查:')
    expected_cols = ['抽样时间', '抽样编号', '总价（元）', '样品名称', '批准文号', '被抽样单位']
    for col in expected_cols:
        if col in df.columns:
            print(f'✓ {col} - 存在')
        else:
            print(f'✗ {col} - 不存在')
            # 查找相似的列名
            similar = []
            for c in df.columns:
                if col.replace('（', '').replace('）', '') in str(c) or str(c) in col:
                    similar.append(c)
            if similar:
                print(f'  相似列名: {similar}')
    
    print('\n前5行数据预览:')
    print(df.head())
    
    print('\n关键列的数据统计:')
    key_cols = [col for col in expected_cols if col in df.columns]
    for col in key_cols:
        print(f'\n{col}:')
        print(f'  非空值数量: {df[col].notna().sum()}')
        print(f'  唯一值数量: {df[col].nunique()}')
        if col == '抽样时间':
            print(f'  时间范围: {df[col].min()} 到 {df[col].max()}')
    
    return df

if __name__ == "__main__":
    try:
        df = analyze_excel_structure()
    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
