#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_filter():
    """验证检验机构过滤功能"""
    
    print("=== 检验机构过滤功能验证 ===")
    
    # 读取检验报告
    df = pd.read_excel('检验报告.xlsx')
    print(f"原始数据总数: {len(df)} 行")
    
    # 查看承检机构分布
    if '承检机构' in df.columns:
        print(f"\n承检机构分布:")
        institution_counts = df['承检机构'].value_counts()
        for institution, count in institution_counts.items():
            print(f"  {institution}: {count} 条 ({count/len(df)*100:.1f}%)")
        
        # 过滤广州市药品检验所的数据
        filtered_df = df[df['承检机构'] == '广州市药品检验所']
        print(f"\n过滤后数据:")
        print(f"  广州市药品检验所: {len(filtered_df)} 条")
        print(f"  过滤比例: {len(filtered_df)/len(df)*100:.1f}%")
        
        # 验证过滤后的数据
        print(f"\n过滤后数据验证:")
        print(f"  唯一承检机构: {filtered_df['承检机构'].unique()}")
        print(f"  抽样凭证编号数量: {filtered_df['抽样凭证编号'].nunique()}")
        print(f"  批准文号数量: {filtered_df['批准文号或备案号'].nunique()}")
        
        # 按月份统计过滤后的数据
        if '抽样日期' in filtered_df.columns:
            filtered_df['抽样月份'] = pd.to_datetime(filtered_df['抽样日期'], errors='coerce').dt.strftime('%Y-%m')
            month_stats = filtered_df['抽样月份'].value_counts().sort_index()
            print(f"\n过滤后月度分布:")
            for month, count in month_stats.items():
                if pd.notna(month):
                    print(f"  {month}: {count} 条")
        
        # 计算购样金额
        if '药品总价（元）' in filtered_df.columns:
            total_amount = filtered_df['药品总价（元）'].sum()
            print(f"\n过滤后购样金额: {total_amount:.2f} 元")
    
    else:
        print("错误：未找到承检机构列")

if __name__ == "__main__":
    verify_filter()
