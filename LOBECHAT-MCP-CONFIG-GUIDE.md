# LobeChat MCP配置指南

## 🎯 问题解决

您遇到的"获取 Manifest 失败: fetch failed"错误是因为LobeChat的Streamable HTTP类型需要特定的端点结构。

## 🔧 解决步骤

### 1. 重启服务以应用新的Nginx配置

```bash
# 重启服务
docker-compose down
docker-compose up -d

# 等待服务启动
sleep 15
```

### 2. 验证Manifest端点

```bash
# 测试manifest端点
curl http://**************/manifest

# 应该返回JSON格式的工具清单
```

### 3. LobeChat的正确配置

在LobeChat中使用以下配置：

#### 方式一：基本配置
```json
{
  "url": "http://**************/mcp",
  "type": "streamable-http"
}
```

#### 方式二：完整配置
```json
{
  "name": "Chart Generator",
  "description": "Generate various types of charts and visualizations",
  "url": "http://**************/mcp", 
  "type": "streamable-http",
  "manifest": "http://**************/manifest"
}
```

#### 方式三：如果需要认证
```json
{
  "name": "Chart Generator",
  "url": "http://**************/mcp",
  "type": "streamable-http", 
  "manifest": "http://**************/manifest",
  "headers": {
    "Authorization": "Bearer your-token-here"
  }
}
```

## 🌐 端点说明

现在您的服务器提供以下端点：

| 端点 | 用途 | 客户端 |
|------|------|--------|
| `http://**************/manifest` | 工具清单 | LobeChat |
| `http://**************/mcp` | MCP通信 | LobeChat |
| `http://**************/sse` | SSE通信 | 其他客户端 |
| `http://**************:3000/api/gpt-vis` | 图表渲染 | 后端服务 |

## 🧪 测试步骤

### 1. 测试Manifest端点
```bash
curl -H "Accept: application/json" http://**************/manifest
```

期望返回：
```json
{
  "schemaVersion": "2024-11-05",
  "vendor": "antv",
  "name": "mcp-server-chart",
  "tools": [...]
}
```

### 2. 测试MCP端点
```bash
curl -X POST http://**************/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/list",
    "params": {}
  }'
```

### 3. 在LobeChat中测试
1. 打开LobeChat设置
2. 找到MCP服务器配置
3. 添加新的MCP服务器
4. 输入上面的配置
5. 保存并测试连接

## 🔍 故障排除

### 如果仍然显示"fetch failed"

#### 1. 检查网络连通性
```bash
# 从LobeChat所在设备测试
ping **************
curl http://**************/manifest
```

#### 2. 检查防火墙
```bash
# 确保端口开放
sudo ufw allow 80
sudo ufw allow 443
```

#### 3. 检查服务状态
```bash
docker-compose ps
docker-compose logs nginx
```

#### 4. 检查CORS设置
如果LobeChat运行在不同域名，可能需要CORS支持。Nginx配置已经添加了CORS头：
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

### 如果需要HTTPS

如果LobeChat要求HTTPS，可以配置SSL：

```bash
# 配置HTTPS
./configure-server-ip.sh --ip ************** --https

# 然后在LobeChat中使用HTTPS URL
# https://**************/mcp
```

## 📱 LobeChat配置界面操作

### 在LobeChat中的具体步骤：

1. **打开设置页面**
   - 点击设置图标
   - 找到"插件"或"MCP服务器"选项

2. **添加MCP服务器**
   - 点击"添加服务器"或"+"按钮
   - 选择"Streamable HTTP"类型

3. **填写配置信息**
   - **名称**: `Chart Generator`
   - **描述**: `Generate various types of charts`
   - **URL**: `http://**************/mcp`
   - **类型**: `Streamable HTTP`
   - **Manifest**: `http://**************/manifest` (如果有此字段)

4. **保存并测试**
   - 点击保存
   - 测试连接
   - 如果成功，应该能看到可用的工具列表

## 🎯 与Cherry Studio的兼容性

这个修改不会影响Cherry Studio的配置，因为：

- **Cherry Studio**: 使用stdio方式，本地运行MCP进程
- **LobeChat**: 使用HTTP方式，连接到远程MCP服务器
- **两者独立**: 使用不同的通信方式，互不干扰

您的Cherry Studio配置仍然有效：
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart"],
      "env": {
        "VIS_REQUEST_SERVER": "http://**************:3000/api/gpt-vis"
      }
    }
  }
}
```

## 🎉 总结

现在您有了一个完整的解决方案：

1. ✅ **Cherry Studio**: 通过stdio本地运行MCP
2. ✅ **LobeChat**: 通过HTTP连接到远程MCP服务器
3. ✅ **共享后端**: 两者都使用同一个GPT-Vis-SSR渲染服务
4. ✅ **互不干扰**: 两种客户端可以同时使用

请按照上面的步骤重启服务并在LobeChat中配置，应该就能正常工作了！
