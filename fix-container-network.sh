#!/bin/bash

# 修复容器网络连接问题
# 确保容器中有必要的工具并测试网络连接

set -e

echo "=== 修复容器网络连接问题 ==="

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo "错误：找不到MCP部署目录"
    exit 1
fi

echo "使用工作目录: $WORK_DIR"
cd "$WORK_DIR"

echo "1. 在MCP容器中安装curl..."
docker exec mcp-chart-server sh -c "apk add --no-cache curl" || echo "curl可能已经安装"

echo "2. 测试容器间网络连接..."
if docker exec mcp-chart-server curl -s --connect-timeout 5 http://gpt-vis-ssr:3000/health 2>/dev/null; then
    echo "✅ 容器间网络连接正常"
    docker exec mcp-chart-server curl -s http://gpt-vis-ssr:3000/health
else
    echo "❌ 容器间网络连接仍然失败"
    echo "检查网络配置..."
    docker network ls
    echo "检查容器网络设置..."
    docker inspect mcp-chart-server | grep -A 10 "Networks"
    docker inspect gpt-vis-ssr | grep -A 10 "Networks"
fi

echo ""
echo "3. 测试从MCP容器调用图表生成API..."
RESPONSE=$(docker exec mcp-chart-server curl -s -X POST http://gpt-vis-ssr:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "测试1", "value": 30},
      {"category": "测试2", "value": 70}
    ]
  }' \
  --connect-timeout 10 \
  --max-time 30 2>/dev/null || echo "API调用失败")

if echo "$RESPONSE" | grep -q '"success":true'; then
    echo "✅ 容器间API调用成功"
    echo "响应长度: $(echo "$RESPONSE" | wc -c) 字符"
else
    echo "❌ 容器间API调用失败"
    echo "响应: $RESPONSE"
fi

echo ""
echo "4. 检查MCP服务日志..."
echo "--- MCP服务最新日志 ---"
docker logs --tail=10 mcp-chart-server

echo ""
echo "5. 重启MCP服务以确保配置生效..."
docker compose restart mcp-chart-server

echo "等待MCP服务重启..."
sleep 20

echo ""
echo "6. 再次测试MCP服务..."
for i in {1..5}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✅ MCP服务重启后正常"
        break
    else
        echo "⏳ 等待MCP服务重启... ($i/5)"
        sleep 10
    fi
done

echo ""
echo "7. 最终网络连接测试..."
if docker exec mcp-chart-server curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
    echo "✅ 最终测试：容器间网络连接正常"
else
    echo "❌ 最终测试：容器间网络连接仍有问题"
    echo "尝试重新创建网络..."
    docker compose down
    docker network prune -f
    docker compose up -d
    echo "等待服务重新启动..."
    sleep 30
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "现在请重新测试Cherry Studio连接："
echo "1. 确保Cherry Studio配置的URL是: http://localhost:1122/sse"
echo "2. 如果是远程服务器，使用: http://YOUR_SERVER_IP:1122/sse"
echo "3. 重新运行测试脚本验证: sudo ./test-mcp-chart.sh"
echo ""
echo "如果问题仍然存在，请检查："
echo "- Cherry Studio的网络设置"
echo "- 服务器防火墙配置"
echo "- MCP协议版本兼容性"
