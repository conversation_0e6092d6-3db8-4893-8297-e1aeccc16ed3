#!/bin/bash

# 修复MCP构建问题
# 解决mcp-server-chart项目没有build脚本的问题

set -e

echo "=== 修复MCP构建问题 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

APP_DIR="/opt/mcp-prebuilt"
echo "进入应用目录: $APP_DIR"
cd $APP_DIR

# 停止现有服务
echo "停止现有Docker服务..."
docker compose down 2>/dev/null || true

# 检查mcp-server-chart目录
if [ ! -d "mcp-server-chart" ]; then
    echo "克隆mcp-server-chart项目..."
    git clone https://github.com/antvis/mcp-server-chart.git
fi

cd mcp-server-chart

echo "检查项目结构..."
ls -la

echo "检查package.json中的脚本..."
if [ -f "package.json" ]; then
    echo "=== package.json scripts ==="
    cat package.json | grep -A 10 '"scripts"' || echo "没有找到scripts部分"
    echo "=========================="
fi

echo "安装依赖..."
npm install --ignore-scripts

echo "检查可用的npm脚本..."
npm run 2>&1 || true

echo "尝试不同的构建方法..."

# 方法1：检查是否有build脚本
if npm run 2>&1 | grep -q "build"; then
    echo "方法1：找到build脚本，使用npm run build"
    npm run build
elif [ -f "tsconfig.json" ]; then
    echo "方法2：找到tsconfig.json，使用tsc编译"
    npx tsc
else
    echo "方法3：手动创建构建目录"
    mkdir -p build
    
    # 检查src目录
    if [ -d "src" ]; then
        echo "复制src目录内容到build目录..."
        cp -r src/* build/
        
        # 如果是TypeScript文件，尝试编译
        if find src -name "*.ts" | head -1 | grep -q ".ts"; then
            echo "发现TypeScript文件，尝试编译..."
            npx tsc --outDir build --target es2020 --module commonjs src/*.ts 2>/dev/null || {
                echo "TypeScript编译失败，直接复制文件..."
                # 将.ts文件重命名为.js
                find build -name "*.ts" -exec bash -c 'mv "$1" "${1%.ts}.js"' _ {} \;
            }
        fi
    else
        echo "没有找到src目录，检查根目录文件..."
        # 查找可能的入口文件
        for file in index.js index.ts main.js main.ts server.js server.ts; do
            if [ -f "$file" ]; then
                echo "找到入口文件: $file"
                cp "$file" build/
                if [[ "$file" == *.ts ]]; then
                    # 重命名.ts为.js
                    mv "build/$file" "build/${file%.ts}.js"
                fi
                break
            fi
        done
    fi
fi

echo "检查构建结果..."
if [ -d "build" ]; then
    echo "=== build目录内容 ==="
    ls -la build/
    echo "====================="
elif [ -d "dist" ]; then
    echo "=== dist目录内容 ==="
    ls -la dist/
    echo "===================="
    # 如果只有dist目录，创建build目录的软链接
    ln -sf dist build
elif [ -d "lib" ]; then
    echo "=== lib目录内容 ==="
    ls -la lib/
    echo "=================="
    # 如果只有lib目录，创建build目录的软链接
    ln -sf lib build
else
    echo "警告：没有找到构建输出目录"
fi

# 查找入口文件
echo "查找入口文件..."
ENTRY_FILE=""
for dir in build dist lib .; do
    for file in index.js main.js server.js; do
        if [ -f "$dir/$file" ]; then
            if [ "$dir" = "." ]; then
                ENTRY_FILE="$file"
            else
                ENTRY_FILE="$dir/$file"
            fi
            echo "找到入口文件: $ENTRY_FILE"
            break 2
        fi
    done
done

if [ -z "$ENTRY_FILE" ]; then
    echo "错误：找不到入口文件"
    echo "尝试查看所有.js文件..."
    find . -name "*.js" -type f | head -10
    exit 1
fi

# 测试入口文件
echo "测试入口文件..."
if node "$ENTRY_FILE" --help 2>/dev/null; then
    echo "✅ 入口文件可以正常运行"
else
    echo "⚠️ 入口文件测试失败，但继续部署"
fi

cd $APP_DIR

echo "更新Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++
        npm install
        npm start
    restart: unless-stopped
    networks:
      - mcp-network

  mcp-chart-server:
    image: node:18-alpine
    container_name: mcp-chart-server
    working_dir: /app
    ports:
      - "1122:1122"
    volumes:
      - ./mcp-server-chart:/app
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "MCP服务启动中..."
        echo "安装依赖..."
        npm install --ignore-scripts
        echo "检查项目结构..."
        ls -la
        echo "查找入口文件..."
        ENTRY_FILE=""
        for dir in build dist lib .; do
          for file in index.js main.js server.js; do
            if [ -f "$dir/$file" ]; then
              if [ "$dir" = "." ]; then
                ENTRY_FILE="$file"
              else
                ENTRY_FILE="$dir/$file"
              fi
              echo "找到入口文件: $ENTRY_FILE"
              break 2
            fi
          done
        done
        if [ -z "$ENTRY_FILE" ]; then
          echo "错误：找不到入口文件"
          echo "可用文件："
          find . -name "*.js" -type f | head -10
          exit 1
        fi
        echo "启动MCP服务器..."
        node $ENTRY_FILE --transport sse --port 1122
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
EOF

echo "启动Docker服务..."
docker compose up -d

echo "等待服务启动..."
sleep 30

echo "检查服务状态..."
docker compose ps

echo "查看MCP服务日志..."
docker compose logs --tail=20 mcp-chart-server

echo ""
echo "=== 修复完成 ==="
echo "如果服务仍有问题，请查看日志："
echo "  docker compose logs -f mcp-chart-server"
echo "  docker compose logs -f gpt-vis-ssr"
