# MCP Client 配置模板

## Claude Desktop 配置

### macOS 配置文件位置
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

### Windows 配置文件位置
```
%APPDATA%\Claude\claude_desktop_config.json
```

### 配置内容（本地部署）

```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": [
        "-y",
        "@antv/mcp-server-chart",
        "--transport", "sse"
      ],
      "env": {
        "VIS_REQUEST_SERVER": "http://YOUR_SERVER_IP:3000/api/gpt-vis"
      }
    }
  }
}
```

### 配置内容（使用默认服务）

```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": [
        "-y",
        "@antv/mcp-server-chart"
      ]
    }
  }
}
```

## VSCode + Cline 配置

在VSCode的设置中添加：

```json
{
  "cline.mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": [
        "-y",
        "@antv/mcp-server-chart",
        "--transport", "sse"
      ],
      "env": {
        "VIS_REQUEST_SERVER": "http://YOUR_SERVER_IP:3000/api/gpt-vis"
      }
    }
  }
}
```

## Cursor 配置

在Cursor的设置中添加：

```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": [
        "-y",
        "@antv/mcp-server-chart"
      ],
      "env": {
        "VIS_REQUEST_SERVER": "http://YOUR_SERVER_IP:3000/api/gpt-vis"
      }
    }
  }
}
```

## 环境变量说明

| 变量名 | 描述 | 默认值 | 示例 |
|--------|------|--------|------|
| `VIS_REQUEST_SERVER` | 自定义图表生成服务URL | `https://antv-studio.alipay.com/api/gpt-vis` | `http://*************:3000/api/gpt-vis` |
| `SERVICE_ID` | 服务标识符（用于记录） | - | `your-service-id-123` |
| `DISABLED_TOOLS` | 禁用的工具列表 | - | `generate_fishbone_diagram,generate_mind_map` |

## 网络配置注意事项

1. **防火墙设置**
   ```bash
   # 开放必要端口
   sudo ufw allow 3000/tcp  # 渲染服务
   sudo ufw allow 1122/tcp  # MCP服务
   ```

2. **内网访问**
   - 将 `YOUR_SERVER_IP` 替换为您的Debian服务器内网IP
   - 例如：`*************` 或 `*********`

3. **公网访问**（如果需要）
   - 配置路由器端口转发
   - 使用域名和SSL证书
   - 考虑使用Nginx反向代理

## 测试配置

配置完成后，在客户端中测试：

```
请帮我生成一个折线图，数据如下：
- 2025年1月：100
- 2025年2月：150  
- 2025年3月：200
- 2025年4月：180
- 2025年5月：220
```

如果配置正确，应该能看到生成的图表。

## 故障排除

1. **连接失败**
   - 检查服务器IP地址是否正确
   - 确认防火墙端口已开放
   - 验证服务是否正在运行

2. **图表生成失败**
   - 检查渲染服务日志：`sudo journalctl -u gpt-vis-ssr -f`
   - 验证渲染服务健康状态：`curl http://localhost:3000/health`

3. **MCP连接问题**
   - 检查MCP服务日志：`sudo journalctl -u mcp-chart-server -f`
   - 重启客户端应用程序
   - 验证配置文件语法正确
