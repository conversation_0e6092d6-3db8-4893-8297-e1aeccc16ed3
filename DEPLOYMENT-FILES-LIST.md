# MCP Chart Server 部署文件清单

## 📋 核心部署文件

### 🚀 主要脚本文件
| 文件名 | 大小 | 功能描述 | 使用方法 |
|--------|------|----------|----------|
| `mcp-chart-deploy.sh` | ~25KB | 主部署脚本，支持3种部署方式 | `sudo ./mcp-chart-deploy.sh` |
| `manage-services.sh` | ~15KB | 服务管理脚本，统一管理所有服务 | `./manage-services.sh status` |
| `test-services.sh` | ~12KB | 服务测试脚本，全面测试功能 | `./test-services.sh` |
| `package-deployment.sh` | ~8KB | 打包脚本，生成完整部署包 | `./package-deployment.sh` |

### 🐳 Docker 配置文件
| 文件名 | 大小 | 功能描述 |
|--------|------|----------|
| `docker-compose.yml` | ~2KB | Docker Compose 服务编排配置 |
| `nginx.conf` | ~3KB | Nginx 反向代理配置 |
| `gpt-vis-ssr/Dockerfile` | ~1.5KB | GPT-Vis-SSR 服务 Docker 镜像 |

### 📦 服务配置文件
| 文件名 | 大小 | 功能描述 |
|--------|------|----------|
| `gpt-vis-ssr/package.json` | ~500B | GPT-Vis-SSR Node.js 依赖配置 |
| `gpt-vis-ssr/index.js` | ~2KB | GPT-Vis-SSR 服务主程序 |

### 📖 文档文件
| 文件名 | 大小 | 功能描述 |
|--------|------|----------|
| `MCP-CHART-DEPLOY-README.md` | ~8KB | 详细部署和使用文档 |
| `PROJECT-OVERVIEW.md` | ~10KB | 项目总览和快速开始指南 |
| `DEPLOYMENT-FILES-LIST.md` | ~3KB | 本文件，部署文件清单 |

## 🎯 文件功能详解

### 1. mcp-chart-deploy.sh (主部署脚本)
**功能特性:**
- ✅ 自动检测系统环境和依赖
- ✅ 支持 3 种部署方式：Docker Compose、systemd、手动部署
- ✅ 自动安装 Docker、Node.js 等依赖
- ✅ 创建完整的服务配置文件
- ✅ 自动启动和健康检查

**支持的命令行参数:**
```bash
./mcp-chart-deploy.sh -t docker    # Docker Compose 部署
./mcp-chart-deploy.sh -t systemd   # systemd 服务部署
./mcp-chart-deploy.sh -t separate  # 手动分离部署
./mcp-chart-deploy.sh -h           # 显示帮助信息
```

### 2. manage-services.sh (服务管理脚本)
**功能特性:**
- ✅ 自动检测部署方式
- ✅ 统一的服务管理接口
- ✅ 实时服务监控
- ✅ 日志查看和分析
- ✅ 数据备份和清理

**支持的操作:**
```bash
./manage-services.sh start      # 启动服务
./manage-services.sh stop       # 停止服务
./manage-services.sh restart    # 重启服务
./manage-services.sh status     # 查看状态
./manage-services.sh logs       # 查看日志
./manage-services.sh monitor    # 实时监控
./manage-services.sh cleanup 7  # 清理7天前的图片
./manage-services.sh backup     # 备份数据
```

### 3. test-services.sh (服务测试脚本)
**测试项目:**
- ✅ GPT-Vis-SSR 健康检查
- ✅ 图表渲染功能测试
- ✅ MCP Server 连接测试
- ✅ Nginx 代理测试
- ✅ 图片访问测试
- ✅ 并发性能测试

**测试输出示例:**
```
[STEP] 测试 GPT-Vis-SSR 健康检查...
[INFO] ✓ GPT-Vis-SSR 健康检查通过

[STEP] 测试图表渲染功能...
[INFO] ✓ 图表渲染成功
[INFO] 图片URL: http://localhost:3000/images/abc123.png
```

### 4. package-deployment.sh (打包脚本)
**打包内容:**
- ✅ 所有部署脚本
- ✅ 配置文件模板
- ✅ 文档和说明
- ✅ 示例配置
- ✅ 快速开始脚本

**生成的部署包结构:**
```
mcp-chart-deployment-20250131_120000/
├── mcp-chart-deploy.sh
├── quick-start.sh
├── docker-compose.yml
├── nginx.conf
├── gpt-vis-ssr/
├── scripts/
│   ├── manage-services.sh
│   └── test-services.sh
├── docs/
│   └── README.md
└── examples/
    ├── env-example
    ├── systemd-service-example
    └── nginx-ssl-example.conf
```

## 🔧 配置文件说明

### Docker Compose 配置 (docker-compose.yml)
```yaml
services:
  gpt-vis-ssr:        # 图表渲染服务
  mcp-server-chart:   # MCP 主服务
  nginx:              # 反向代理
```

### Nginx 配置 (nginx.conf)
```nginx
location /sse         # MCP SSE 端点
location /mcp         # MCP Streamable 端点
location /api/gpt-vis # 图表渲染 API
location /images/     # 图片静态文件
```

### GPT-Vis-SSR 服务 (gpt-vis-ssr/index.js)
```javascript
app.get('/health')           # 健康检查
app.post('/api/gpt-vis')     # 图表渲染接口
app.use('/images', static)   # 图片静态服务
```

## 📊 部署方式对比

| 特性 | Docker Compose | systemd | 手动部署 |
|------|----------------|---------|----------|
| 安装难度 | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| 资源占用 | 中等 | 低 | 低 |
| 管理便利性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |
| 扩展性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| 适用场景 | 生产环境 | 系统集成 | 开发测试 |

## 🚀 快速部署指南

### 1. 下载部署文件
```bash
# 方式一：直接下载脚本
wget https://your-server/mcp-chart-deploy.sh
chmod +x mcp-chart-deploy.sh

# 方式二：使用部署包
tar -xzf mcp-chart-deployment-*.tar.gz
cd mcp-chart-deployment-*
```

### 2. 运行部署
```bash
# 交互式部署
sudo ./mcp-chart-deploy.sh

# 直接指定部署方式
sudo ./mcp-chart-deploy.sh -t docker
```

### 3. 验证部署
```bash
# 测试服务
./test-services.sh

# 查看状态
./manage-services.sh status
```

## 🛠️ 自定义配置

### 环境变量配置
```bash
export VIS_REQUEST_SERVER="http://localhost:3000/api/gpt-vis"
export SERVICE_ID="your-service-id"
export DISABLED_TOOLS="generate_fishbone_diagram"
```

### 端口配置
- **3000**: GPT-Vis-SSR 服务端口
- **1122**: MCP Server Chart 端口
- **80/443**: Nginx 代理端口

### 存储配置
- **charts-storage/**: 图片存储目录
- **logs/**: 日志文件目录
- **backup/**: 备份文件目录

## 📈 性能参数

### 系统要求
- **内存**: 最低 2GB，推荐 4GB+
- **磁盘**: 最低 10GB，推荐 50GB+
- **CPU**: 最低 2 核，推荐 4 核+
- **网络**: 稳定的网络连接

### 性能指标
- **图表渲染**: 平均 2-5 秒/图表
- **并发处理**: 支持 10+ 并发请求
- **图片存储**: 自动清理，支持 CDN
- **内存使用**: 约 500MB-1GB

## 🔍 故障排除

### 常见问题文件
1. **端口冲突**: 检查 `netstat -tlnp`
2. **权限问题**: 确保脚本有执行权限
3. **依赖缺失**: 查看安装日志
4. **网络问题**: 检查防火墙设置

### 日志文件位置
- Docker: `docker-compose logs`
- systemd: `/var/log/journal/`
- 手动: `logs/` 目录

## 📞 技术支持

如需技术支持，请提供以下信息：
1. 操作系统版本
2. 部署方式
3. 错误日志
4. 服务状态输出

---

**🎉 现在您拥有了完整的 MCP Chart Server 部署解决方案！**
