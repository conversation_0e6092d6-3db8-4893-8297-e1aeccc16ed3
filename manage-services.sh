#!/bin/bash

# MCP Chart Server 服务管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检测部署方式
detect_deployment_type() {
    if [[ -f "docker-compose.yml" ]] && command -v docker-compose >/dev/null 2>&1; then
        echo "docker-compose"
    elif systemctl list-units --type=service | grep -q "gpt-vis-ssr\|mcp-chart"; then
        echo "systemd"
    elif [[ -f "gpt-vis-ssr.pid" ]] || [[ -f "mcp-chart.pid" ]]; then
        echo "manual"
    else
        echo "unknown"
    fi
}

# Docker Compose 管理
manage_docker_compose() {
    local action="$1"
    
    case $action in
        start)
            log_step "启动 Docker Compose 服务..."
            docker-compose up -d
            ;;
        stop)
            log_step "停止 Docker Compose 服务..."
            docker-compose down
            ;;
        restart)
            log_step "重启 Docker Compose 服务..."
            docker-compose restart
            ;;
        status)
            log_step "查看 Docker Compose 服务状态..."
            docker-compose ps
            ;;
        logs)
            log_step "查看 Docker Compose 服务日志..."
            docker-compose logs -f
            ;;
        update)
            log_step "更新 Docker Compose 服务..."
            docker-compose pull
            docker-compose up -d
            ;;
        *)
            log_error "未知操作: $action"
            return 1
            ;;
    esac
}

# systemd 管理
manage_systemd() {
    local action="$1"
    
    case $action in
        start)
            log_step "启动 systemd 服务..."
            sudo systemctl start gpt-vis-ssr mcp-chart-server
            ;;
        stop)
            log_step "停止 systemd 服务..."
            sudo systemctl stop mcp-chart-server gpt-vis-ssr
            ;;
        restart)
            log_step "重启 systemd 服务..."
            sudo systemctl restart gpt-vis-ssr mcp-chart-server
            ;;
        status)
            log_step "查看 systemd 服务状态..."
            sudo systemctl status gpt-vis-ssr --no-pager
            echo
            sudo systemctl status mcp-chart-server --no-pager
            ;;
        logs)
            log_step "查看 systemd 服务日志..."
            sudo journalctl -u gpt-vis-ssr -u mcp-chart-server -f
            ;;
        enable)
            log_step "启用 systemd 服务..."
            sudo systemctl enable gpt-vis-ssr mcp-chart-server
            ;;
        disable)
            log_step "禁用 systemd 服务..."
            sudo systemctl disable mcp-chart-server gpt-vis-ssr
            ;;
        *)
            log_error "未知操作: $action"
            return 1
            ;;
    esac
}

# 手动管理
manage_manual() {
    local action="$1"
    
    case $action in
        start)
            log_step "启动手动服务..."
            
            # 启动 GPT-Vis-SSR
            if [[ ! -f "gpt-vis-ssr.pid" ]]; then
                cd gpt-vis-ssr
                nohup npm start > ../logs/gpt-vis-ssr.log 2>&1 &
                echo $! > ../gpt-vis-ssr.pid
                cd ..
                log_info "GPT-Vis-SSR 已启动 (PID: $(cat gpt-vis-ssr.pid))"
            else
                log_warn "GPT-Vis-SSR 已在运行"
            fi
            
            # 启动 MCP Chart Server
            if [[ ! -f "mcp-chart.pid" ]]; then
                export VIS_REQUEST_SERVER="http://localhost:3000/api/gpt-vis"
                nohup mcp-server-chart --transport sse --port 1122 > logs/mcp-chart.log 2>&1 &
                echo $! > mcp-chart.pid
                log_info "MCP Chart Server 已启动 (PID: $(cat mcp-chart.pid))"
            else
                log_warn "MCP Chart Server 已在运行"
            fi
            ;;
        stop)
            log_step "停止手动服务..."
            
            if [[ -f "mcp-chart.pid" ]]; then
                kill $(cat mcp-chart.pid) 2>/dev/null || true
                rm -f mcp-chart.pid
                log_info "MCP Chart Server 已停止"
            fi
            
            if [[ -f "gpt-vis-ssr.pid" ]]; then
                kill $(cat gpt-vis-ssr.pid) 2>/dev/null || true
                rm -f gpt-vis-ssr.pid
                log_info "GPT-Vis-SSR 已停止"
            fi
            ;;
        restart)
            manage_manual stop
            sleep 2
            manage_manual start
            ;;
        status)
            log_step "查看手动服务状态..."
            
            if [[ -f "gpt-vis-ssr.pid" ]]; then
                local pid=$(cat gpt-vis-ssr.pid)
                if ps -p $pid > /dev/null 2>&1; then
                    log_info "✓ GPT-Vis-SSR 运行中 (PID: $pid)"
                else
                    log_warn "✗ GPT-Vis-SSR 未运行 (PID 文件存在但进程不存在)"
                    rm -f gpt-vis-ssr.pid
                fi
            else
                log_warn "✗ GPT-Vis-SSR 未运行"
            fi
            
            if [[ -f "mcp-chart.pid" ]]; then
                local pid=$(cat mcp-chart.pid)
                if ps -p $pid > /dev/null 2>&1; then
                    log_info "✓ MCP Chart Server 运行中 (PID: $pid)"
                else
                    log_warn "✗ MCP Chart Server 未运行 (PID 文件存在但进程不存在)"
                    rm -f mcp-chart.pid
                fi
            else
                log_warn "✗ MCP Chart Server 未运行"
            fi
            ;;
        logs)
            log_step "查看手动服务日志..."
            
            if [[ -f "logs/gpt-vis-ssr.log" ]]; then
                echo "=== GPT-Vis-SSR 日志 ==="
                tail -n 20 logs/gpt-vis-ssr.log
                echo
            fi
            
            if [[ -f "logs/mcp-chart.log" ]]; then
                echo "=== MCP Chart Server 日志 ==="
                tail -n 20 logs/mcp-chart.log
            fi
            ;;
        *)
            log_error "未知操作: $action"
            return 1
            ;;
    esac
}

# 清理功能
cleanup_images() {
    log_step "清理旧图片..."
    
    local days="${1:-7}"
    local image_dir="charts-storage"
    
    if [[ -d "$image_dir" ]]; then
        local count=$(find "$image_dir" -name "*.png" -mtime +$days | wc -l)
        if [[ $count -gt 0 ]]; then
            find "$image_dir" -name "*.png" -mtime +$days -delete
            log_info "已清理 $count 个超过 $days 天的图片文件"
        else
            log_info "没有需要清理的图片文件"
        fi
    else
        log_warn "图片存储目录不存在: $image_dir"
    fi
}

# 备份功能
backup_data() {
    log_step "备份数据..."
    
    local backup_dir="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份配置文件
    if [[ -f "docker-compose.yml" ]]; then
        cp docker-compose.yml "$backup_dir/"
    fi
    
    if [[ -f "nginx.conf" ]]; then
        cp nginx.conf "$backup_dir/"
    fi
    
    # 备份图片（如果不太大）
    if [[ -d "charts-storage" ]]; then
        local size=$(du -sm charts-storage 2>/dev/null | cut -f1)
        if [[ $size -lt 100 ]]; then  # 小于100MB才备份
            cp -r charts-storage "$backup_dir/"
            log_info "已备份图片数据"
        else
            log_warn "图片数据过大 (${size}MB)，跳过备份"
        fi
    fi
    
    log_info "备份完成: $backup_dir"
}

# 监控功能
monitor_services() {
    log_step "监控服务状态..."
    
    while true; do
        clear
        echo "========================================"
        echo "  MCP Chart Server 服务监控"
        echo "  $(date)"
        echo "========================================"
        echo
        
        # 检查服务状态
        local deployment_type=$(detect_deployment_type)
        case $deployment_type in
            docker-compose)
                docker-compose ps
                ;;
            systemd)
                manage_systemd status
                ;;
            manual)
                manage_manual status
                ;;
            *)
                log_warn "未检测到运行中的服务"
                ;;
        esac
        
        echo
        echo "========================================"
        
        # 检查服务健康状态
        if curl -s http://localhost:3000/health >/dev/null 2>&1; then
            log_info "✓ GPT-Vis-SSR 健康检查通过"
        else
            log_error "✗ GPT-Vis-SSR 健康检查失败"
        fi
        
        if timeout 3 curl -s http://localhost:1122/sse >/dev/null 2>&1; then
            log_info "✓ MCP Server 响应正常"
        else
            log_warn "⚠ MCP Server 响应异常"
        fi
        
        echo
        echo "按 Ctrl+C 退出监控"
        sleep 10
    done
}

# 显示帮助信息
show_help() {
    echo "MCP Chart Server 服务管理脚本"
    echo
    echo "用法: $0 <操作> [参数]"
    echo
    echo "操作:"
    echo "  start          启动服务"
    echo "  stop           停止服务"
    echo "  restart        重启服务"
    echo "  status         查看服务状态"
    echo "  logs           查看服务日志"
    echo "  test           测试服务"
    echo "  cleanup [天数] 清理旧图片 (默认7天)"
    echo "  backup         备份数据"
    echo "  monitor        监控服务状态"
    echo "  help           显示此帮助信息"
    echo
    echo "systemd 专用操作:"
    echo "  enable         启用开机自启"
    echo "  disable        禁用开机自启"
    echo
    echo "Docker Compose 专用操作:"
    echo "  update         更新服务镜像"
    echo
    echo "示例:"
    echo "  $0 start                # 启动服务"
    echo "  $0 logs                 # 查看日志"
    echo "  $0 cleanup 3            # 清理3天前的图片"
    echo "  $0 monitor              # 监控服务状态"
}

# 主函数
main() {
    local action="${1:-help}"
    
    case $action in
        start|stop|restart|status|logs|enable|disable|update)
            local deployment_type=$(detect_deployment_type)
            log_info "检测到部署方式: $deployment_type"
            
            case $deployment_type in
                docker-compose)
                    manage_docker_compose "$action"
                    ;;
                systemd)
                    manage_systemd "$action"
                    ;;
                manual)
                    manage_manual "$action"
                    ;;
                *)
                    log_error "未检测到已部署的服务"
                    exit 1
                    ;;
            esac
            ;;
        test)
            if [[ -f "test-services.sh" ]]; then
                bash test-services.sh
            else
                log_error "测试脚本不存在: test-services.sh"
                exit 1
            fi
            ;;
        cleanup)
            cleanup_images "$2"
            ;;
        backup)
            backup_data
            ;;
        monitor)
            monitor_services
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 如果脚本被直接执行，则运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
