#!/bin/bash

# 测试完全本地化渲染方案
# 验证本地Canvas渲染是否正常工作

SERVER_IP="**************"
echo "=== 测试完全本地化渲染方案 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

success_count=0
total_tests=0

print_result() {
    local test_name="$1"
    local result="$2"
    total_tests=$((total_tests + 1))
    
    if [ "$result" = "success" ]; then
        echo -e "${GREEN}✅ $test_name${NC}"
        success_count=$((success_count + 1))
    else
        echo -e "${RED}❌ $test_name${NC}"
    fi
}

print_section() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_section "1. 基础服务检查"

# 检查Docker容器状态
if docker ps --format "{{.Names}}" | grep -q "gpt-vis-ssr"; then
    print_result "渲染服务容器运行中" "success"
else
    print_result "渲染服务容器运行中" "fail"
fi

# 检查服务响应
if curl -s --connect-timeout 10 http://localhost:3000/health > /dev/null; then
    print_result "渲染服务响应正常" "success"
else
    print_result "渲染服务响应正常" "fail"
fi

print_section "2. 本地化特性检查"

# 获取健康检查信息
HEALTH_RESPONSE=$(curl -s http://localhost:3000/health 2>/dev/null)
echo "健康检查响应:"
echo "$HEALTH_RESPONSE" | jq . 2>/dev/null || echo "$HEALTH_RESPONSE"

# 检查是否为本地渲染
if echo "$HEALTH_RESPONSE" | grep -q '"isLocalOnly":true'; then
    print_result "确认为完全本地渲染" "success"
else
    print_result "确认为完全本地渲染" "fail"
fi

# 检查渲染能力
if echo "$HEALTH_RESPONSE" | grep -q '"renderCapabilities"'; then
    print_result "渲染能力检测正常" "success"
    CAPABILITIES=$(echo "$HEALTH_RESPONSE" | jq -r '.renderCapabilities[]' 2>/dev/null)
    echo "  可用渲染引擎: $CAPABILITIES"
else
    print_result "渲染能力检测正常" "fail"
fi

print_section "3. 图表类型支持检查"

# 检查支持的图表类型
CHART_TYPES_RESPONSE=$(curl -s http://localhost:3000/api/chart-types 2>/dev/null)
if echo "$CHART_TYPES_RESPONSE" | grep -q '"success":true'; then
    print_result "图表类型API正常" "success"
    echo "支持的图表类型:"
    echo "$CHART_TYPES_RESPONSE" | jq -r '.supportedTypes[].type' 2>/dev/null | sed 's/^/  - /'
else
    print_result "图表类型API正常" "fail"
fi

print_section "4. 本地图表生成测试"

# 测试饼图生成
echo "测试饼图生成..."
PIE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "本地渲染", "value": 60},
      {"category": "离线模式", "value": 25},
      {"category": "完全自主", "value": 15}
    ],
    "title": "本地化优势分布"
  }' 2>/dev/null)

if echo "$PIE_RESPONSE" | grep -q '"success":true'; then
    print_result "饼图生成测试" "success"
    if echo "$PIE_RESPONSE" | grep -q '"method":"local-rendering"'; then
        print_result "确认使用本地渲染" "success"
    else
        print_result "确认使用本地渲染" "fail"
    fi
else
    print_result "饼图生成测试" "fail"
    echo "错误信息: $(echo "$PIE_RESPONSE" | jq -r '.errorMessage' 2>/dev/null)"
fi

# 测试柱状图生成
echo ""
echo "测试柱状图生成..."
COLUMN_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "column",
    "data": [
      {"category": "性能", "value": 90},
      {"category": "稳定性", "value": 95},
      {"category": "安全性", "value": 100},
      {"category": "可控性", "value": 100}
    ],
    "title": "本地部署优势评分"
  }' 2>/dev/null)

if echo "$COLUMN_RESPONSE" | grep -q '"success":true'; then
    print_result "柱状图生成测试" "success"
else
    print_result "柱状图生成测试" "fail"
fi

# 测试折线图生成
echo ""
echo "测试折线图生成..."
LINE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "line",
    "data": [
      {"time": "部署前", "value": 0},
      {"time": "安装依赖", "value": 30},
      {"time": "配置服务", "value": 60},
      {"time": "测试验证", "value": 90},
      {"time": "完全就绪", "value": 100}
    ],
    "title": "本地化部署进度"
  }' 2>/dev/null)

if echo "$LINE_RESPONSE" | grep -q '"success":true'; then
    print_result "折线图生成测试" "success"
else
    print_result "折线图生成测试" "fail"
fi

print_section "5. 图片URL和访问测试"

# 检查图片URL格式
if echo "$PIE_RESPONSE" | grep -q "\"imageUrl\":\"http://$SERVER_IP:3000/images/"; then
    print_result "图片URL格式正确" "success"
    IMAGE_URL=$(echo "$PIE_RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    echo "  图片URL: $IMAGE_URL"
else
    print_result "图片URL格式正确" "fail"
fi

# 测试图片访问
if [ -n "$IMAGE_URL" ] && [ "$IMAGE_URL" != "null" ]; then
    if curl -s --head --connect-timeout 10 "$IMAGE_URL" | grep -q "200 OK"; then
        print_result "图片URL可访问" "success"
        # 获取文件大小
        SIZE=$(curl -s --head "$IMAGE_URL" | grep -i content-length | cut -d' ' -f2 | tr -d '\r')
        echo "  文件大小: $SIZE bytes"
    else
        print_result "图片URL可访问" "fail"
    fi
fi

print_section "6. 图片存储检查"

# 检查本地图片存储
IMAGE_COUNT=$(ls images/*.png 2>/dev/null | wc -l)
if [ "$IMAGE_COUNT" -gt 0 ]; then
    print_result "本地图片存储正常 ($IMAGE_COUNT 个文件)" "success"
    echo "  最新图片:"
    ls -lt images/*.png 2>/dev/null | head -3 | awk '{print "    " $9 " (" $5 " bytes)"}'
else
    print_result "本地图片存储正常" "fail"
fi

# 测试图片列表API
IMAGES_LIST=$(curl -s http://localhost:3000/api/images 2>/dev/null)
if echo "$IMAGES_LIST" | grep -q '"success":true'; then
    print_result "图片列表API正常" "success"
    TOTAL_IMAGES=$(echo "$IMAGES_LIST" | jq -r '.total' 2>/dev/null)
    echo "  API报告图片数量: $TOTAL_IMAGES"
else
    print_result "图片列表API正常" "fail"
fi

print_section "7. MCP服务集成测试"

# 检查SSE服务
if curl -s --connect-timeout 5 http://localhost:1122/sse > /dev/null; then
    print_result "MCP SSE服务正常" "success"
else
    print_result "MCP SSE服务正常" "fail"
fi

# 检查Streamable服务
if curl -s --connect-timeout 5 http://localhost:1123/mcp > /dev/null; then
    print_result "MCP Streamable服务正常" "success"
else
    print_result "MCP Streamable服务正常" "fail"
fi

print_section "8. 性能和稳定性测试"

# 并发测试
echo "执行并发图表生成测试..."
for i in {1..3}; do
    curl -s -X POST http://localhost:3000/api/gpt-vis \
      -H "Content-Type: application/json" \
      -d "{
        \"type\": \"pie\",
        \"data\": [
          {\"category\": \"测试$i\", \"value\": $((50 + i * 10))},
          {\"category\": \"其他\", \"value\": $((50 - i * 10))}
        ],
        \"title\": \"并发测试$i\"
      }" > /dev/null &
done

wait
sleep 2

# 检查并发生成结果
NEW_IMAGE_COUNT=$(ls images/*.png 2>/dev/null | wc -l)
if [ "$NEW_IMAGE_COUNT" -gt "$IMAGE_COUNT" ]; then
    print_result "并发图表生成测试" "success"
    echo "  新增图片: $((NEW_IMAGE_COUNT - IMAGE_COUNT)) 个"
else
    print_result "并发图表生成测试" "fail"
fi

print_section "9. 网络隔离验证"

# 检查是否真的没有外部网络依赖
echo "验证网络隔离..."
if netstat -tn 2>/dev/null | grep -E "(443|80)" | grep -v "127.0.0.1\|$SERVER_IP" > /dev/null; then
    print_result "网络完全隔离" "fail"
    echo "  检测到外部网络连接"
else
    print_result "网络完全隔离" "success"
    echo "  无外部网络依赖"
fi

print_section "测试结果汇总"

echo -e "总测试数: $total_tests"
echo -e "成功数: ${GREEN}$success_count${NC}"
echo -e "失败数: ${RED}$((total_tests - success_count))${NC}"
echo -e "成功率: $(( success_count * 100 / total_tests ))%"

print_section "本地化部署状态"

if [ $success_count -ge $((total_tests * 4 / 5)) ]; then
    echo -e "${GREEN}🎉 本地化部署成功！${NC}"
    echo ""
    echo "✅ 完全本地渲染能力"
    echo "✅ 无外部网络依赖"
    echo "✅ 支持多种图表类型"
    echo "✅ 图片本地存储和访问"
    echo "✅ MCP多传输方式支持"
else
    echo -e "${YELLOW}⚠️ 本地化部署需要优化${NC}"
fi

echo ""
echo "🌐 服务地址:"
echo "  - 渲染服务: http://$SERVER_IP:3000"
echo "  - 健康检查: http://$SERVER_IP:3000/health"
echo "  - 图表类型: http://$SERVER_IP:3000/api/chart-types"
echo "  - 图片列表: http://$SERVER_IP:3000/api/images"
echo "  - SSE服务: http://$SERVER_IP:1122/sse"
echo "  - Streamable服务: http://$SERVER_IP:1123/mcp"

echo ""
echo "🎯 Cherry Studio配置:"
echo "  服务器URL: http://$SERVER_IP:1122/sse"
echo "  传输方式: SSE"
echo "  完全本地化: 是"

echo ""
echo "📊 本地化特性:"
echo "  - 双重渲染引擎（@antv/gpt-vis-ssr + Chart.js）"
echo "  - 完全离线运行"
echo "  - 本地图片存储"
echo "  - 无外部API依赖"
echo "  - 支持stdio、SSE、Streamable传输"

if [ $success_count -lt $((total_tests * 4 / 5)) ]; then
    echo ""
    echo -e "${YELLOW}🔧 如果测试失败较多，请运行修复脚本:${NC}"
    echo "  sudo ./fix-local-canvas-complete.sh"
fi
