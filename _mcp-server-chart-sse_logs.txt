    at checkOptionUsage (node:internal/util/parse_args/parse_args:98:11)
    at node:internal/util/parse_args/parse_args:360:9
    at Array.forEach (<anonymous>)
    at parseArgs (node:internal/util/parse_args/parse_args:357:3)
    at Object.<anonymous> (/usr/local/lib/node_modules/@antv/mcp-server-chart/build/index.js:7:46)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12) {
  code: 'ERR_PARSE_ARGS_UNKNOWN_OPTION'
}
Node.js v18.20.8
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/x86_64/APKINDEX.tar.gz
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/x86_64/APKINDEX.tar.gz
OK: 14 MiB in 26 packages
changed 89 packages in 4s
17 packages are looking for funding
  run `npm fund` for details
MCP SSE服务器启动中...
node:internal/util/parse_args/parse_args:98
    throw new ERR_PARSE_ARGS_UNKNOWN_OPTION(
    ^
TypeError [ERR_PARSE_ARGS_UNKNOWN_OPTION]: Unknown option '--host'
    at new NodeError (node:internal/errors:405:5)
    at checkOptionUsage (node:internal/util/parse_args/parse_args:98:11)
    at node:internal/util/parse_args/parse_args:360:9
    at Array.forEach (<anonymous>)
    at parseArgs (node:internal/util/parse_args/parse_args:357:3)
    at Object.<anonymous> (/usr/local/lib/node_modules/@antv/mcp-server-chart/build/index.js:7:46)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12) {
  code: 'ERR_PARSE_ARGS_UNKNOWN_OPTION'
}
Node.js v18.20.8
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/x86_64/APKINDEX.tar.gz
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/x86_64/APKINDEX.tar.gz
OK: 14 MiB in 26 packages
changed 89 packages in 5s
17 packages are looking for funding
  run `npm fund` for details
MCP SSE服务器启动中...
node:internal/util/parse_args/parse_args:98
    throw new ERR_PARSE_ARGS_UNKNOWN_OPTION(
    ^
TypeError [ERR_PARSE_ARGS_UNKNOWN_OPTION]: Unknown option '--host'
    at new NodeError (node:internal/errors:405:5)
    at checkOptionUsage (node:internal/util/parse_args/parse_args:98:11)
    at node:internal/util/parse_args/parse_args:360:9
    at Array.forEach (<anonymous>)
    at parseArgs (node:internal/util/parse_args/parse_args:357:3)
    at Object.<anonymous> (/usr/local/lib/node_modules/@antv/mcp-server-chart/build/index.js:7:46)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12) {
  code: 'ERR_PARSE_ARGS_UNKNOWN_OPTION'
}
Node.js v18.20.8
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/x86_64/APKINDEX.tar.gz
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/x86_64/APKINDEX.tar.gz
OK: 14 MiB in 26 packages
changed 89 packages in 8s
17 packages are looking for funding
  run `npm fund` for details
MCP SSE服务器启动中...
node:internal/util/parse_args/parse_args:98
    throw new ERR_PARSE_ARGS_UNKNOWN_OPTION(
    ^
TypeError [ERR_PARSE_ARGS_UNKNOWN_OPTION]: Unknown option '--host'
    at new NodeError (node:internal/errors:405:5)
    at checkOptionUsage (node:internal/util/parse_args/parse_args:98:11)
    at node:internal/util/parse_args/parse_args:360:9
    at Array.forEach (<anonymous>)
    at parseArgs (node:internal/util/parse_args/parse_args:357:3)
    at Object.<anonymous> (/usr/local/lib/node_modules/@antv/mcp-server-chart/build/index.js:7:46)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12) {
  code: 'ERR_PARSE_ARGS_UNKNOWN_OPTION'
}
Node.js v18.20.8
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/x86_64/APKINDEX.tar.gz
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/x86_64/APKINDEX.tar.gz
OK: 14 MiB in 26 packages
