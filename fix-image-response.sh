#!/bin/bash

# 修复图片响应格式
# 让MCP服务返回图片URL而不是base64数据

set -e

echo "=== 修复图片响应格式 ==="

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-multi-transport" "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo "错误：找不到MCP部署目录"
    exit 1
fi

echo "使用工作目录: $WORK_DIR"
cd "$WORK_DIR"

# 备份原始配置
echo "1. 备份原始配置..."
cp docker-compose.yml docker-compose.yml.backup

# 更新渲染服务代码
echo "2. 更新渲染服务代码..."
cat > render-service/server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();

// 启用CORS
app.use(cors());
app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

// 获取服务器地址
function getServerUrl(req) {
  const protocol = req.get('x-forwarded-proto') || req.protocol;
  const host = req.get('host');
  return `${protocol}://${host}`;
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    // 生成图片URL
    const serverUrl = getServerUrl(req);
    const imageUrl = `${serverUrl}/images/${filename}`;
    
    // 同时提供base64（兼容性）
    const base64Url = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    console.log('图片URL:', imageUrl);
    
    res.json({
      success: true,
      resultObj: imageUrl,        // 主要返回URL
      imageUrl: imageUrl,         // 明确的URL字段
      base64Url: base64Url,       // 备用base64
      filename: filename,
      size: buffer.length
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr',
    imageCount: fs.readdirSync(imageDir).filter(f => f.endsWith('.png')).length
  });
});

// 静态文件服务 - 提供图片访问
app.use('/images', express.static(imageDir, {
  maxAge: '1d', // 缓存1天
  setHeaders: (res, path) => {
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Access-Control-Allow-Origin', '*');
  }
}));

// 图片列表API
app.get('/api/images', (req, res) => {
  try {
    const files = fs.readdirSync(imageDir)
      .filter(f => f.endsWith('.png'))
      .map(f => {
        const filepath = path.join(imageDir, f);
        const stats = fs.statSync(filepath);
        return {
          filename: f,
          url: `${getServerUrl(req)}/images/${f}`,
          size: stats.size,
          created: stats.birthtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));
    
    res.json({
      success: true,
      images: files,
      total: files.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

// 删除图片API
app.delete('/api/images/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filepath = path.join(imageDir, filename);
    
    if (fs.existsSync(filepath)) {
      fs.unlinkSync(filepath);
      res.json({
        success: true,
        message: `图片 ${filename} 已删除`
      });
    } else {
      res.status(404).json({
        success: false,
        errorMessage: '图片不存在'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log(`API端点: http://localhost:${PORT}/api/gpt-vis`);
  console.log(`图片访问: http://localhost:${PORT}/images/`);
  console.log(`图片列表: http://localhost:${PORT}/api/images`);
});
EOF

# 更新package.json以包含cors依赖
echo "3. 更新package.json..."
cat > render-service/package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service with URL response",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0",
    "cors": "^2.8.5"
  }
}
EOF

# 重启服务
echo "4. 重启渲染服务..."
docker compose restart gpt-vis-ssr

echo "等待服务重启..."
sleep 20

# 测试新的响应格式
echo "5. 测试新的响应格式..."
RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "苹果", "value": 30},
      {"category": "香蕉", "value": 25},
      {"category": "橙子", "value": 20},
      {"category": "葡萄", "value": 15},
      {"category": "其他", "value": 10}
    ],
    "title": "水果销量分布测试"
  }')

echo "响应结果:"
echo "$RESPONSE" | jq . 2>/dev/null || echo "$RESPONSE"

# 检查是否返回了URL
if echo "$RESPONSE" | grep -q '"imageUrl"'; then
    echo "✅ 成功返回图片URL"
    IMAGE_URL=$(echo "$RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    if [ "$IMAGE_URL" != "null" ] && [ -n "$IMAGE_URL" ]; then
        echo "图片URL: $IMAGE_URL"
        # 测试图片是否可访问
        if curl -s --head "$IMAGE_URL" | grep -q "200 OK"; then
            echo "✅ 图片URL可访问"
        else
            echo "❌ 图片URL无法访问"
        fi
    fi
else
    echo "❌ 未返回图片URL"
fi

echo ""
echo "6. 检查图片目录..."
echo "图片目录内容:"
ls -la images/ | head -10

echo ""
echo "7. 测试图片列表API..."
curl -s http://localhost:3000/api/images | jq . 2>/dev/null || curl -s http://localhost:3000/api/images

echo ""
echo "=== 修复完成 ==="
echo ""
echo "🎯 现在MCP服务将返回:"
echo "  - resultObj: 图片URL (主要字段)"
echo "  - imageUrl: 图片URL (明确字段)"
echo "  - base64Url: base64数据 (兼容性)"
echo ""
echo "🌐 图片访问地址:"
echo "  - 本地: http://localhost:3000/images/"
echo "  - 远程: http://YOUR_SERVER_IP:3000/images/"
echo ""
echo "📋 API端点:"
echo "  - 生成图表: POST /api/gpt-vis"
echo "  - 图片列表: GET /api/images"
echo "  - 删除图片: DELETE /api/images/{filename}"
echo ""
echo "🔧 Cherry Studio配置:"
echo "  确保可以访问 http://localhost:3000/images/ 目录"
echo "  如果是远程服务器，使用 http://YOUR_SERVER_IP:3000/images/"
