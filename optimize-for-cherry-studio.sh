#!/bin/bash

# 优化MCP服务以适配Cherry Studio
# 确保返回的图片可以在Cherry Studio中正确显示

set -e

echo "=== 优化MCP服务以适配Cherry Studio ==="

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-multi-transport" "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo "错误：找不到MCP部署目录"
    echo "请先运行部署脚本"
    exit 1
fi

echo "使用工作目录: $WORK_DIR"
cd "$WORK_DIR"

# 检查当前服务状态
echo "1. 检查当前服务状态..."
if ! docker ps | grep -q gpt-vis-ssr; then
    echo "❌ 渲染服务未运行，启动服务..."
    docker compose up -d
    sleep 30
fi

# 获取服务器IP
SERVER_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "localhost")
echo "检测到服务器IP: $SERVER_IP"

# 更新渲染服务以支持Cherry Studio
echo "2. 更新渲染服务配置..."
cat > render-service/server.js << EOF
const express = require('express');
const cors = require('cors');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();

// 配置CORS以支持Cherry Studio
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false
}));

app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

// 获取外部访问地址
function getExternalUrl(req) {
  // 优先使用环境变量中的外部地址
  const externalHost = process.env.EXTERNAL_HOST || '$SERVER_IP';
  const port = process.env.PORT || 3000;
  
  // 如果是localhost，尝试使用实际IP
  if (externalHost === 'localhost' || externalHost === '127.0.0.1') {
    return \`http://$SERVER_IP:\${port}\`;
  }
  
  return \`http://\${externalHost}:\${port}\`;
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = \`chart_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}.png\`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    // 生成外部可访问的URL
    const externalUrl = getExternalUrl(req);
    const imageUrl = \`\${externalUrl}/images/\${filename}\`;
    
    console.log('图表生成成功:', filename);
    console.log('外部访问URL:', imageUrl);
    
    // Cherry Studio优化的响应格式
    const response = {
      success: true,
      resultObj: imageUrl,           // Cherry Studio主要读取这个字段
      imageUrl: imageUrl,            // 明确的图片URL
      filename: filename,
      size: buffer.length,
      timestamp: new Date().toISOString(),
      // 为了兼容性，也提供base64
      base64: \`data:image/png;base64,\${buffer.toString('base64')}\`
    };
    
    res.json(response);
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  const externalUrl = getExternalUrl(req);
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr',
    externalUrl: externalUrl,
    imageCount: fs.readdirSync(imageDir).filter(f => f.endsWith('.png')).length,
    version: '1.0.0-cherry-optimized'
  });
});

// 静态文件服务 - 优化图片访问
app.use('/images', express.static(imageDir, {
  maxAge: '7d', // 缓存7天
  setHeaders: (res, path) => {
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cache-Control', 'public, max-age=604800'); // 7天缓存
  }
}));

// 图片预览页面
app.get('/preview/:filename', (req, res) => {
  const filename = req.params.filename;
  const filepath = path.join(imageDir, filename);
  
  if (!fs.existsSync(filepath)) {
    return res.status(404).send('图片不存在');
  }
  
  const externalUrl = getExternalUrl(req);
  const imageUrl = \`\${externalUrl}/images/\${filename}\`;
  
  res.send(\`
    <!DOCTYPE html>
    <html>
    <head>
        <title>图片预览 - \${filename}</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; text-align: center; }
            img { max-width: 100%; height: auto; border: 1px solid #ddd; }
            .info { margin: 20px 0; padding: 10px; background: #f5f5f5; }
        </style>
    </head>
    <body>
        <h1>图片预览</h1>
        <div class="info">
            <p><strong>文件名:</strong> \${filename}</p>
            <p><strong>直接链接:</strong> <a href="\${imageUrl}" target="_blank">\${imageUrl}</a></p>
        </div>
        <img src="\${imageUrl}" alt="\${filename}">
    </body>
    </html>
  \`);
});

// 图片列表API
app.get('/api/images', (req, res) => {
  try {
    const externalUrl = getExternalUrl(req);
    const files = fs.readdirSync(imageDir)
      .filter(f => f.endsWith('.png'))
      .map(f => {
        const filepath = path.join(imageDir, f);
        const stats = fs.statSync(filepath);
        return {
          filename: f,
          url: \`\${externalUrl}/images/\${f}\`,
          previewUrl: \`\${externalUrl}/preview/\${f}\`,
          size: stats.size,
          created: stats.birthtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));
    
    res.json({
      success: true,
      images: files,
      total: files.length,
      externalUrl: externalUrl
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(\`GPT-Vis-SSR服务运行在端口 \${PORT}\`);
  console.log(\`外部访问地址: http://$SERVER_IP:\${PORT}\`);
  console.log(\`健康检查: http://$SERVER_IP:\${PORT}/health\`);
  console.log(\`API端点: http://$SERVER_IP:\${PORT}/api/gpt-vis\`);
  console.log(\`图片访问: http://$SERVER_IP:\${PORT}/images/\`);
  console.log(\`图片列表: http://$SERVER_IP:\${PORT}/api/images\`);
});
EOF

# 更新Docker Compose配置
echo "3. 更新Docker Compose配置..."
cat > docker-compose.yml << EOF
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
      - EXTERNAL_HOST=$SERVER_IP
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        echo "安装系统依赖..."
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++ curl
        echo "安装npm依赖..."
        npm install
        echo "启动渲染服务..."
        npm start
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mcp-sse-server:
    image: node:18-alpine
    container_name: mcp-sse-server
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..30}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... (\$i/30)"
          sleep 2
        done
        echo "启动MCP SSE服务器..."
        mcp-server-chart --transport sse --port 1122
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

# 重启服务
echo "4. 重启服务..."
docker compose down
docker compose up -d

echo "等待服务启动..."
sleep 45

# 测试优化后的服务
echo "5. 测试优化后的服务..."
echo "健康检查:"
curl -s http://localhost:3000/health | jq . 2>/dev/null || curl -s http://localhost:3000/health

echo ""
echo "测试图表生成:"
RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "Cherry Studio", "value": 40},
      {"category": "Claude Desktop", "value": 30},
      {"category": "VSCode", "value": 20},
      {"category": "其他", "value": 10}
    ],
    "title": "MCP客户端使用分布"
  }')

echo "$RESPONSE" | jq . 2>/dev/null || echo "$RESPONSE"

# 提取图片URL并测试
IMAGE_URL=$(echo "$RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
if [ "$IMAGE_URL" != "null" ] && [ -n "$IMAGE_URL" ]; then
    echo ""
    echo "生成的图片URL: $IMAGE_URL"
    
    # 测试URL访问
    if curl -s --head "$IMAGE_URL" | grep -q "200 OK"; then
        echo "✅ 图片URL可访问"
    else
        echo "❌ 图片URL无法访问"
    fi
fi

echo ""
echo "=== 优化完成 ==="
echo ""
echo "🎯 Cherry Studio配置信息:"
echo "  服务器类型: MCP Server"
echo "  传输方式: SSE"
echo "  服务器URL: http://$SERVER_IP:1122/sse"
echo ""
echo "🌐 服务地址:"
echo "  渲染服务: http://$SERVER_IP:3000"
echo "  健康检查: http://$SERVER_IP:3000/health"
echo "  图片访问: http://$SERVER_IP:3000/images/"
echo "  图片列表: http://$SERVER_IP:3000/api/images"
echo ""
echo "📋 响应格式优化:"
echo "  ✅ resultObj 返回图片URL（Cherry Studio主要字段）"
echo "  ✅ imageUrl 明确的图片URL字段"
echo "  ✅ 外部可访问的完整URL"
echo "  ✅ CORS支持"
echo "  ✅ 图片缓存优化"
echo ""
echo "🔧 如果Cherry Studio仍显示base64:"
echo "1. 检查Cherry Studio的图片显示设置"
echo "2. 确认网络连接: ping $SERVER_IP"
echo "3. 在浏览器中测试图片URL"
echo "4. 检查防火墙设置: sudo ufw allow 3000"
echo ""
echo "📖 测试页面: http://$SERVER_IP:3000/api/images"
