# 文件路径修改示例

## 示例1：修改文件名

如果您的文件名不是默认的名称，请按以下方式修改：

**原始代码（第203-204行）：**
```python
inspection_report_path = os.path.join(script_dir, "检验报告.xlsx")
production_task_path = os.path.join(script_dir, "生产环节任务专项.xlsx")
```

**修改后：**
```python
inspection_report_path = os.path.join(script_dir, "我的检验报告2025.xlsx")
production_task_path = os.path.join(script_dir, "我的生产任务表.xlsx")
```

## 示例2：文件在子文件夹中

如果您的文件放在脚本目录下的子文件夹中：

```python
inspection_report_path = os.path.join(script_dir, "数据文件", "检验报告.xlsx")
production_task_path = os.path.join(script_dir, "数据文件", "生产环节任务专项.xlsx")
```

## 示例3：文件在上级目录

如果您的文件在脚本的上级目录：

```python
inspection_report_path = os.path.join(script_dir, "..", "检验报告.xlsx")
production_task_path = os.path.join(script_dir, "..", "生产环节任务专项.xlsx")
```

## 示例4：使用完整路径

如果您想使用完整的文件路径：

```python
inspection_report_path = r"D:\我的工作文件\2025年数据\检验报告.xlsx"
production_task_path = r"D:\我的工作文件\2025年数据\生产环节任务专项.xlsx"
```

## 示例5：修改输出路径

如果您想将结果保存到其他位置（第703-704行）：

**原始代码：**
```python
script_dir = os.path.dirname(os.path.abspath(__file__))
output_path = os.path.join(script_dir, output_filename)
```

**修改为保存到桌面：**
```python
output_dir = r"C:\Users\<USER>\Desktop"
output_path = os.path.join(output_dir, output_filename)
```

**修改为保存到指定文件夹：**
```python
output_dir = r"D:\分析结果"
output_path = os.path.join(output_dir, output_filename)
```

## 注意事项

1. **路径中的反斜杠**：在Windows中，路径使用反斜杠`\`，但在Python字符串中需要使用双反斜杠`\\`或在字符串前加`r`
2. **文件夹必须存在**：如果指定的文件夹不存在，程序会报错
3. **权限问题**：确保对指定的文件夹有读写权限
4. **中文路径**：确保路径中的中文字符正确无误
