#!/bin/bash

# 完全本地Canvas解决方案
# 解决Canvas依赖问题，确保完全本地部署

set -e

SERVER_IP="**************"
echo "=== 完全本地Canvas解决方案 ==="

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-all-transports" "/opt/mcp-multi-transport" "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo "错误：找不到MCP部署目录"
    exit 1
fi

echo "使用工作目录: $WORK_DIR"
cd "$WORK_DIR"

# 停止现有服务
echo "1. 停止现有服务..."
docker compose down

# 创建本地Canvas解决方案的package.json
echo "2. 创建优化的package.json..."
cat > render-service/package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service - Local Canvas Solution",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "canvas": "2.11.2",
    "chartjs-node-canvas": "^4.1.6",
    "chart.js": "^3.9.1"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}
EOF

# 创建本地Canvas渲染服务
echo "3. 创建本地Canvas渲染服务..."
cat > render-service/server.js << EOF
const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

const app = express();

// 配置外部IP
const EXTERNAL_IP = '$SERVER_IP';
const PORT = process.env.PORT || 3000;

console.log(\`配置外部IP地址: \${EXTERNAL_IP}\`);

// 配置CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false
}));

app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

// 生成外部可访问的URL
function getExternalImageUrl(filename) {
  return \`http://\${EXTERNAL_IP}:\${PORT}/images/\${filename}\`;
}

// 本地图表渲染函数
async function renderChartLocally(options) {
  try {
    // 首先尝试使用@antv/gpt-vis-ssr
    try {
      const { render } = require('@antv/gpt-vis-ssr');
      console.log('使用@antv/gpt-vis-ssr进行本地渲染...');
      const vis = await render(options);
      return vis.toBuffer();
    } catch (antvError) {
      console.log('@antv/gpt-vis-ssr不可用，使用Chart.js备用方案...');
      
      // 备用方案：使用Chart.js + Canvas
      const { ChartJSNodeCanvas } = require('chartjs-node-canvas');
      
      const width = 800;
      const height = 600;
      const chartJSNodeCanvas = new ChartJSNodeCanvas({ width, height });
      
      // 转换数据格式
      const chartConfig = convertToChartJS(options);
      
      console.log('使用Chart.js渲染图表...');
      const buffer = await chartJSNodeCanvas.renderToBuffer(chartConfig);
      return buffer;
    }
  } catch (error) {
    console.error('本地渲染失败:', error);
    throw error;
  }
}

// 将gpt-vis格式转换为Chart.js格式
function convertToChartJS(options) {
  const { type, data, title } = options;
  
  let chartType = type;
  let chartData = {};
  let chartOptions = {
    responsive: false,
    plugins: {
      title: {
        display: !!title,
        text: title || ''
      },
      legend: {
        display: true
      }
    }
  };
  
  switch (type) {
    case 'pie':
      chartType = 'pie';
      chartData = {
        labels: data.map(item => item.category),
        datasets: [{
          data: data.map(item => item.value),
          backgroundColor: [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
          ]
        }]
      };
      break;
      
    case 'column':
    case 'bar':
      chartType = 'bar';
      chartData = {
        labels: data.map(item => item.category),
        datasets: [{
          label: title || '数据',
          data: data.map(item => item.value),
          backgroundColor: '#36A2EB'
        }]
      };
      if (type === 'column') {
        chartOptions.indexAxis = 'x';
      } else {
        chartOptions.indexAxis = 'y';
      }
      break;
      
    case 'line':
      chartType = 'line';
      chartData = {
        labels: data.map(item => item.time || item.category),
        datasets: [{
          label: title || '数据',
          data: data.map(item => item.value),
          borderColor: '#36A2EB',
          backgroundColor: 'rgba(54, 162, 235, 0.1)',
          fill: false
        }]
      };
      break;
      
    case 'area':
      chartType = 'line';
      chartData = {
        labels: data.map(item => item.time || item.category),
        datasets: [{
          label: title || '数据',
          data: data.map(item => item.value),
          borderColor: '#36A2EB',
          backgroundColor: 'rgba(54, 162, 235, 0.3)',
          fill: true
        }]
      };
      break;
      
    default:
      // 默认使用柱状图
      chartType = 'bar';
      chartData = {
        labels: data.map(item => item.category || item.time || 'Unknown'),
        datasets: [{
          label: title || '数据',
          data: data.map(item => item.value),
          backgroundColor: '#36A2EB'
        }]
      };
  }
  
  return {
    type: chartType,
    data: chartData,
    options: chartOptions
  };
}

// 图表生成API
app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    if (!options.data || !Array.isArray(options.data)) {
      throw new Error('缺少有效的数据数组');
    }
    
    // 本地渲染图表
    const buffer = await renderChartLocally(options);
    
    const filename = \`chart_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}.png\`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    const imageUrl = getExternalImageUrl(filename);
    
    console.log('图表生成成功:', filename);
    console.log('外部图片URL:', imageUrl);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      imageUrl: imageUrl,
      filename: filename,
      size: buffer.length,
      timestamp: new Date().toISOString(),
      externalIP: EXTERNAL_IP,
      method: 'local-canvas'
    });
    
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  // 检查Canvas是否可用
  let canvasStatus = 'unknown';
  try {
    const { createCanvas } = require('canvas');
    const canvas = createCanvas(100, 100);
    canvasStatus = 'available';
  } catch (error) {
    canvasStatus = 'unavailable';
  }
  
  // 检查Chart.js是否可用
  let chartjsStatus = 'unknown';
  try {
    require('chartjs-node-canvas');
    chartjsStatus = 'available';
  } catch (error) {
    chartjsStatus = 'unavailable';
  }
  
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr-local',
    externalIP: EXTERNAL_IP,
    port: PORT,
    imageBaseUrl: \`http://\${EXTERNAL_IP}:\${PORT}/images/\`,
    imageCount: fs.readdirSync(imageDir).filter(f => f.endsWith('.png')).length,
    version: '1.0.0-local-canvas',
    renderMethod: 'local-only',
    dependencies: {
      canvas: canvasStatus,
      chartjs: chartjsStatus
    }
  });
});

// 静态文件服务
app.use('/images', express.static(imageDir, {
  maxAge: '1d',
  setHeaders: (res, path) => {
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cache-Control', 'public, max-age=86400');
  }
}));

// 图片列表API
app.get('/api/images', (req, res) => {
  try {
    const files = fs.readdirSync(imageDir)
      .filter(f => f.endsWith('.png'))
      .map(f => {
        const filepath = path.join(imageDir, f);
        const stats = fs.statSync(filepath);
        return {
          filename: f,
          url: getExternalImageUrl(f),
          size: stats.size,
          created: stats.birthtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));
    
    res.json({
      success: true,
      images: files,
      total: files.length,
      externalIP: EXTERNAL_IP,
      baseUrl: \`http://\${EXTERNAL_IP}:\${PORT}/images/\`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(\`GPT-Vis-SSR本地服务运行在端口 \${PORT}\`);
  console.log(\`外部访问地址: http://\${EXTERNAL_IP}:\${PORT}\`);
  console.log(\`健康检查: http://\${EXTERNAL_IP}:\${PORT}/health\`);
  console.log(\`API端点: http://\${EXTERNAL_IP}:\${PORT}/api/gpt-vis\`);
  console.log(\`完全本地渲染，无外部依赖\`);
});
EOF

# 创建预安装脚本
echo "4. 创建预安装脚本..."
cat > render-service/preinstall.sh << 'EOF'
#!/bin/sh

echo "=== Canvas依赖预安装脚本 ==="

# 更新包管理器
echo "更新包管理器..."
apk update

# 安装编译工具
echo "安装编译工具..."
apk add --no-cache \
  build-base \
  cairo-dev \
  pango-dev \
  jpeg-dev \
  giflib-dev \
  librsvg-dev \
  pixman-dev \
  pkgconfig \
  python3 \
  make \
  g++ \
  curl \
  git

# 设置npm配置
echo "设置npm配置..."
npm config set unsafe-perm true
npm config set registry https://registry.npmjs.org/

# 清理缓存
echo "清理npm缓存..."
npm cache clean --force

echo "预安装完成"
EOF

chmod +x render-service/preinstall.sh

# 更新Docker Compose配置
echo "5. 更新Docker Compose配置..."
cat > docker-compose.yml << EOF
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:16-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
      - EXTERNAL_IP=$SERVER_IP
      - NPM_CONFIG_UNSAFE_PERM=true
      - PYTHONUNBUFFERED=1
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        echo "运行预安装脚本..."
        sh preinstall.sh
        echo "安装npm依赖..."
        npm install --verbose
        echo "验证Canvas安装..."
        node -e "
          try {
            const { createCanvas } = require('canvas');
            const canvas = createCanvas(100, 100);
            console.log('✅ Canvas安装成功');
          } catch (error) {
            console.log('❌ Canvas安装失败:', error.message);
            process.exit(1);
          }
        "
        echo "验证Chart.js安装..."
        node -e "
          try {
            const { ChartJSNodeCanvas } = require('chartjs-node-canvas');
            console.log('✅ Chart.js安装成功');
          } catch (error) {
            console.log('❌ Chart.js安装失败:', error.message);
            process.exit(1);
          }
        "
        echo "启动渲染服务..."
        npm start
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 180s

  # SSE传输方式
  mcp-sse-server:
    image: node:16-alpine
    container_name: mcp-sse-server
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl git
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..90}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... (\$i/90)"
          sleep 2
        done
        echo "启动MCP SSE服务器..."
        mcp-server-chart --transport sse --port 1122
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

  # Streamable传输方式
  mcp-streamable-server:
    image: node:16-alpine
    container_name: mcp-streamable-server
    working_dir: /app
    ports:
      - "1123:1123"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl git
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..90}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... (\$i/90)"
          sleep 2
        done
        echo "启动MCP Streamable服务器..."
        mcp-server-chart --transport streamable --port 1123
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

# 确保图片目录存在
mkdir -p images

# 启动服务
echo "6. 启动完全本地化服务..."
docker compose up -d

echo "等待服务启动（这可能需要几分钟来编译Canvas）..."
sleep 120

# 检查服务状态
echo "7. 检查服务状态..."
docker compose ps

echo ""
echo "8. 查看服务日志..."
echo "--- 渲染服务日志 (最近40行) ---"
docker compose logs --tail=40 gpt-vis-ssr

echo ""
echo "9. 测试本地渲染服务..."
echo "健康检查:"
for i in {1..10}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务响应正常"
        HEALTH_RESPONSE=$(curl -s http://localhost:3000/health)
        echo "$HEALTH_RESPONSE" | jq . 2>/dev/null || echo "$HEALTH_RESPONSE"
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/10)"
        sleep 20
    fi
done

echo ""
echo "测试本地图表生成:"
RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "本地Canvas", "value": 60},
      {"category": "Chart.js备用", "value": 40}
    ],
    "title": "完全本地渲染方案"
  }')

echo "$RESPONSE" | jq . 2>/dev/null || echo "$RESPONSE"

if echo "$RESPONSE" | grep -q '"success":true'; then
    echo "✅ 本地图表生成测试成功"
    IMAGE_URL=$(echo "$RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    if [ "$IMAGE_URL" != "null" ]; then
        echo "图片URL: $IMAGE_URL"
        # 验证图片是否可访问
        if curl -s --head "$IMAGE_URL" | grep -q "200 OK"; then
            echo "✅ 图片URL可访问"
        else
            echo "❌ 图片URL无法访问"
        fi
    fi
else
    echo "❌ 本地图表生成测试失败"
fi

echo ""
echo "=== 完全本地化部署完成 ==="
echo ""
echo "🎯 本地化特性:"
echo "  ✅ 完全本地Canvas渲染（无外部依赖）"
echo "  ✅ Chart.js作为备用渲染引擎"
echo "  ✅ 支持多种图表类型（饼图、柱状图、折线图、面积图）"
echo "  ✅ 本地图片存储和访问"
echo "  ✅ 完整的错误处理和降级机制"
echo ""
echo "🌐 服务地址:"
echo "  - 渲染服务: http://$SERVER_IP:3000"
echo "  - SSE服务: http://$SERVER_IP:1122/sse"
echo "  - Streamable服务: http://$SERVER_IP:1123/mcp"
echo ""
echo "📋 渲染引擎:"
echo "  - 主引擎: @antv/gpt-vis-ssr (如果可用)"
echo "  - 备用引擎: Chart.js + Canvas"
echo "  - 存储: 完全本地文件系统"
echo ""
echo "🔍 如果问题仍然存在:"
echo "  1. 查看详细日志: docker compose logs gpt-vis-ssr"
echo "  2. 检查Canvas状态: curl http://$SERVER_IP:3000/health"
echo "  3. 重新构建: docker compose down && docker compose up -d --build"
