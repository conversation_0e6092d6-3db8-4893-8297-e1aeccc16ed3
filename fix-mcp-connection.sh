#!/bin/bash

# 修复MCP连接问题
# 解决图表生成服务连接失败的问题

set -e

echo "=== 修复MCP连接问题 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo "错误：找不到MCP部署目录"
    echo "请先运行部署脚本"
    exit 1
fi

echo "使用工作目录: $WORK_DIR"
cd "$WORK_DIR"

echo "1. 停止现有服务..."
docker compose down

echo "2. 清理Docker网络..."
docker network prune -f

echo "3. 重新创建优化的Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        echo "安装系统依赖..."
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++ curl
        echo "安装npm依赖..."
        npm install
        echo "启动渲染服务..."
        npm start
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mcp-chart-server:
    image: node:18-alpine
    container_name: mcp-chart-server
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..30}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... ($i/30)"
          sleep 2
        done
        echo "测试渲染服务连接..."
        curl -s http://gpt-vis-ssr:3000/health || echo "渲染服务连接测试失败"
        echo "启动MCP服务器..."
        if command -v mcp-server-chart >/dev/null 2>&1; then
          mcp-server-chart --transport sse --port 1122
        else
          npx @antv/mcp-server-chart --transport sse --port 1122
        fi
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1122/sse"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

echo "4. 确保渲染服务目录存在..."
if [ ! -d "render-service" ]; then
    mkdir -p render-service
    cd render-service
    
    cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0"
  }
}
EOF

    cat > server.js << 'EOF'
const express = require('express');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();
app.use(express.json({ limit: '10mb' }));

// 添加CORS支持
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    // 验证请求数据
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr'
  });
});

app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log(`API端点: http://localhost:${PORT}/api/gpt-vis`);
});
EOF

    cd ..
fi

echo "5. 确保图片目录存在..."
mkdir -p images

echo "6. 启动服务..."
docker compose up -d

echo "7. 等待服务启动..."
sleep 30

echo "8. 检查服务状态..."
docker compose ps

echo "9. 检查服务健康状态..."
echo "渲染服务健康检查:"
for i in {1..10}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务健康"
        curl -s http://localhost:3000/health | jq . 2>/dev/null || curl -s http://localhost:3000/health
        break
    else
        echo "⏳ 等待渲染服务... ($i/10)"
        sleep 5
    fi
done

echo ""
echo "MCP服务健康检查:"
for i in {1..10}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✅ MCP服务健康"
        break
    else
        echo "⏳ 等待MCP服务... ($i/10)"
        sleep 5
    fi
done

echo ""
echo "10. 测试图表生成..."
curl -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "苹果", "value": 30},
      {"category": "香蕉", "value": 25},
      {"category": "橙子", "value": 20},
      {"category": "葡萄", "value": 15},
      {"category": "其他", "value": 10}
    ],
    "title": "水果销量分布"
  }' | head -c 200

echo ""
echo ""
echo "=== 修复完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - 渲染服务健康检查: http://localhost:3000/health"
echo "  - MCP服务: http://localhost:1122/sse"
echo ""
echo "Cherry Studio配置:"
echo "  SSE URL: http://localhost:1122/sse"
echo ""
echo "如果问题仍然存在，请运行诊断脚本:"
echo "  sudo ./diagnose-mcp-connection.sh"
