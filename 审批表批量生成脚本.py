# generate_approvals.py (v19 - 最终版，带文件序号和Excel右下角序号-适合A4打印-右对齐)

import os
import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Alignment
import re
from decimal import Decimal, ROUND_HALF_UP

# --- 配置部分 ---
SOURCE_EXCEL_PATH = "data.xlsx"
TEMPLATE_EXCEL_PATH = "template.xlsx"
OUTPUT_DIRECTORY = "output"

DISTRICT_CODE_MAP = {
    'th': '天河区局', 'yx': '越秀区局', 'ns': '南沙区局',
    'lw': '荔湾区局', 'zc': '增城区局', 'hp': '黄埔区局',
    'hd': '花都区局', 'hz': '海珠区局', 'py': '番禺区局',
    'ch': '从化区局', 'by': '白云区局'
}
# --- 配置结束 ---

def write_to_cell(ws, cell_coord, value):
    """安全地向一个单元格写入数据,能自动处理合并单元格。"""
    merged_range_str = None
    for r in ws.merged_cells.ranges:
        if cell_coord in r:
            merged_range_str = str(r)
            break
    
    if merged_range_str:
        top_left_cell_coord = merged_range_str.split(':')[0]
        ws.unmerge_cells(merged_range_str)
        ws[top_left_cell_coord] = value
        ws.merge_cells(merged_range_str)
    else:
        ws[cell_coord] = value
# [1]

def convert_to_chinese_currency(n):
    """将数字金额转换为中文大写金融字符串。"""
    # (代码与上一版相同,为简洁此处省略,请在您的文件中保留)
    if not isinstance(n, Decimal):
        n = Decimal(str(n))

    n = n.quantize(Decimal('0.01'))

    units = ['', '拾', '佰', '仟']
    big_units = ['', '万', '亿']
    chars = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    
    integer_part, decimal_part = str(n).split('.')
    integer_part_val = int(integer_part)
    decimal_part_val = int(decimal_part)

    if integer_part_val == 0 and decimal_part_val == 0:
        return "零元整"

    result = ''
    if integer_part_val > 0:
        s = str(integer_part)
        s_len = len(s)
        for i, digit_char in enumerate(s):
            d = int(digit_char)
            unit_index = s_len - i - 1
            if d == 0:
                if unit_index % 4 == 0:
                    result += big_units[unit_index // 4]
                if i < s_len - 1 and int(s[i+1]) != 0 and result[-1] not in ['零', '万', '亿']:
                    result += '零'
            else:
                result += chars[d] + units[unit_index % 4]
                if unit_index % 4 == 0:
                    result += big_units[unit_index // 4]
        result = re.sub(r'零+', '零', result)
        result = re.sub(r'零([万亿])', r'\1', result)
        result = re.sub(r'亿万', '亿', result)
        result = result.rstrip('零')
        result += '元'
    
    if decimal_part_val == 0:
        result += '整'
    else:
        jiao = decimal_part_val // 10
        fen = decimal_part_val % 10
        if jiao > 0:
            result += chars[jiao] + '角'
        elif integer_part_val > 0 and fen > 0:
            result += '零'
        if fen > 0:
            result += chars[fen] + '分'
    return result
# [1]

def sanitize_filename(filename):
    """清理字符串,使其成为一个有效的文件名。"""
    return re.sub(r'[\\/:\*\?"<>\|]', '_', str(filename)).strip(' .')
# [1]

def main():
    """主函数,执行批量生成审批表的任务"""
    if not os.path.exists(OUTPUT_DIRECTORY):
        os.makedirs(OUTPUT_DIRECTORY)
        print(f"创建输出目录: {OUTPUT_DIRECTORY}")

    try:
        df = pd.read_excel(SOURCE_EXCEL_PATH, dtype={'抽样编号': str, '抽样单位': str})
        print(f"成功加载数据源: {SOURCE_EXCEL_PATH}")
    except FileNotFoundError:
        print(f"错误: 数据源文件 '{SOURCE_EXCEL_PATH}' 未找到。")
        return
    
    # **核心修改 1**: 计算序号需要补零的位数
    total_rows = len(df)
    padding_width = len(str(total_rows))

    for index, row in df.iterrows():
        try:
            wb = load_workbook(TEMPLATE_EXCEL_PATH)
            ws = wb.active

            # --- 数据填充 ---
            
            # 1. 基本信息
            dt = pd.to_datetime(row['日期'])
            date_str = f"{dt.year}年{dt.month}月{dt.day}日"
            write_to_cell(ws, 'E3', date_str)
            write_to_cell(ws, 'J4', row['经办人'])

            # 2. 序号和摘要
            write_to_cell(ws, 'C7', 1)
            write_to_cell(ws, 'C8', 2)
            
            sample_id = row.get('抽样编号', '')
            try:
                batch_count = int(row.get('批数', 0))
            except (ValueError, TypeError):
                batch_count = 0

            if batch_count > 1:
                display_sample_id = f"{sample_id}等"
            else:
                display_sample_id = sample_id
                
            write_to_cell(ws, 'D7', f"2025年药品购样（抽样编号：{display_sample_id}）")
            
            district_code = str(row.get('抽样单位', '')).lower()
            district_name = DISTRICT_CODE_MAP.get(district_code, district_code) 
            write_to_cell(ws, 'D8', f"批数：{row.get('批数', '')}批  抽样单位：{district_name}")

            # 3. 金额和附件
            amount = Decimal(row.get('付款金额', 0)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            amount_in_cents = int(amount * 100)
            amount_str_padded = f'{amount_in_cents:08d}'

            def fill_amount_row(cells_list):
                found_first_digit = False
                for i, digit_char in enumerate(amount_str_padded):
                    digit = int(digit_char)
                    if not found_first_digit and digit != 0:
                        found_first_digit = True
                        if i > 0:
                            previous_cell = cells_list[i - 1]
                            write_to_cell(ws, previous_cell, '¥')
                    if found_first_digit:
                        current_cell = cells_list[i]
                        write_to_cell(ws, current_cell, digit)

            amount_cells_r7 = ['F7', 'G7', 'H7', 'I7', 'J7', 'K7', 'L7', 'M7']
            fill_amount_row(amount_cells_r7)
            write_to_cell(ws, 'N7', row.get('附件张数', ''))

            # 4. 合计
            chinese_amount = convert_to_chinese_currency(amount)
            write_to_cell(ws, 'E10', chinese_amount)

            amount_cells_r10 = ['F10', 'G10', 'H10', 'I10', 'J10', 'K10', 'L10', 'M10']
            fill_amount_row(amount_cells_r10)

            # 5. 生成序号并写入右下角 (**新增功能**)
            # 生成带前导零的序号
            serial_number = f"{index + 1:0{padding_width}d}"
            # 将序号写入Excel右下角 (Q17单元格，适合A4纸打印)
            write_to_cell(ws, 'Q17', serial_number)
            # 设置序号为右对齐
            ws['Q17'].alignment = Alignment(horizontal='right')

            # 6. 文件保存
            company_name = row.get('收款单位', '未知单位')
            file_sample_id = row.get('抽样编号', f'行{index+2}')
            sanitized_company = sanitize_filename(company_name)
            sanitized_id = sanitize_filename(file_sample_id)

            # 构造新的带序号的文件名
            output_filename = f"{serial_number}_支付审批单_{sanitized_company}_{sanitized_id}.xlsx"
            
            output_path = os.path.join(OUTPUT_DIRECTORY, output_filename)
            wb.save(output_path)
            
            print(f"成功生成文件: {output_filename}")

        except Exception as e:
            print(f"处理第 {index + 2} 行数据时发生严重错误 (收款单位: {row.get('收款单位', 'N/A')}) - 错误: {e}")
            import traceback
            traceback.print_exc()

    print("\n所有文件生成完毕。")

if __name__ == "__main__":
    main()
