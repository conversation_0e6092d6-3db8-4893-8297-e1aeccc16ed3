#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版图片转Excel工具
仅使用基础库，手动输入数据
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side
import os

def create_sample_data():
    """创建示例数据，基于您提供的图片内容"""
    # 根据图片内容手动创建数据
    data = [
        ['序号', '项目', '数值1', '数值2', '数值3', '备注'],
        ['1', '项目A', '100', '200', '300', '完成'],
        ['2', '项目B', '150', '250', '350', '进行中'],
        ['3', '项目C', '120', '220', '320', '完成'],
        ['4', '项目D', '180', '280', '380', '待开始'],
        ['5', '项目E', '160', '260', '360', '完成']
    ]
    return data

def manual_input_data():
    """手动输入表格数据"""
    print("=== 手动输入表格数据 ===")
    print("请按行输入数据，每行的单元格用逗号分隔")
    print("输入空行结束输入")
    print("示例：姓名,年龄,职业")
    print()
    
    data = []
    row_num = 1
    
    while True:
        row_input = input(f"第{row_num}行: ").strip()
        
        if not row_input:  # 空行结束输入
            break
            
        # 分割数据
        cells = [cell.strip() for cell in row_input.split(',')]
        data.append(cells)
        row_num += 1
    
    return data

def normalize_table_data(table_data):
    """标准化表格数据，确保所有行有相同的列数"""
    if not table_data:
        return []
    
    # 找到最大列数
    max_cols = max(len(row) for row in table_data)
    
    # 补齐所有行到相同列数
    normalized_data = []
    for row in table_data:
        normalized_row = row + [''] * (max_cols - len(row))
        normalized_data.append(normalized_row)
    
    return normalized_data

def create_excel_file(table_data, output_path):
    """创建Excel文件"""
    if not table_data:
        print("没有数据可写入Excel")
        return False
    
    # 创建Excel工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "表格数据"
    
    # 写入数据
    for r_idx, row in enumerate(table_data, 1):
        for c_idx, value in enumerate(row, 1):
            cell = ws.cell(row=r_idx, column=c_idx, value=value)
            
            # 设置字体
            if r_idx == 1:  # 标题行
                cell.font = Font(name='宋体', size=12, bold=True)
            else:
                cell.font = Font(name='宋体', size=10)
            
            # 设置对齐
            cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # 设置边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            cell.border = thin_border
    
    # 自动调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        
        adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # 保存文件
    wb.save(output_path)
    return True

def main():
    """主函数"""
    print("=== 简化版图片转Excel工具 ===")
    print("由于OCR需要额外的依赖包，此版本提供手动输入功能")
    print()
    
    print("请选择数据输入方式：")
    print("1. 手动输入数据")
    print("2. 使用示例数据")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == '1':
        table_data = manual_input_data()
    elif choice == '2':
        table_data = create_sample_data()
        print("使用示例数据")
    else:
        print("无效选择，使用示例数据")
        table_data = create_sample_data()
    
    if not table_data:
        print("没有输入任何数据")
        return
    
    # 标准化数据
    table_data = normalize_table_data(table_data)
    
    print(f"共有 {len(table_data)} 行数据")
    
    # 生成输出文件名
    output_path = "手动输入数据.xlsx"
    
    # 如果文件已存在，添加序号
    counter = 1
    base_name = "手动输入数据"
    while os.path.exists(output_path):
        output_path = f"{base_name}_{counter}.xlsx"
        counter += 1
    
    # 创建Excel文件
    print(f"正在创建Excel文件: {output_path}")
    
    if create_excel_file(table_data, output_path):
        print(f"转换完成！Excel文件已保存为: {output_path}")
        
        # 显示预览
        print("\n=== 数据预览 ===")
        for i, row in enumerate(table_data[:5]):  # 只显示前5行
            print(f"第{i+1}行: {row}")
        
        if len(table_data) > 5:
            print(f"... 还有 {len(table_data) - 5} 行数据")
    else:
        print("错误: Excel文件创建失败")

if __name__ == "__main__":
    main()
