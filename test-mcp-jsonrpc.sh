#!/bin/bash

# 测试MCP JSON-RPC协议

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "========================================"
echo "  MCP JSON-RPC 协议测试"
echo "========================================"
echo

# 1. 测试直接连接到MCP服务器
log_step "1. 测试直接连接到MCP Streamable服务器"

echo "测试 initialize 方法..."
response=$(curl -s -X POST http://192.168.50.105:1123 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {},
      "clientInfo": {
        "name": "test-client",
        "version": "1.0.0"
      }
    }
  }' 2>/dev/null)

if echo "$response" | jq . >/dev/null 2>&1; then
    log_info "✓ 直接连接成功，返回有效JSON"
    echo "$response" | jq .
else
    log_error "✗ 直接连接失败或返回无效JSON"
    echo "响应: $response"
fi
echo

# 2. 测试通过nginx代理
log_step "2. 测试通过nginx代理连接"

echo "测试 initialize 方法..."
response=$(curl -s -X POST http://192.168.50.105/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {},
      "clientInfo": {
        "name": "test-client",
        "version": "1.0.0"
      }
    }
  }' 2>/dev/null)

if echo "$response" | jq . >/dev/null 2>&1; then
    log_info "✓ 通过nginx代理连接成功"
    echo "$response" | jq .
else
    log_error "✗ 通过nginx代理连接失败"
    echo "响应: $response"
fi
echo

# 3. 测试 tools/list 方法
log_step "3. 测试 tools/list 方法"

echo "测试获取工具列表..."
response=$(curl -s -X POST http://192.168.50.105/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/list",
    "params": {}
  }' 2>/dev/null)

if echo "$response" | jq . >/dev/null 2>&1; then
    log_info "✓ tools/list 方法成功"
    echo "$response" | jq .
else
    log_error "✗ tools/list 方法失败"
    echo "响应: $response"
fi
echo

# 4. 检查服务器状态
log_step "4. 检查服务器状态"

echo "检查容器状态..."
docker-compose ps | grep mcp-server-chart

echo
echo "检查MCP服务器日志..."
docker-compose logs --tail=5 mcp-server-chart-streamable
echo

# 5. 生成LobeChat配置建议
log_step "5. LobeChat配置建议"

echo "基于测试结果，建议的LobeChat配置："
echo

if curl -s http://192.168.50.105:1123 >/dev/null 2>&1; then
    echo "方式一：直接连接MCP服务器端口"
    echo '{'
    echo '  "name": "Chart Generator",'
    echo '  "url": "http://192.168.50.105:1123",'
    echo '  "type": "streamable-http"'
    echo '}'
    echo
fi

if curl -s http://192.168.50.105/mcp >/dev/null 2>&1; then
    echo "方式二：通过nginx代理连接"
    echo '{'
    echo '  "name": "Chart Generator",'
    echo '  "url": "http://192.168.50.105/mcp",'
    echo '  "type": "streamable-http"'
    echo '}'
    echo
fi

echo "========================================"
echo "  测试完成"
echo "========================================"
