#!/bin/bash

# MCP Chart Server 私有化部署脚本
# 包含 GPT-Vis-SSR 服务的完整部署方案

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_error "此脚本仅支持 Linux 系统"
        exit 1
    fi
    
    # 检查是否为 root 用户或有 sudo 权限
    if [[ $EUID -eq 0 ]]; then
        SUDO=""
    elif command -v sudo >/dev/null 2>&1; then
        SUDO="sudo"
        log_info "检测到 sudo，将使用 sudo 执行特权命令"
    else
        log_error "需要 root 权限或 sudo 权限"
        exit 1
    fi
    
    # 检查必要的命令
    local required_commands=("curl" "wget" "git")
    for cmd in "${required_commands[@]}"; do
        if ! command -v $cmd >/dev/null 2>&1; then
            log_error "缺少必要的命令: $cmd"
            exit 1
        fi
    done
    
    log_info "系统要求检查通过"
}

# 安装 Docker 和 Docker Compose
install_docker() {
    log_step "安装 Docker 和 Docker Compose..."
    
    if command -v docker >/dev/null 2>&1; then
        log_info "Docker 已安装，跳过安装步骤"
    else
        log_info "安装 Docker..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        $SUDO sh get-docker.sh
        $SUDO usermod -aG docker $USER
        rm get-docker.sh
    fi
    
    if command -v docker-compose >/dev/null 2>&1; then
        log_info "Docker Compose 已安装，跳过安装步骤"
    else
        log_info "安装 Docker Compose..."
        $SUDO curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        $SUDO chmod +x /usr/local/bin/docker-compose
    fi
    
    # 启动 Docker 服务
    $SUDO systemctl enable docker
    $SUDO systemctl start docker
    
    log_info "Docker 安装完成"
}

# 安装 Node.js (用于非 Docker 部署)
install_nodejs() {
    log_step "安装 Node.js..."
    
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [[ $node_version -ge 18 ]]; then
            log_info "Node.js 版本满足要求，跳过安装"
            return
        fi
    fi
    
    log_info "安装 Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | $SUDO -E bash -
    $SUDO apt-get install -y nodejs
    
    log_info "Node.js 安装完成"
}

# 创建项目目录结构
create_project_structure() {
    log_step "创建项目目录结构..."
    
    local project_dir="${1:-mcp-chart-private}"
    
    if [[ -d "$project_dir" ]]; then
        log_warn "目录 $project_dir 已存在，是否删除并重新创建? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            rm -rf "$project_dir"
        else
            log_error "部署取消"
            exit 1
        fi
    fi
    
    mkdir -p "$project_dir"/{gpt-vis-ssr,charts-storage,scripts,config}
    cd "$project_dir"
    
    log_info "项目目录结构创建完成: $(pwd)"
}

# 创建 GPT-Vis-SSR 服务文件
create_gpt_vis_ssr_files() {
    log_step "创建 GPT-Vis-SSR 服务文件..."
    
    # 创建 package.json
    cat > gpt-vis-ssr/package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "Server-side rendering service for GPT-Vis",
  "main": "index.js",
  "scripts": {
    "start": "node index.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "^0.2.1",
    "express": "^4.18.2",
    "fs-extra": "^11.1.1",
    "uuid": "^9.0.0"
  }
}
EOF

    # 创建 index.js
    cat > gpt-vis-ssr/index.js << 'EOF'
const express = require('express')
const { render } = require('@antv/gpt-vis-ssr')
const fs = require('fs-extra')
const path = require('path')
const { v4: uuidv4 } = require('uuid')

const app = express()
const port = process.env.PORT || 3000
const publicDir = path.join(__dirname, 'public')
const imagesDir = path.join(publicDir, 'images')

// 确保目录存在
fs.ensureDirSync(imagesDir)

app.use(express.json({ limit: '10mb' }))
app.use('/images', express.static(imagesDir))

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'gpt-vis-ssr' })
})

// 图表渲染接口
app.post('/api/gpt-vis', async (req, res) => {
  try {
    const options = req.body
    
    // 验证必要的参数
    if (!options || !options.type || !options.data) {
      return res.status(400).json({
        success: false,
        errorMessage: '缺少必要的参数: type 或 data'
      })
    }

    console.log('渲染图表请求:', JSON.stringify(options, null, 2))

    // 渲染图表
    const vis = await render(options)
    const buffer = await vis.toBuffer()

    // 生成唯一文件名并保存图片
    const filename = `${uuidv4()}.png`
    const filePath = path.join(imagesDir, filename)
    await fs.writeFile(filePath, buffer)

    // 构建图片URL
    const host = req.get('host')
    const protocol = req.protocol
    const imageUrl = `${protocol}://${host}/images/${filename}`

    console.log('图表渲染成功:', imageUrl)

    res.json({
      success: true,
      resultObj: imageUrl
    })
  } catch (error) {
    console.error('渲染图表时出错:', error)
    res.status(500).json({
      success: false,
      errorMessage: `渲染图表失败: ${error.message}`
    })
  }
})

app.listen(port, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR 服务运行在 http://0.0.0.0:${port}`)
})
EOF

    log_info "GPT-Vis-SSR 服务文件创建完成"
}

# 创建 Docker Compose 文件
create_docker_compose_file() {
    log_step "创建 Docker Compose 配置..."

    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # GPT-Vis-SSR 图表渲染服务
  gpt-vis-ssr:
    build: ./gpt-vis-ssr
    container_name: gpt-vis-ssr
    ports:
      - "3000:3000"
    volumes:
      - ./charts-storage:/app/public/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MCP Chart Server
  mcp-server-chart:
    image: node:18-alpine
    container_name: mcp-server-chart
    ports:
      - "1122:1122"
    environment:
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
      - NODE_ENV=production
    volumes:
      - ./mcp-server-chart:/app
    working_dir: /app
    command: >
      sh -c "
        npm install -g @antv/mcp-server-chart &&
        mcp-server-chart --transport sse --port 1122
      "
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./charts-storage:/usr/share/nginx/html/images
    depends_on:
      - mcp-server-chart
      - gpt-vis-ssr
    restart: unless-stopped

volumes:
  charts_storage:
    driver: local
EOF

    log_info "Docker Compose 配置创建完成"
}

# 创建 Nginx 配置
create_nginx_config() {
    log_step "创建 Nginx 配置..."

    cat > nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # 上游服务器配置
    upstream mcp_server {
        server mcp-server-chart:1122;
    }

    upstream gpt_vis_ssr {
        server gpt-vis-ssr:3000;
    }

    server {
        listen 80;
        server_name localhost;

        # MCP Server Chart 服务
        location /sse {
            proxy_pass http://mcp_server/sse;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # SSE特定配置
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 24h;
        }

        location /mcp {
            proxy_pass http://mcp_server/mcp;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }

        # GPT-Vis-SSR 服务
        location /api/gpt-vis {
            proxy_pass http://gpt_vis_ssr/api/gpt-vis;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 增加超时时间，因为图表渲染可能需要较长时间
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 图片静态文件服务
        location /images/ {
            alias /usr/share/nginx/html/images/;
            expires 7d;
            add_header Cache-Control "public, immutable";
        }

        # 健康检查
        location /health {
            proxy_pass http://gpt_vis_ssr/health;
            proxy_set_header Host $host;
        }

        # 默认页面
        location / {
            return 200 'MCP Chart Server with GPT-Vis-SSR is running!';
            add_header Content-Type text/plain;
        }
    }
}
EOF

    log_info "Nginx 配置创建完成"
}

# 显示部署选项菜单
show_deployment_menu() {
    echo
    log_step "选择部署方式:"
    echo "1) Docker Compose 部署 (推荐)"
    echo "2) 分离部署 (手动管理)"
    echo "3) systemd 服务部署"
    echo "4) 退出"
    echo
    read -p "请选择 (1-4): " choice

    case $choice in
        1) deploy_with_docker_compose ;;
        2) deploy_separate ;;
        3) deploy_with_systemd ;;
        4) exit 0 ;;
        *) log_error "无效选择"; show_deployment_menu ;;
    esac
}

# Docker Compose 部署
deploy_with_docker_compose() {
    log_step "开始 Docker Compose 部署..."

    # 创建 Docker Compose 文件
    create_docker_compose_file

    # 创建 Nginx 配置
    create_nginx_config

    # 配置服务器IP
    configure_server_ip_for_lan

    # 创建 GPT-Vis-SSR Dockerfile
    cat > gpt-vis-ssr/Dockerfile << 'EOF'
# 使用 Node.js 官方镜像作为基础镜像
FROM node:18-alpine

# 安装构建依赖和字体支持
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    fontconfig \
    ttf-dejavu \
    ttf-liberation \
    font-noto \
    font-noto-cjk \
    font-noto-emoji \
    icu-libs \
    icu-data-full

# 设置语言环境
ENV LANG=zh_CN.UTF-8 \
    LANGUAGE=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制项目文件
COPY . .

# 创建图片存储目录
RUN mkdir -p public/images

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
EOF

    # 启动服务
    log_info "启动 Docker Compose 服务..."
    docker-compose up -d

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15

    # 检查服务状态
    check_services_health

    log_info "Docker Compose 部署完成!"
    show_access_info
}

# 检查服务健康状态
check_services_health() {
    log_step "检查服务健康状态..."

    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        log_info "检查服务状态 (尝试 $attempt/$max_attempts)..."

        # 检查 GPT-Vis-SSR
        if curl -s http://localhost:3000/health >/dev/null 2>&1; then
            log_info "✓ GPT-Vis-SSR 服务正常"
            break
        else
            log_warn "GPT-Vis-SSR 服务未就绪，等待..."
            sleep 5
            ((attempt++))
        fi
    done

    if [[ $attempt -gt $max_attempts ]]; then
        log_error "服务启动超时，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 显示访问信息
show_access_info() {
    echo
    log_info "=== 部署完成 ==="
    echo

    # 获取配置的服务器IP
    local server_ip=$(grep "SERVER_IP=" docker-compose.yml 2>/dev/null | cut -d'=' -f2 | head -1)
    if [[ -z "$server_ip" ]]; then
        server_ip="localhost"
    fi

    echo "服务访问地址:"
    echo "  - MCP Server (SSE):     http://${server_ip}/sse"
    echo "  - MCP Server (Stream):  http://${server_ip}/mcp"
    echo "  - GPT-Vis-SSR API:     http://${server_ip}/api/gpt-vis"
    echo "  - 健康检查:             http://${server_ip}/health"
    echo "  - 图片存储:             http://${server_ip}/images/"
    echo
    echo "管理命令:"
    echo "  - 查看服务状态:   docker-compose ps"
    echo "  - 查看日志:       docker-compose logs -f"
    echo "  - 停止服务:       docker-compose down"
    echo "  - 重启服务:       docker-compose restart"
    echo
    echo "配置文件位置:"
    echo "  - Docker Compose: $(pwd)/docker-compose.yml"
    echo "  - Nginx 配置:     $(pwd)/nginx.conf"
    echo "  - 图片存储:       $(pwd)/charts-storage"
    echo "  - Cherry Studio:  $(pwd)/configs/"
    echo
}

# 配置服务器IP用于局域网访问
configure_server_ip_for_lan() {
    log_step "配置服务器IP用于局域网访问..."

    # 自动检测服务器IP
    local server_ip=""

    # 尝试通过默认路由获取IP
    if command -v ip >/dev/null 2>&1; then
        server_ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
    fi

    # 如果检测失败，尝试其他方法
    if [[ -z "$server_ip" ]] && command -v hostname >/dev/null 2>&1; then
        server_ip=$(hostname -I 2>/dev/null | awk '{print $1}')
    fi

    # 如果还是检测失败，使用默认值
    if [[ -z "$server_ip" ]]; then
        server_ip="**************"
        log_warn "无法自动检测IP，使用默认值: $server_ip"
    else
        log_info "检测到服务器IP: $server_ip"
    fi

    # 询问用户是否修改
    echo
    read -p "检测到服务器IP为 $server_ip，是否修改? (y/N): " modify_ip

    if [[ "$modify_ip" =~ ^[Yy]$ ]]; then
        echo "请输入正确的服务器IP地址:"
        read -r server_ip
    fi

    # 更新配置文件
    log_info "更新配置文件中的IP地址..."

    # 更新 docker-compose.yml
    sed -i.bak "s/SERVER_IP=.*/SERVER_IP=${server_ip}/" docker-compose.yml

    # 更新 gpt-vis-ssr/index.js
    sed -i.bak "s/const serverIP = process.env.SERVER_IP || '.*'/const serverIP = process.env.SERVER_IP || '${server_ip}'/" gpt-vis-ssr/index.js

    log_info "图片URL将返回: http://${server_ip}:3000/images/xxx.png"

    # 创建Cherry Studio配置
    create_cherry_studio_configs "$server_ip" "80" "false"

    log_info "IP配置完成: $server_ip"
}

# 创建Cherry Studio配置文件
create_cherry_studio_configs() {
    local server_ip="$1"
    local nginx_port="${2:-80}"
    local use_https="${3:-false}"

    log_step "创建 Cherry Studio 配置文件..."

    mkdir -p configs

    # 确定协议和端口
    local protocol="http"
    if [[ "$use_https" == "true" ]]; then
        protocol="https"
    fi

    # 根据协议和端口决定是否显示端口号
    local port_suffix=""
    if [[ "$protocol" == "http" && "$nginx_port" != "80" ]] || [[ "$protocol" == "https" && "$nginx_port" != "443" ]]; then
        port_suffix=":${nginx_port}"
    fi

    local base_url="${protocol}://${server_ip}${port_suffix}"

    # SSE配置
    cat > configs/cherry-studio-sse-config.json << EOF
{
  "mcpServers": {
    "mcp-chart-server-sse": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/sdk",
        "client",
        "${base_url}/sse"
      ],
      "env": {
        "MCP_SERVER_URL": "${base_url}/sse"
      }
    }
  }
}
EOF

    # Streamable配置
    cat > configs/cherry-studio-streamable-config.json << EOF
{
  "mcpServers": {
    "mcp-chart-server-streamable": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/sdk",
        "client",
        "${base_url}/mcp"
      ],
      "env": {
        "MCP_SERVER_URL": "${base_url}/mcp"
      }
    }
  }
}
EOF

    log_info "Cherry Studio 配置文件已创建"
    echo "  - SSE配置: configs/cherry-studio-sse-config.json"
    echo "  - Streamable配置: configs/cherry-studio-streamable-config.json"
    echo "  - 基础URL: ${base_url}"
}

# 分离部署
deploy_separate() {
    log_step "开始分离部署..."

    install_nodejs

    # 部署 GPT-Vis-SSR 服务
    log_info "部署 GPT-Vis-SSR 服务..."
    cd gpt-vis-ssr
    npm install

    # 启动 GPT-Vis-SSR 服务（后台运行）
    log_info "启动 GPT-Vis-SSR 服务..."
    nohup npm start > ../logs/gpt-vis-ssr.log 2>&1 &
    echo $! > ../gpt-vis-ssr.pid

    cd ..

    # 安装并启动 MCP Server Chart
    log_info "安装 MCP Server Chart..."
    npm install -g @antv/mcp-server-chart

    # 设置环境变量并启动
    export VIS_REQUEST_SERVER="http://localhost:3000/api/gpt-vis"
    log_info "启动 MCP Server Chart..."
    nohup mcp-server-chart --transport sse --port 1122 > logs/mcp-chart.log 2>&1 &
    echo $! > mcp-chart.pid

    # 等待服务启动
    sleep 10

    # 检查服务
    if curl -s http://localhost:3000/health >/dev/null 2>&1; then
        log_info "✓ GPT-Vis-SSR 服务启动成功"
    else
        log_error "GPT-Vis-SSR 服务启动失败"
        exit 1
    fi

    log_info "分离部署完成!"
    echo
    echo "服务访问地址:"
    echo "  - GPT-Vis-SSR:  http://localhost:3000"
    echo "  - MCP Server:   http://localhost:1122/sse"
    echo
    echo "停止服务:"
    echo "  kill \$(cat gpt-vis-ssr.pid)"
    echo "  kill \$(cat mcp-chart.pid)"
}

# systemd 服务部署
deploy_with_systemd() {
    log_step "开始 systemd 服务部署..."

    install_nodejs

    # 创建服务目录
    local service_dir="/opt/mcp-chart-service"
    $SUDO mkdir -p "$service_dir"/{gpt-vis-ssr,logs}

    # 复制文件
    $SUDO cp -r gpt-vis-ssr/* "$service_dir/gpt-vis-ssr/"

    # 安装依赖
    cd "$service_dir/gpt-vis-ssr"
    $SUDO npm install

    # 创建用户
    $SUDO useradd -r -s /bin/false -d "$service_dir" mcp-chart || true
    $SUDO chown -R mcp-chart:mcp-chart "$service_dir"

    # 创建 systemd 服务文件
    $SUDO tee /etc/systemd/system/gpt-vis-ssr.service > /dev/null << EOF
[Unit]
Description=GPT-Vis-SSR Rendering Service
After=network.target

[Service]
Type=simple
User=mcp-chart
Group=mcp-chart
WorkingDirectory=$service_dir/gpt-vis-ssr
Environment=NODE_ENV=production
Environment=PORT=3000
ExecStart=/usr/bin/node index.js
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    $SUDO tee /etc/systemd/system/mcp-chart-server.service > /dev/null << EOF
[Unit]
Description=MCP Chart Server
After=network.target gpt-vis-ssr.service
Requires=gpt-vis-ssr.service

[Service]
Type=simple
User=mcp-chart
Group=mcp-chart
Environment=NODE_ENV=production
Environment=VIS_REQUEST_SERVER=http://localhost:3000/api/gpt-vis
ExecStart=/usr/bin/mcp-server-chart --transport sse --port 1122
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # 安装 MCP Server Chart
    $SUDO npm install -g @antv/mcp-server-chart

    # 启动服务
    $SUDO systemctl daemon-reload
    $SUDO systemctl enable gpt-vis-ssr mcp-chart-server
    $SUDO systemctl start gpt-vis-ssr mcp-chart-server

    # 检查服务状态
    sleep 5
    $SUDO systemctl status gpt-vis-ssr --no-pager
    $SUDO systemctl status mcp-chart-server --no-pager

    log_info "systemd 服务部署完成!"
    echo
    echo "服务管理命令:"
    echo "  sudo systemctl status gpt-vis-ssr"
    echo "  sudo systemctl restart gpt-vis-ssr"
    echo "  sudo journalctl -u gpt-vis-ssr -f"
}

# 主函数
main() {
    echo "========================================"
    echo "  MCP Chart Server 私有化部署脚本"
    echo "========================================"
    echo
    
    # 解析命令行参数
    local project_dir="mcp-chart-private"
    local deployment_type=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dir)
                project_dir="$2"
                shift 2
                ;;
            -t|--type)
                deployment_type="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行部署流程
    check_requirements
    install_docker
    create_project_structure "$project_dir"
    create_gpt_vis_ssr_files
    
    if [[ -n "$deployment_type" ]]; then
        case $deployment_type in
            docker) deploy_with_docker_compose ;;
            separate) deploy_separate ;;
            systemd) deploy_with_systemd ;;
            *) log_error "无效的部署类型: $deployment_type"; exit 1 ;;
        esac
    else
        show_deployment_menu
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -d, --dir DIR      指定项目目录 (默认: mcp-chart-private)"
    echo "  -t, --type TYPE    指定部署类型 (docker|separate|systemd)"
    echo "  -h, --help         显示此帮助信息"
    echo
    echo "部署类型:"
    echo "  docker    - Docker Compose 部署 (推荐)"
    echo "  separate  - 分离部署"
    echo "  systemd   - systemd 服务部署"
    echo
    echo "示例:"
    echo "  $0                           # 交互式部署"
    echo "  $0 -t docker                 # 直接使用 Docker 部署"
    echo "  $0 -d my-project -t docker   # 指定目录和部署类型"
}

# 如果脚本被直接执行，则运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
