#!/bin/bash

# 生成客户端配置文件
# 为不同的MCP客户端生成配置文件

SERVER_IP="**************"
echo "=== 生成客户端配置文件 (服务器IP: $SERVER_IP) ==="

# 创建配置目录
CONFIG_DIR="$HOME/mcp-client-configs"
mkdir -p "$CONFIG_DIR"

echo "配置文件将保存到: $CONFIG_DIR"

# 1. <PERSON>配置（macOS）
echo "1. 生成Claude <PERSON>配置（macOS）..."
cat > "$CONFIG_DIR/claude_desktop_config_macos.json" << EOF
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "mcp-server-chart",
      "args": ["--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://$SERVER_IP:3000/api/gpt-vis"
      }
    }
  }
}
EOF

# 2. <PERSON>配置（Windows）
echo "2. 生成Claude <PERSON>配置（Windows）..."
cat > "$CONFIG_DIR/claude_desktop_config_windows.json" << EOF
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "cmd",
      "args": ["/c", "npx", "-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://$SERVER_IP:3000/api/gpt-vis"
      }
    }
  }
}
EOF

# 3. Claude Desktop配置（使用npx，推荐）
echo "3. 生成Claude Desktop配置（npx，推荐）..."
cat > "$CONFIG_DIR/claude_desktop_config_npx.json" << EOF
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://$SERVER_IP:3000/api/gpt-vis"
      }
    }
  }
}
EOF

# 4. VSCode Cline配置
echo "4. 生成VSCode Cline配置..."
cat > "$CONFIG_DIR/vscode_cline_settings.json" << EOF
{
  "cline.mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://$SERVER_IP:3000/api/gpt-vis"
      }
    }
  }
}
EOF

# 5. Cursor配置
echo "5. 生成Cursor配置..."
cat > "$CONFIG_DIR/cursor_settings.json" << EOF
{
  "mcp.servers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://$SERVER_IP:3000/api/gpt-vis"
      }
    }
  }
}
EOF

# 6. Continue配置
echo "6. 生成Continue配置..."
cat > "$CONFIG_DIR/continue_config.json" << EOF
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://$SERVER_IP:3000/api/gpt-vis"
      }
    }
  }
}
EOF

# 7. Cherry Studio配置说明
echo "7. 生成Cherry Studio配置说明..."
cat > "$CONFIG_DIR/cherry_studio_config.txt" << EOF
Cherry Studio MCP配置

SSE传输方式:
服务器类型: MCP Server
传输方式: SSE
服务器URL: http://$SERVER_IP:1122/sse
名称: MCP Chart Server
描述: 图表生成服务

Streamable传输方式:
服务器类型: MCP Server
传输方式: Streamable
服务器URL: http://$SERVER_IP:1123/mcp
名称: MCP Chart Server (Streamable)
描述: 高性能图表生成服务
EOF

# 8. 环境变量配置
echo "8. 生成环境变量配置..."
cat > "$CONFIG_DIR/environment_variables.sh" << EOF
#!/bin/bash
# MCP环境变量配置

# 渲染服务地址
export VIS_REQUEST_SERVER="http://$SERVER_IP:3000/api/gpt-vis"

# 可选：服务ID（用于保存记录）
# export SERVICE_ID="your-service-id"

# 可选：禁用特定工具
# export DISABLED_TOOLS="generate_fishbone_diagram,generate_mind_map"

echo "MCP环境变量已设置:"
echo "VIS_REQUEST_SERVER=\$VIS_REQUEST_SERVER"
EOF

chmod +x "$CONFIG_DIR/environment_variables.sh"

# 9. 测试脚本
echo "9. 生成测试脚本..."
cat > "$CONFIG_DIR/test_stdio.sh" << EOF
#!/bin/bash
# 测试stdio传输

echo "测试MCP stdio传输..."

# 检查命令行工具
if ! command -v mcp-server-chart &> /dev/null; then
    echo "错误: mcp-server-chart未安装"
    echo "请运行: npm install -g @antv/mcp-server-chart"
    exit 1
fi

# 设置环境变量
export VIS_REQUEST_SERVER="http://$SERVER_IP:3000/api/gpt-vis"

# 测试初始化
echo "测试MCP初始化..."
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{}}}' | timeout 10 mcp-server-chart --transport stdio

echo "stdio传输测试完成"
EOF

chmod +x "$CONFIG_DIR/test_stdio.sh"

# 10. 网络测试脚本
echo "10. 生成网络测试脚本..."
cat > "$CONFIG_DIR/test_network.sh" << EOF
#!/bin/bash
# 测试网络连接

echo "测试MCP服务网络连接..."

# 测试渲染服务
echo "测试渲染服务..."
if curl -s http://$SERVER_IP:3000/health > /dev/null; then
    echo "✅ 渲染服务连接正常"
else
    echo "❌ 渲染服务连接失败"
fi

# 测试SSE服务
echo "测试SSE服务..."
if curl -s http://$SERVER_IP:1122/sse > /dev/null; then
    echo "✅ SSE服务连接正常"
else
    echo "❌ SSE服务连接失败"
fi

# 测试Streamable服务
echo "测试Streamable服务..."
if curl -s http://$SERVER_IP:1123/mcp > /dev/null; then
    echo "✅ Streamable服务连接正常"
else
    echo "❌ Streamable服务连接失败"
fi

# 测试图表生成
echo "测试图表生成..."
RESPONSE=\$(curl -s -X POST http://$SERVER_IP:3000/api/gpt-vis \\
  -H "Content-Type: application/json" \\
  -d '{
    "type": "pie",
    "data": [
      {"category": "测试", "value": 100}
    ],
    "title": "网络测试"
  }')

if echo "\$RESPONSE" | grep -q '"success":true'; then
    echo "✅ 图表生成测试成功"
    IMAGE_URL=\$(echo "\$RESPONSE" | grep -o 'http://[^"]*\.png')
    echo "图片URL: \$IMAGE_URL"
else
    echo "❌ 图表生成测试失败"
fi
EOF

chmod +x "$CONFIG_DIR/test_network.sh"

# 11. 安装说明
echo "11. 生成安装说明..."
cat > "$CONFIG_DIR/README.md" << EOF
# MCP客户端配置文件

服务器IP: **$SERVER_IP**

## 配置文件说明

### stdio传输（本地客户端）
- \`claude_desktop_config_macos.json\` - macOS Claude Desktop配置
- \`claude_desktop_config_windows.json\` - Windows Claude Desktop配置
- \`claude_desktop_config_npx.json\` - 使用npx的配置（推荐）
- \`vscode_cline_settings.json\` - VSCode Cline配置
- \`cursor_settings.json\` - Cursor配置
- \`continue_config.json\` - Continue配置

### HTTP传输（Web客户端）
- \`cherry_studio_config.txt\` - Cherry Studio配置说明

### 环境变量和测试
- \`environment_variables.sh\` - 环境变量配置
- \`test_stdio.sh\` - stdio传输测试
- \`test_network.sh\` - 网络连接测试

## 使用方法

### 1. Claude Desktop配置

#### macOS
\`\`\`bash
cp claude_desktop_config_npx.json "~/Library/Application Support/Claude/claude_desktop_config.json"
\`\`\`

#### Windows
\`\`\`cmd
copy claude_desktop_config_windows.json "%APPDATA%\\Claude\\claude_desktop_config.json"
\`\`\`

### 2. VSCode Cline配置
将 \`vscode_cline_settings.json\` 的内容添加到VSCode设置中。

### 3. Cursor配置
将 \`cursor_settings.json\` 的内容添加到Cursor设置中。

### 4. Cherry Studio配置
按照 \`cherry_studio_config.txt\` 中的说明配置Cherry Studio。

## 测试

### 测试stdio传输
\`\`\`bash
./test_stdio.sh
\`\`\`

### 测试网络连接
\`\`\`bash
./test_network.sh
\`\`\`

### 设置环境变量
\`\`\`bash
source environment_variables.sh
\`\`\`

## 服务地址

- 渲染服务: http://$SERVER_IP:3000
- SSE服务: http://$SERVER_IP:1122/sse
- Streamable服务: http://$SERVER_IP:1123/mcp
- 图片访问: http://$SERVER_IP:3000/images/

## 故障排除

1. 确保服务器 $SERVER_IP 可访问
2. 检查防火墙设置
3. 验证Node.js和npm已安装
4. 运行测试脚本检查连接

## 支持的图表类型

- 饼图 (pie)
- 柱状图 (column)
- 条形图 (bar)
- 折线图 (line)
- 面积图 (area)
- 散点图 (scatter)
- 雷达图 (radar)
- 漏斗图 (funnel)
- 等等...

详细的图表类型和参数请参考MCP服务文档。
EOF

echo ""
echo "=== 配置文件生成完成 ==="
echo ""
echo "📁 配置文件位置: $CONFIG_DIR"
echo ""
echo "📋 生成的文件:"
ls -la "$CONFIG_DIR"
echo ""
echo "🚀 快速开始:"
echo "1. 选择适合你的客户端的配置文件"
echo "2. 复制配置内容到相应的配置文件中"
echo "3. 重启客户端应用"
echo "4. 运行测试脚本验证连接"
echo ""
echo "📖 详细说明请查看: $CONFIG_DIR/README.md"
echo ""
echo "🔧 测试命令:"
echo "  网络测试: $CONFIG_DIR/test_network.sh"
echo "  stdio测试: $CONFIG_DIR/test_stdio.sh"
