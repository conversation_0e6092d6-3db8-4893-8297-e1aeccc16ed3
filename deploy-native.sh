#!/bin/bash

# 纯宿主机部署MCP解决方案
# 完全避免Docker容器问题

set -e

echo "=== 纯宿主机部署MCP解决方案 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    apt-get install -y nodejs
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 停止现有Docker服务
echo "停止现有Docker服务..."
for dir in "/opt/mcp-server-chart" "/opt/mcp-prebuilt" "/opt/mcp-npm-solution" "/opt/mcp-multi-transport" "/opt/mcp-prebuilt-images" "/opt/mcp-host-build"; do
    if [ -d "$dir" ]; then
        cd "$dir" 2>/dev/null && docker compose down 2>/dev/null || true
    fi
done

# 停止可能运行的进程
pkill -f "gpt-vis-ssr" || true
pkill -f "mcp-server-chart" || true

APP_DIR="/opt/mcp-native"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

echo "1. 安装系统依赖..."
apt-get update
apt-get install -y \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    libpixman-1-dev \
    pkg-config \
    python3-dev \
    build-essential \
    curl \
    pm2

echo "2. 创建渲染服务..."
mkdir -p render-service
cd render-service

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0",
    "cors": "^2.8.5"
  }
}
EOF

# 创建服务器代码
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();

app.use(cors());
app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log(`[${new Date().toISOString()}] 收到渲染请求:`, req.body.type);
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log(`[${new Date().toISOString()}] 图表生成成功:`, filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error(`[${new Date().toISOString()}] 渲染错误:`, error.message);
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr',
    uptime: process.uptime()
  });
});

app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`[${new Date().toISOString()}] GPT-Vis-SSR服务运行在端口 ${PORT}`);
});
EOF

echo "3. 安装渲染服务依赖..."
npm config set registry https://registry.npmmirror.com
npm install

echo "4. 测试渲染服务..."
timeout 10 node -e "
const { render } = require('@antv/gpt-vis-ssr');
console.log('GPT-Vis-SSR模块加载成功');
render({type: 'pie', data: [{category: 'test', value: 100}]})
  .then(() => console.log('渲染测试成功'))
  .catch(err => console.error('渲染测试失败:', err));
" || echo "渲染测试超时，但模块已安装"

cd $APP_DIR

echo "5. 全局安装mcp-server-chart..."
npm install -g @antv/mcp-server-chart

echo "6. 创建PM2配置文件..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'gpt-vis-ssr',
      cwd: './render-service',
      script: 'server.js',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      log_file: '/var/log/gpt-vis-ssr.log',
      error_file: '/var/log/gpt-vis-ssr-error.log',
      out_file: '/var/log/gpt-vis-ssr-out.log'
    },
    {
      name: 'mcp-sse-server',
      script: 'mcp-server-chart',
      args: '--transport sse --port 1122',
      env: {
        NODE_ENV: 'production',
        VIS_REQUEST_SERVER: 'http://localhost:3000/api/gpt-vis'
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      log_file: '/var/log/mcp-sse-server.log',
      error_file: '/var/log/mcp-sse-server-error.log',
      out_file: '/var/log/mcp-sse-server-out.log'
    }
  ]
};
EOF

echo "7. 创建图片目录..."
mkdir -p render-service/images

echo "8. 启动服务..."
pm2 start ecosystem.config.js

echo "9. 等待服务启动..."
sleep 10

echo "10. 检查服务状态..."
pm2 status

echo "11. 测试服务..."
echo "测试渲染服务..."
for i in {1..5}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务正常"
        curl -s http://localhost:3000/health
        break
    else
        echo "⏳ 等待渲染服务... ($i/5)"
        sleep 5
    fi
done

echo ""
echo "测试SSE服务..."
for i in {1..5}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✅ SSE服务正常"
        break
    else
        echo "⏳ 等待SSE服务... ($i/5)"
        sleep 5
    fi
done

echo ""
echo "测试图表生成..."
RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "测试", "value": 100}
    ]
  }')

if echo "$RESPONSE" | grep -q '"success":true'; then
    echo "✅ 图表生成成功"
else
    echo "❌ 图表生成失败"
    echo "响应: $RESPONSE"
fi

echo ""
echo "=== 部署完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - MCP SSE服务: http://localhost:1122/sse"
echo ""
echo "Cherry Studio配置:"
echo "  SSE URL: http://localhost:1122/sse"
echo ""
echo "服务管理命令:"
echo "  查看状态: pm2 status"
echo "  查看日志: pm2 logs"
echo "  重启服务: pm2 restart all"
echo "  停止服务: pm2 stop all"
echo "  删除服务: pm2 delete all"
echo ""
echo "日志文件位置:"
echo "  /var/log/gpt-vis-ssr.log"
echo "  /var/log/mcp-sse-server.log"
echo ""
echo "优势："
echo "  - 完全避免Docker问题"
echo "  - 使用PM2进程管理"
echo "  - 性能更好，启动更快"
echo "  - 更容易调试和维护"
