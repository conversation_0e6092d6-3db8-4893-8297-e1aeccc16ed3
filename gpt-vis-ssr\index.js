const express = require('express')
const { render } = require('@antv/gpt-vis-ssr')
const fs = require('fs-extra')
const path = require('path')
const { v4: uuidv4 } = require('uuid')

const app = express()
const port = process.env.PORT || 3000
const publicDir = path.join(__dirname, 'public')
const imagesDir = path.join(publicDir, 'images')

// 确保目录存在
fs.ensureDirSync(imagesDir)

app.use(express.json({ limit: '10mb' }))
app.use('/images', express.static(imagesDir))

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'gpt-vis-ssr' })
})

// 图表渲染接口
app.post('/api/gpt-vis', async (req, res) => {
  try {
    const options = req.body
    
    // 验证必要的参数
    if (!options || !options.type || !options.data) {
      return res.status(400).json({
        success: false,
        errorMessage: '缺少必要的参数: type 或 data'
      })
    }

    console.log('渲染图表请求:', JSON.stringify(options, null, 2))

    // 渲染图表
    const vis = await render(options)
    const buffer = await vis.toBuffer()

    // 生成唯一文件名并保存图片
    const filename = `${uuidv4()}.png`
    const filePath = path.join(imagesDir, filename)
    await fs.writeFile(filePath, buffer)

    // 构建图片URL - 支持局域网访问
    const serverIP = process.env.SERVER_IP || '**************'
    const gptVisPort = process.env.PORT || '3000'  // GPT-Vis-SSR服务端口
    const protocol = process.env.USE_HTTPS === 'true' ? 'https' : 'http'

    // 图片URL直接指向GPT-Vis-SSR服务端口
    const imageUrl = `${protocol}://${serverIP}:${gptVisPort}/images/${filename}`

    console.log('图表渲染成功:', imageUrl)

    res.json({
      success: true,
      resultObj: imageUrl
    })
  } catch (error) {
    console.error('渲染图表时出错:', error)
    res.status(500).json({
      success: false,
      errorMessage: `渲染图表失败: ${error.message}`
    })
  }
})

app.listen(port, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR 服务运行在 http://0.0.0.0:${port}`)
})
