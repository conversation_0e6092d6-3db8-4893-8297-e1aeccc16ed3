#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查汇总sheet内容的脚本
"""

import pandas as pd

def main():
    # 读取最新生成的结果文件
    result_file = "2025年省抽任务分析结果_20250721_111001.xlsx"
    
    # 读取所有sheet名称
    excel_file = pd.ExcelFile(result_file)
    sheet_names = excel_file.sheet_names
    
    print("=== Excel文件Sheet顺序 ===")
    for i, sheet_name in enumerate(sheet_names, 1):
        print(f"{i}. {sheet_name}")
    
    # 读取汇总统计sheet
    summary_df = pd.read_excel(result_file, sheet_name='汇总统计')
    
    print("\n=== 汇总统计内容 ===")
    print(summary_df.to_string(index=False))
    
    print(f"\n汇总sheet位置: 第{sheet_names.index('汇总统计') + 1}个sheet")

if __name__ == "__main__":
    main()
