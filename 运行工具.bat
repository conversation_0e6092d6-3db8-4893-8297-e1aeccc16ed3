@echo off
chcp 65001 >nul
echo ================================
echo     图片转Excel工具
echo ================================
echo.

echo 请选择要使用的工具:
echo 1. 简化版 (手动输入数据)
echo 2. 完整版 (OCR识别图片)
echo 3. 安装依赖包
echo 4. 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo.
    echo 启动简化版工具...
    python simple_image_to_excel.py
) else if "%choice%"=="2" (
    echo.
    echo 启动完整版工具...
    python image_to_excel.py
) else if "%choice%"=="3" (
    echo.
    echo 安装依赖包...
    python install_dependencies.py
) else if "%choice%"=="4" (
    echo 再见!
    exit /b 0
) else (
    echo 无效选择，请重新运行
)

echo.
pause
