<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水果分布饼图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
            margin: 20px 0;
        }
        .data-table {
            margin-top: 30px;
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .data-table tr:hover {
            background-color: #f5f5f5;
        }
        .color-box {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>水果分布饼图</h1>
        
        <div class="chart-container">
            <canvas id="fruitChart"></canvas>
        </div>
        
        <table class="data-table">
            <thead>
                <tr>
                    <th>水果类型</th>
                    <th>百分比</th>
                    <th>数值</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="color-box" style="background-color: #FF6384;"></span>🍎 苹果</td>
                    <td>40%</td>
                    <td>40</td>
                </tr>
                <tr>
                    <td><span class="color-box" style="background-color: #36A2EB;"></span>🍌 香蕉</td>
                    <td>30%</td>
                    <td>30</td>
                </tr>
                <tr>
                    <td><span class="color-box" style="background-color: #FFCE56;"></span>🍊 橙子</td>
                    <td>20%</td>
                    <td>20</td>
                </tr>
                <tr>
                    <td><span class="color-box" style="background-color: #4BC0C0;"></span>🍇 其他</td>
                    <td>10%</td>
                    <td>10</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        // 获取canvas元素
        const ctx = document.getElementById('fruitChart').getContext('2d');
        
        // 创建饼图
        const fruitChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['苹果', '香蕉', '橙子', '其他'],
                datasets: [{
                    data: [40, 30, 20, 10],
                    backgroundColor: [
                        '#FF6384',  // 苹果 - 红色
                        '#36A2EB',  // 香蕉 - 蓝色
                        '#FFCE56',  // 橙子 - 黄色
                        '#4BC0C0'   // 其他 - 青色
                    ],
                    borderColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '水果分布情况',
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 14
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>