#!/bin/bash

# 在宿主机构建，容器只运行
# 避免容器内网络和安装问题

set -e

echo "=== 宿主机构建MCP解决方案 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    apt-get install -y nodejs
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

APP_DIR="/opt/mcp-host-build"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# 停止现有服务
echo "停止现有Docker服务..."
for dir in "/opt/mcp-server-chart" "/opt/mcp-prebuilt" "/opt/mcp-npm-solution" "/opt/mcp-multi-transport" "/opt/mcp-prebuilt-images"; do
    if [ -d "$dir" ]; then
        cd "$dir" 2>/dev/null && docker compose down 2>/dev/null || true
    fi
done

cd $APP_DIR

echo "1. 在宿主机构建渲染服务..."
mkdir -p render-service
cd render-service

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0",
    "cors": "^2.8.5"
  }
}
EOF

# 创建服务器代码
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();

app.use(cors());
app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr'
  });
});

app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
});
EOF

echo "2. 安装系统依赖（用于Canvas）..."
apt-get update
apt-get install -y \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    libpixman-1-dev \
    pkg-config \
    python3-dev \
    build-essential

echo "3. 在宿主机安装npm依赖..."
npm config set registry https://registry.npmmirror.com
npm install

echo "4. 测试渲染服务..."
node -e "
const { render } = require('@antv/gpt-vis-ssr');
console.log('GPT-Vis-SSR模块加载成功');
render({type: 'pie', data: [{category: 'test', value: 100}]})
  .then(() => console.log('渲染测试成功'))
  .catch(err => console.error('渲染测试失败:', err));
"

cd $APP_DIR

echo "5. 全局安装mcp-server-chart..."
npm install -g @antv/mcp-server-chart

echo "6. 创建简化的Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-slim
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    command: ["node", "server.js"]
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  mcp-sse-server:
    image: node:18-slim
    container_name: mcp-sse-server
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    command:
      - bash
      - -c
      - |
        apt-get update && apt-get install -y curl
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务..."
        for i in {1..30}; do
          if curl -s http://gpt-vis-ssr:3000/health; then break; fi
          sleep 2
        done
        mcp-server-chart --transport sse --port 1122
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
EOF

echo "7. 创建图片目录..."
mkdir -p images

echo "8. 启动Docker服务..."
docker compose up -d

echo "9. 等待服务启动..."
sleep 30

echo "10. 检查服务状态..."
docker compose ps

echo "11. 测试服务..."
echo "测试渲染服务..."
for i in {1..5}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务正常"
        curl -s http://localhost:3000/health
        break
    else
        echo "⏳ 等待渲染服务... ($i/5)"
        sleep 10
    fi
done

echo ""
echo "测试SSE服务..."
for i in {1..5}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✅ SSE服务正常"
        break
    else
        echo "⏳ 等待SSE服务... ($i/5)"
        sleep 10
    fi
done

echo ""
echo "测试图表生成..."
RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "测试", "value": 100}
    ]
  }')

if echo "$RESPONSE" | grep -q '"success":true'; then
    echo "✅ 图表生成成功"
else
    echo "❌ 图表生成失败"
    echo "响应: $RESPONSE"
fi

echo ""
echo "=== 部署完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - MCP SSE服务: http://localhost:1122/sse"
echo ""
echo "Cherry Studio配置:"
echo "  SSE URL: http://localhost:1122/sse"
echo ""
echo "优势："
echo "  - 在宿主机构建，避免容器网络问题"
echo "  - 使用预安装的依赖，启动更快"
echo "  - 更稳定可靠"
