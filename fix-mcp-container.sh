#!/bin/bash

# 修复MCP容器问题
# 解决husky和git依赖问题

set -e

echo "=== 修复MCP容器问题 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

APP_DIR="/opt/mcp-server-chart"
cd $APP_DIR

echo "停止现有服务..."
docker compose down

echo "创建修复后的Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./gpt-vis-ssr:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++
        npm install
        npm start
    restart: unless-stopped
    networks:
      - mcp-network

  mcp-chart-server:
    image: node:18-alpine
    container_name: mcp-chart-server
    working_dir: /app
    ports:
      - "1122:1122"
    volumes:
      - ./mcp-server-chart:/app
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        apk add --no-cache git
        npm install --ignore-scripts
        npm run build
        node build/index.js --transport sse --port 1122
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
EOF

echo "清理Docker缓存..."
docker system prune -f

echo "重新启动服务..."
docker compose up -d

echo "等待服务启动..."
sleep 60

echo "检查服务状态..."
docker compose ps

echo "查看MCP服务日志..."
docker compose logs --tail=20 mcp-chart-server

echo "查看渲染服务日志..."
docker compose logs --tail=20 gpt-vis-ssr

echo ""
echo "=== 测试服务 ==="
echo "测试渲染服务..."
for i in {1..5}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务响应正常"
        curl -s http://localhost:3000/health
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "测试MCP服务..."
for i in {1..5}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✅ MCP服务响应正常"
        break
    else
        echo "⏳ 等待MCP服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "=== 修复完成 ==="
echo "如果服务仍有问题，请查看日志："
echo "  docker compose logs -f mcp-chart-server"
echo "  docker compose logs -f gpt-vis-ssr"
