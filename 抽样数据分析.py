import openpyxl
import pandas as pd
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
import tkinter as tk
from tkinter import filedialog
import os
import matplotlib.pyplot as plt
from openpyxl.drawing.image import Image as XLImage
import matplotlib
from datetime import datetime
import re

# 设置matplotlib使用中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'STSong']
matplotlib.rcParams['axes.unicode_minus'] = False

# 文件选择对话框
root = tk.Tk()
root.withdraw()
print("--------------------------------------------------")
print("提示：请在弹出的对话框中选择你需要分析的Excel文件。")
print("--------------------------------------------------")
file_path = filedialog.askopenfilename(
    title='请选择需要分析的Excel文件',
    filetypes=[('Excel Files', '*.xlsx *.xls')]
)
if not file_path:
    print('未选择文件,程序退出。')
    exit()

# 直接用pandas读取Excel为DataFrame，使用第1行作为列名
df = pd.read_excel(file_path, header=1)

# 预处理抽样时间为月份
if '抽样时间' in df.columns:
    df['抽样月份'] = pd.to_datetime(df['抽样时间'], errors='coerce').dt.to_period('M').astype(str)
else:
    df['抽样月份'] = '未知'

# 按月份排序
month_list = sorted(df['抽样月份'].dropna().unique())

# 统计累积数据
TOTAL_TASKS = 400
cumulative_stats = []
for month in month_list:
    df_cum = df[df['抽样月份'] <= month]
    sampled_batches = df_cum['抽样编号'].dropna().nunique() if '抽样编号' in df_cum.columns else 0
    progress = sampled_batches / TOTAL_TASKS if TOTAL_TASKS else 0
    progress_percent = f'{progress*100:.2f}%'
    total_price = df_cum['总价（元）'].dropna().astype(float).sum() if '总价（元）' in df_cum.columns else 0
    sample_count = df_cum['样品名称'].dropna().nunique() if '样品名称' in df_cum.columns else 0
    approval_count = df_cum['批准文号'].dropna().nunique() if '批准文号' in df_cum.columns else 0
    unit_count = df_cum['被抽样单位'].dropna().nunique() if '被抽样单位' in df_cum.columns else 0
    cumulative_stats.append([
        month,
        sampled_batches,
        f'{sampled_batches}/{TOTAL_TASKS} ({progress_percent})',
        f'{total_price:.2f}',
        sample_count,
        approval_count,
        unit_count
    ])

# 创建新的Excel工作簿用于美观展示
wb_out = openpyxl.Workbook()
ws = wb_out.active
ws.title = '每月累积统计'

# 插入标题和截止时间
now_str = datetime.now().strftime('%Y-%m-%d')
title = f'2025年广州市进口化学药品抽样数据分析表（截止时间：{now_str}）'
ws.merge_cells('A1:G1')
ws['A1'] = title
ws['A1'].font = Font(bold=True, size=16)
ws['A1'].alignment = Alignment(horizontal='center', vertical='center')

# 设置表头和单元格样式
def style_header(cell):
    cell.font = Font(bold=True, color='FFFFFF', size=13)
    cell.fill = PatternFill('solid', fgColor='4F81BD')
    cell.alignment = Alignment(horizontal='center', vertical='center')
    cell.border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

def style_cell(cell, v_align='center'):
    cell.font = Font(size=12, color='000000')
    cell.alignment = Alignment(horizontal='left', vertical=v_align, wrap_text=True)
    cell.fill = PatternFill(fill_type=None)
    cell.border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

# 写入每月累积统计数据
ws.append(['抽样月份', '已抽样批次', '抽样进度', '总价合计', '药品品种数量', '药品品规数量', '被抽样单位数量'])
for row in cumulative_stats:
    ws.append(row)

# 美化每月累积统计表格
for col in ws.columns:
    for cell in col:
        if cell.row == 1:
            continue
        if cell.row == 2:
            style_header(cell)
        else:
            style_cell(cell)

# 调整列宽
ws.column_dimensions['A'].width = 14
ws.column_dimensions['B'].width = 16
ws.column_dimensions['C'].width = 22
ws.column_dimensions['D'].width = 18
ws.column_dimensions['E'].width = 18
ws.column_dimensions['F'].width = 18
ws.column_dimensions['G'].width = 18

# 生成图表
line_chart_path = os.path.join(os.path.expanduser('~'), 'Desktop', 'sampled_batches_line.png')
pie_chart_path = os.path.join(os.path.expanduser('~'), 'Desktop', 'progress_pie.png')

months = [row[0] for row in cumulative_stats]
batches = [row[1] for row in cumulative_stats]
plt.figure(figsize=(7, 4))
plt.plot(months, batches, marker='o', color='#4F81BD', label='已抽样批次')
plt.title('已抽样批次按月累积趋势')
plt.xlabel('抽样月份')
plt.ylabel('已抽样批次')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.5)
plt.tight_layout()
plt.savefig(line_chart_path, dpi=150)
plt.close()

if batches:
    last_batches = batches[-1]
    remain = max(TOTAL_TASKS - last_batches, 0)
    plt.figure(figsize=(4, 4))
    plt.pie([last_batches, remain], labels=['已抽样', '未抽样'], autopct='%1.1f%%', colors=['#4F81BD', '#C0504D'], startangle=90, counterclock=False)
    plt.title('抽样进度')
    plt.savefig(pie_chart_path, dpi=150)
    plt.close()

# 插入图片到Excel
img1 = XLImage(line_chart_path)
img1.width, img1.height = 420, 240
img_row = len(cumulative_stats) + 5
ws.add_image(img1, f'A{img_row}')
if os.path.exists(pie_chart_path):
    img2 = XLImage(pie_chart_path)
    img2.width, img2.height = 300, 240
    ws.add_image(img2, f'E{img_row}')

# --- 创建被抽样单位明细 sheet ---
ws_detail = wb_out.create_sheet('被抽样单位明细')
ws_detail.append(['被抽样单位', '样品名称', '制剂规格', '批准文号', '批数'])

detail_rows = []  # 初始化 detail_rows 变量
if all(col in df.columns for col in ['被抽样单位', '样品名称', '制剂规格', '批准文号']):
    approval_counts = df['批准文号'].value_counts()
    total_batches = df['批准文号'].count()

    detail_df = df[['被抽样单位', '样品名称', '制剂规格', '批准文号']].dropna(subset=['被抽样单位', '样品名称'])
    detail_df = detail_df.drop_duplicates()

    for _, row in detail_df.iterrows():
        unit = str(row['被抽样单位'])
        samples = [s.strip() for s in re.split(r'[，,;；]+', str(row['样品名称'])) if s.strip()]
        spec = row['制剂规格'] if not pd.isna(row['制剂规格']) else ''
        approval = row['批准文号'] if not pd.isna(row['批准文号']) else ''
        batch_count = approval_counts.get(approval, 0) if approval else 0
        for sample in samples:
            detail_rows.append([unit, sample, spec, approval, batch_count])

    detail_rows = sorted(detail_rows, key=lambda x: (x[0], x[1]))

    for row_data in detail_rows:
        ws_detail.append(row_data)

    ws_detail.append(['合计', '', '', '', total_batches])
else:
    # 如果缺少必要的列，添加一个提示行
    ws_detail.append(['数据不完整', '缺少必要的列', '', '', 0])

# 美化表头和单元格
for cell in ws_detail[1]:
    style_header(cell)
for row in ws_detail.iter_rows(min_row=2, max_row=ws_detail.max_row):
    for cell in row:
        style_cell(cell, v_align='top') # 靠上对齐以配合合并单元格

# 合并被抽样单位单元格
start_row = 2
if len(detail_rows) > 0:
    for i in range(2, ws_detail.max_row):
        if ws_detail.cell(row=i, column=1).value != ws_detail.cell(row=start_row, column=1).value:
            if i - 1 > start_row:
                ws_detail.merge_cells(start_row=start_row, start_column=1, end_row=i - 1, end_column=1)
            start_row = i
    # 合并最后一部分
    if ws_detail.max_row > start_row:
        ws_detail.merge_cells(start_row=start_row, start_column=1, end_row=ws_detail.max_row -1, end_column=1)

# 美化合计行
total_row_idx = ws_detail.max_row
for cell in ws_detail[total_row_idx]:
    cell.font = Font(bold=True, size=12)
    cell.border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
    cell.alignment = Alignment(horizontal='center', vertical='center')

# 调整列宽
ws_detail.column_dimensions['A'].width = 40
ws_detail.column_dimensions['B'].width = 24
ws_detail.column_dimensions['C'].width = 24
ws_detail.column_dimensions['D'].width = 24
ws_detail.column_dimensions['E'].width = 12

# --- 新增统计sheet：被抽样单位批数统计 ---
ws_unit_stats = wb_out.create_sheet('被抽样单位批数统计')
ws_unit_stats.append(['被抽样单位', '批数'])

if '被抽样单位' in df.columns:
    unit_counts = df['被抽样单位'].value_counts().reset_index()
    unit_counts.columns = ['被抽样单位', '批数']
    unit_counts = unit_counts.sort_values(by='被抽样单位')

    for _, row in unit_counts.iterrows():
        ws_unit_stats.append(list(row))

    total_unit_batches = unit_counts['批数'].sum()
    ws_unit_stats.append(['合计', total_unit_batches])

# 美化
for cell in ws_unit_stats[1]:
    style_header(cell)
for row in ws_unit_stats.iter_rows(min_row=2, max_row=ws_unit_stats.max_row - 1):
    for cell in row:
        style_cell(cell)

total_row_idx_stats = ws_unit_stats.max_row
for cell in ws_unit_stats[total_row_idx_stats]:
    cell.font = Font(bold=True, size=12)
    cell.border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
    cell.alignment = Alignment(horizontal='center', vertical='center')

ws_unit_stats.column_dimensions['A'].width = 40
ws_unit_stats.column_dimensions['B'].width = 15

# --- 文件保存 ---
print("\n--------------------------------------------------")
print("提示：数据分析完成，请在弹出的对话框中选择保存分析结果的路径和文件名。")
print("--------------------------------------------------")
now_str_for_filename = datetime.now().strftime('%Y%m%d_%H%M%S')
save_path = filedialog.asksaveasfilename(
    title='请选择保存分析结果的路径和文件名',
    defaultextension=".xlsx",
    filetypes=[('Excel Files', '*.xlsx')],
    initialfile=f'评价抽样分析结果_{now_str_for_filename}.xlsx'
)

if not save_path:
    print('未选择保存路径,程序退出。')
    if os.path.exists(line_chart_path): os.remove(line_chart_path)
    if os.path.exists(pie_chart_path): os.remove(pie_chart_path)
    exit()

wb_out.save(save_path)

# 删除临时图片
if os.path.exists(line_chart_path): os.remove(line_chart_path)
if os.path.exists(pie_chart_path): os.remove(pie_chart_path)

print(f'美观的数据分析结果已保存到: {save_path}')