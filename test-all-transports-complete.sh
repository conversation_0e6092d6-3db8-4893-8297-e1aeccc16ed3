#!/bin/bash

# 完整测试所有MCP传输方式
# 验证stdio、SSE和Streamable传输是否正常工作

SERVER_IP="**************"
echo "=== 完整测试所有MCP传输方式 (服务器IP: $SERVER_IP) ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

success_count=0
total_tests=0

print_result() {
    local test_name="$1"
    local result="$2"
    total_tests=$((total_tests + 1))
    
    if [ "$result" = "success" ]; then
        echo -e "${GREEN}✅ $test_name${NC}"
        success_count=$((success_count + 1))
    else
        echo -e "${RED}❌ $test_name${NC}"
    fi
}

print_section() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_section "1. 基础环境检查"

# 检查Node.js
if command -v node &> /dev/null; then
    print_result "Node.js已安装 ($(node --version))" "success"
else
    print_result "Node.js未安装" "fail"
fi

# 检查npm
if command -v npm &> /dev/null; then
    print_result "npm已安装 ($(npm --version))" "success"
else
    print_result "npm未安装" "fail"
fi

# 检查全局包
if npm list -g @antv/mcp-server-chart &> /dev/null; then
    print_result "mcp-server-chart全局包已安装" "success"
else
    print_result "mcp-server-chart全局包未安装" "fail"
fi

# 检查命令行工具
if command -v mcp-server-chart &> /dev/null; then
    print_result "mcp-server-chart命令行工具可用" "success"
else
    print_result "mcp-server-chart命令行工具不可用" "fail"
fi

print_section "2. Docker服务检查"

# 检查Docker容器
if docker ps --format "{{.Names}}" | grep -q "gpt-vis-ssr"; then
    print_result "渲染服务容器运行中" "success"
else
    print_result "渲染服务容器运行中" "fail"
fi

if docker ps --format "{{.Names}}" | grep -q "mcp-sse-server"; then
    print_result "SSE服务容器运行中" "success"
else
    print_result "SSE服务容器运行中" "fail"
fi

if docker ps --format "{{.Names}}" | grep -q "mcp-streamable-server"; then
    print_result "Streamable服务容器运行中" "success"
else
    print_result "Streamable服务容器运行中" "fail"
fi

print_section "3. 网络连接测试"

# 检查渲染服务
if curl -s --connect-timeout 5 http://localhost:3000/health > /dev/null; then
    print_result "渲染服务本地连接 (localhost:3000)" "success"
else
    print_result "渲染服务本地连接 (localhost:3000)" "fail"
fi

if curl -s --connect-timeout 5 http://$SERVER_IP:3000/health > /dev/null; then
    print_result "渲染服务外部连接 ($SERVER_IP:3000)" "success"
else
    print_result "渲染服务外部连接 ($SERVER_IP:3000)" "fail"
fi

# 检查SSE服务
if curl -s --connect-timeout 5 http://localhost:1122/sse > /dev/null; then
    print_result "SSE服务本地连接 (localhost:1122)" "success"
else
    print_result "SSE服务本地连接 (localhost:1122)" "fail"
fi

if curl -s --connect-timeout 5 http://$SERVER_IP:1122/sse > /dev/null; then
    print_result "SSE服务外部连接 ($SERVER_IP:1122)" "success"
else
    print_result "SSE服务外部连接 ($SERVER_IP:1122)" "fail"
fi

# 检查Streamable服务
if curl -s --connect-timeout 5 http://localhost:1123/mcp > /dev/null; then
    print_result "Streamable服务本地连接 (localhost:1123)" "success"
else
    print_result "Streamable服务本地连接 (localhost:1123)" "fail"
fi

if curl -s --connect-timeout 5 http://$SERVER_IP:1123/mcp > /dev/null; then
    print_result "Streamable服务外部连接 ($SERVER_IP:1123)" "success"
else
    print_result "Streamable服务外部连接 ($SERVER_IP:1123)" "fail"
fi

print_section "4. stdio传输测试"

# 创建临时测试脚本
cat > /tmp/test_stdio.js << 'EOF'
const { spawn } = require('child_process');

const child = spawn('mcp-server-chart', ['--transport', 'stdio'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: { ...process.env, VIS_REQUEST_SERVER: 'http://**************:3000/api/gpt-vis' }
});

const initMessage = {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
        protocolVersion: "2024-11-05",
        capabilities: {
            roots: { listChanged: true },
            sampling: {}
        }
    }
};

child.stdin.write(JSON.stringify(initMessage) + '\n');

let output = '';
child.stdout.on('data', (data) => {
    output += data.toString();
});

setTimeout(() => {
    child.kill();
    if (output.includes('jsonrpc') && output.includes('result')) {
        console.log('SUCCESS');
    } else {
        console.log('FAIL');
    }
}, 5000);
EOF

if node /tmp/test_stdio.js 2>/dev/null | grep -q "SUCCESS"; then
    print_result "stdio传输初始化测试" "success"
else
    print_result "stdio传输初始化测试" "fail"
fi

rm -f /tmp/test_stdio.js

print_section "5. 图表生成测试"

# 测试渲染服务API
echo "测试饼图生成..."
PIE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "stdio", "value": 40},
      {"category": "SSE", "value": 35},
      {"category": "Streamable", "value": 25}
    ],
    "title": "传输方式使用分布"
  }' \
  --connect-timeout 10 \
  --max-time 30 2>/dev/null || echo "")

if echo "$PIE_RESPONSE" | grep -q '"success":true'; then
    print_result "饼图生成API测试" "success"
else
    print_result "饼图生成API测试" "fail"
fi

# 检查图片URL格式
if echo "$PIE_RESPONSE" | grep -q "\"imageUrl\":\"http://$SERVER_IP:3000/images/"; then
    print_result "图片URL格式正确" "success"
    IMAGE_URL=$(echo "$PIE_RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    echo "  图片URL: $IMAGE_URL"
else
    print_result "图片URL格式正确" "fail"
fi

# 测试图片URL访问
if [ -n "$IMAGE_URL" ] && [ "$IMAGE_URL" != "null" ]; then
    if curl -s --head --connect-timeout 10 "$IMAGE_URL" | grep -q "200 OK"; then
        print_result "图片URL可访问" "success"
    else
        print_result "图片URL可访问" "fail"
    fi
fi

print_section "6. 容器间网络测试"

# 检查容器间网络
if docker exec gpt-vis-ssr curl -s http://localhost:3000/health > /dev/null 2>&1; then
    print_result "容器内部网络连接" "success"
else
    print_result "容器内部网络连接" "fail"
fi

# 检查容器间通信
if docker exec mcp-sse-server curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
    print_result "SSE容器到渲染服务通信" "success"
else
    print_result "SSE容器到渲染服务通信" "fail"
fi

if docker exec mcp-streamable-server curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
    print_result "Streamable容器到渲染服务通信" "success"
else
    print_result "Streamable容器到渲染服务通信" "fail"
fi

print_section "7. 多种图表类型测试"

# 测试柱状图
echo "测试柱状图..."
BAR_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "column",
    "data": [
      {"category": "Q1", "value": 100},
      {"category": "Q2", "value": 150},
      {"category": "Q3", "value": 120},
      {"category": "Q4", "value": 180}
    ],
    "title": "季度业绩"
  }' 2>/dev/null)

if echo "$BAR_RESPONSE" | grep -q "\"imageUrl\":\"http://$SERVER_IP:3000/images/"; then
    print_result "柱状图生成并返回正确URL" "success"
else
    print_result "柱状图生成并返回正确URL" "fail"
fi

# 测试折线图
echo "测试折线图..."
LINE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "line",
    "data": [
      {"time": "2024-01", "value": 100},
      {"time": "2024-02", "value": 120},
      {"time": "2024-03", "value": 150},
      {"time": "2024-04", "value": 180}
    ],
    "title": "增长趋势"
  }' 2>/dev/null)

if echo "$LINE_RESPONSE" | grep -q "\"imageUrl\":\"http://$SERVER_IP:3000/images/"; then
    print_result "折线图生成并返回正确URL" "success"
else
    print_result "折线图生成并返回正确URL" "fail"
fi

print_section "8. 配置文件检查"

# 检查配置示例
if [ -d "$HOME/mcp-configs" ]; then
    print_result "配置示例目录存在" "success"
else
    print_result "配置示例目录存在" "fail"
fi

# 检查Claude Desktop配置（macOS）
if [ -f "$HOME/Library/Application Support/Claude/claude_desktop_config.json" ]; then
    print_result "Claude Desktop配置文件存在 (macOS)" "success"
else
    print_result "Claude Desktop配置文件存在 (macOS)" "fail"
fi

print_section "9. 性能测试"

# 测试并发请求
echo "测试并发图表生成..."
CONCURRENT_SUCCESS=0
for i in {1..3}; do
    RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
      -H "Content-Type: application/json" \
      -d "{
        \"type\": \"pie\",
        \"data\": [
          {\"category\": \"测试$i\", \"value\": $((50 + i * 10))},
          {\"category\": \"其他\", \"value\": $((50 - i * 10))}
        ],
        \"title\": \"并发测试$i\"
      }" 2>/dev/null &)
done

wait
sleep 2

# 检查生成的图片数量
IMAGE_COUNT=$(ls images/*.png 2>/dev/null | wc -l)
if [ "$IMAGE_COUNT" -ge 3 ]; then
    print_result "并发图表生成测试 ($IMAGE_COUNT 个图片)" "success"
else
    print_result "并发图表生成测试 ($IMAGE_COUNT 个图片)" "fail"
fi

print_section "测试结果汇总"

echo -e "总测试数: $total_tests"
echo -e "成功数: ${GREEN}$success_count${NC}"
echo -e "失败数: ${RED}$((total_tests - success_count))${NC}"
echo -e "成功率: $(( success_count * 100 / total_tests ))%"

print_section "传输方式状态总结"

# stdio状态
if command -v mcp-server-chart &> /dev/null; then
    echo -e "${GREEN}📱 stdio传输: 可用${NC}"
    echo "   配置: VIS_REQUEST_SERVER=http://$SERVER_IP:3000/api/gpt-vis"
    echo "   用于: Claude Desktop, VSCode, Cursor"
else
    echo -e "${RED}📱 stdio传输: 不可用${NC}"
    echo "   解决: npm install -g @antv/mcp-server-chart"
fi

# SSE状态
if curl -s --connect-timeout 2 http://$SERVER_IP:1122/sse > /dev/null; then
    echo -e "${GREEN}🌐 SSE传输: 可用${NC}"
    echo "   地址: http://$SERVER_IP:1122/sse"
    echo "   用于: Cherry Studio, Web客户端"
else
    echo -e "${RED}🌐 SSE传输: 不可用${NC}"
    echo "   解决: docker compose restart mcp-sse-server"
fi

# Streamable状态
if curl -s --connect-timeout 2 http://$SERVER_IP:1123/mcp > /dev/null; then
    echo -e "${GREEN}🚀 Streamable传输: 可用${NC}"
    echo "   地址: http://$SERVER_IP:1123/mcp"
    echo "   用于: 高性能客户端"
else
    echo -e "${RED}🚀 Streamable传输: 不可用${NC}"
    echo "   解决: docker compose restart mcp-streamable-server"
fi

print_section "推荐配置"

echo "🎯 根据客户端选择传输方式:"
echo ""
echo "1. Claude Desktop (推荐 stdio):"
echo '   "VIS_REQUEST_SERVER": "http://**************:3000/api/gpt-vis"'
echo ""
echo "2. Cherry Studio (推荐 SSE):"
echo "   服务器URL: http://**************:1122/sse"
echo ""
echo "3. 高性能应用 (推荐 Streamable):"
echo "   服务器URL: http://**************:1123/mcp"
echo ""
echo "📖 详细配置: client-configs-complete.md"
echo "🔧 故障排除: 如果成功率低于80%，请重新部署服务"

if [ $success_count -lt $((total_tests * 4 / 5)) ]; then
    echo -e "${YELLOW}⚠️ 检测到多个问题，建议重新部署:${NC}"
    echo "   sudo ./deploy-all-transports-fixed.sh"
fi
