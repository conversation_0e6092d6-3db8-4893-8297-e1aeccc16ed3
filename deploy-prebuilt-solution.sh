#!/bin/bash

# 预构建解决方案
# 在宿主机上构建，然后在容器中运行

set -e

echo "=== 预构建MCP解决方案 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 安装Node.js（如果没有安装）
if ! command -v node &> /dev/null; then
    echo "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    apt-get install -y nodejs
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

APP_DIR="/opt/mcp-prebuilt"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# 停止现有服务
echo "停止现有Docker服务..."
cd /opt/mcp-server-chart 2>/dev/null && docker compose down 2>/dev/null || true
docker stop mcp-all-in-one 2>/dev/null || true
docker rm mcp-all-in-one 2>/dev/null || true

cd $APP_DIR

# 克隆并构建MCP项目（在宿主机上）
echo "在宿主机上构建MCP项目..."
if [ ! -d "mcp-server-chart" ]; then
    git clone https://github.com/antvis/mcp-server-chart.git
fi

cd mcp-server-chart

# 安装依赖并构建（跳过prepare脚本）
echo "安装依赖..."
npm install --ignore-scripts

echo "检查可用脚本..."
npm run

echo "尝试构建项目..."
# 检查是否有build脚本
if npm run | grep -q "build"; then
    echo "找到build脚本，开始构建..."
    npm run build
else
    echo "没有找到build脚本，尝试使用tsc编译..."
    # 检查是否有TypeScript配置
    if [ -f "tsconfig.json" ]; then
        echo "找到tsconfig.json，使用tsc编译..."
        npx tsc
    else
        echo "没有找到tsconfig.json，跳过构建步骤..."
        echo "检查是否已有构建文件..."
        if [ -d "build" ] || [ -d "dist" ] || [ -d "lib" ]; then
            echo "找到已存在的构建目录"
        else
            echo "创建简单的构建目录..."
            mkdir -p build
            # 如果有src目录，复制文件
            if [ -d "src" ]; then
                cp -r src/* build/ 2>/dev/null || true
            fi
        fi
    fi
fi

cd $APP_DIR

# 创建渲染服务目录
echo "创建渲染服务..."
mkdir -p render-service
cd render-service

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0"
  }
}
EOF

# 创建服务器代码
cat > server.js << 'EOF'
const express = require('express');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();
app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
});
EOF

cd $APP_DIR

# 创建Docker Compose配置（使用预构建的代码）
echo "创建Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++
        npm install
        npm start
    restart: unless-stopped
    networks:
      - mcp-network

  mcp-chart-server:
    image: node:18-alpine
    container_name: mcp-chart-server
    working_dir: /app
    ports:
      - "1122:1122"
    volumes:
      - ./mcp-server-chart:/app
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "MCP服务启动中..."
        echo "检查项目结构..."
        ls -la
        echo "检查可用脚本..."
        npm run || true
        echo "尝试构建项目..."
        if npm run | grep -q "build"; then
          echo "找到build脚本，开始构建..."
          npm run build
        else
          echo "没有找到build脚本，尝试使用tsc编译..."
          if [ -f "tsconfig.json" ]; then
            echo "找到tsconfig.json，使用tsc编译..."
            npx tsc
          else
            echo "没有找到tsconfig.json，检查现有文件..."
            if [ -d "build" ] || [ -d "dist" ] || [ -d "lib" ]; then
              echo "找到已存在的构建目录"
            else
              echo "创建构建目录并复制源文件..."
              mkdir -p build
              if [ -d "src" ]; then
                cp -r src/* build/ 2>/dev/null || true
              fi
              # 如果有index.js在根目录，复制到build目录
              if [ -f "index.js" ]; then
                cp index.js build/
              fi
            fi
          fi
        fi
        echo "检查构建结果..."
        ls -la build/ || ls -la dist/ || ls -la lib/ || ls -la
        echo "查找入口文件..."
        ENTRY_FILE=""
        if [ -f "build/index.js" ]; then
          ENTRY_FILE="build/index.js"
        elif [ -f "dist/index.js" ]; then
          ENTRY_FILE="dist/index.js"
        elif [ -f "lib/index.js" ]; then
          ENTRY_FILE="lib/index.js"
        elif [ -f "index.js" ]; then
          ENTRY_FILE="index.js"
        elif [ -f "src/index.js" ]; then
          ENTRY_FILE="src/index.js"
        else
          echo "错误：找不到入口文件"
          exit 1
        fi
        echo "使用入口文件: $ENTRY_FILE"
        echo "启动MCP服务器..."
        node $ENTRY_FILE --transport sse --port 1122
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
EOF

# 创建图片目录
mkdir -p images

echo "启动Docker服务..."
docker compose up -d

echo "等待服务启动..."
sleep 60

echo "检查服务状态..."
docker compose ps

echo "查看服务日志..."
echo "--- 渲染服务日志 ---"
docker compose logs --tail=10 gpt-vis-ssr

echo ""
echo "--- MCP服务日志 ---"
docker compose logs --tail=10 mcp-chart-server

echo ""
echo "=== 测试服务 ==="
echo "测试渲染服务..."
for i in {1..5}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务响应正常"
        curl -s http://localhost:3000/health | jq . 2>/dev/null || curl -s http://localhost:3000/health
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "测试MCP服务..."
for i in {1..5}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✅ MCP服务响应正常"
        break
    else
        echo "⏳ 等待MCP服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "测试图表生成..."
curl -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "line",
    "data": [
      {"time": "2025-01", "value": 100},
      {"time": "2025-02", "value": 200}
    ]
  }' | head -c 200

echo ""
echo ""
echo "=== 部署完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - 渲染服务健康检查: http://localhost:3000/health"
echo "  - MCP服务: http://localhost:1122/sse"
echo ""
echo "管理命令:"
echo "  查看日志: docker compose logs -f"
echo "  重启服务: docker compose restart"
echo "  停止服务: docker compose down"
