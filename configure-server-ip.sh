#!/bin/bash

# MCP Chart Server IP 配置脚本
# 用于配置服务器IP地址，解决局域网访问问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 自动检测服务器IP
detect_server_ip() {
    log_step "自动检测服务器IP地址..."
    
    # 尝试多种方法获取IP
    local ip=""
    
    # 方法1: 通过默认路由获取
    if command -v ip >/dev/null 2>&1; then
        ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
    fi
    
    # 方法2: 通过hostname获取
    if [[ -z "$ip" ]] && command -v hostname >/dev/null 2>&1; then
        ip=$(hostname -I 2>/dev/null | awk '{print $1}')
    fi
    
    # 方法3: 通过ifconfig获取
    if [[ -z "$ip" ]] && command -v ifconfig >/dev/null 2>&1; then
        ip=$(ifconfig 2>/dev/null | grep -E 'inet.*192\.168\.|inet.*10\.|inet.*172\.' | head -1 | awk '{print $2}' | cut -d: -f2)
    fi
    
    # 方法4: 通过/proc/net/route获取
    if [[ -z "$ip" ]] && [[ -f "/proc/net/route" ]]; then
        local interface=$(awk '$2 == 00000000 { print $1 }' /proc/net/route | head -1)
        if [[ -n "$interface" ]]; then
            ip=$(ip addr show "$interface" 2>/dev/null | grep -oP 'inet \K[\d.]+' | head -1)
        fi
    fi
    
    if [[ -n "$ip" ]]; then
        log_info "检测到服务器IP: $ip"
        echo "$ip"
    else
        log_warn "无法自动检测IP地址"
        echo ""
    fi
}

# 验证IP地址格式
validate_ip() {
    local ip="$1"
    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        IFS='.' read -ra ADDR <<< "$ip"
        for i in "${ADDR[@]}"; do
            if [[ $i -gt 255 ]]; then
                return 1
            fi
        done
        return 0
    else
        return 1
    fi
}

# 更新Docker Compose配置
update_docker_compose() {
    local server_ip="$1"
    local use_https="${2:-false}"
    local nginx_port="${3:-80}"
    local gpt_vis_port="${4:-3000}"

    log_step "更新 Docker Compose 配置..."

    if [[ ! -f "docker-compose.yml" ]]; then
        log_error "docker-compose.yml 文件不存在"
        return 1
    fi

    # 备份原文件
    cp docker-compose.yml docker-compose.yml.backup

    # 更新环境变量
    sed -i.tmp "s/SERVER_IP=.*/SERVER_IP=${server_ip}/" docker-compose.yml
    sed -i.tmp "s/USE_HTTPS=.*/USE_HTTPS=${use_https}/" docker-compose.yml
    sed -i.tmp "s/NGINX_PORT=.*/NGINX_PORT=${nginx_port}/" docker-compose.yml

    # 更新GPT-Vis-SSR端口映射（如果需要自定义端口）
    if [[ "$gpt_vis_port" != "3000" ]]; then
        sed -i.tmp "s/\"3000:3000\"/\"${gpt_vis_port}:3000\"/" docker-compose.yml
        sed -i.tmp "s/PORT=3000/PORT=3000/" docker-compose.yml  # 容器内部端口保持3000
    fi

    # 清理临时文件
    rm -f docker-compose.yml.tmp

    log_info "Docker Compose 配置已更新"
    log_info "GPT-Vis-SSR 端口: ${gpt_vis_port}"
}

# 更新GPT-Vis-SSR配置
update_gpt_vis_ssr() {
    local server_ip="$1"
    local gpt_vis_port="${2:-3000}"

    log_step "更新 GPT-Vis-SSR 配置..."

    if [[ ! -f "gpt-vis-ssr/index.js" ]]; then
        log_error "gpt-vis-ssr/index.js 文件不存在"
        return 1
    fi

    # 备份原文件
    cp gpt-vis-ssr/index.js gpt-vis-ssr/index.js.backup

    # 更新默认IP地址
    sed -i.tmp "s/const serverIP = process.env.SERVER_IP || '.*'/const serverIP = process.env.SERVER_IP || '${server_ip}'/" gpt-vis-ssr/index.js

    # 清理临时文件
    rm -f gpt-vis-ssr/index.js.tmp

    log_info "GPT-Vis-SSR 配置已更新"
    log_info "图片URL将使用端口: ${gpt_vis_port}"
}

# 创建Cherry Studio配置
create_cherry_studio_config() {
    local server_ip="$1"
    local use_https="${2:-false}"
    local nginx_port="${3:-80}"
    
    log_step "创建 Cherry Studio 配置..."
    
    local protocol="http"
    if [[ "$use_https" == "true" ]]; then
        protocol="https"
    fi
    
    # 根据协议和端口决定是否显示端口号
    local port_suffix=""
    if [[ "$protocol" == "http" && "$nginx_port" != "80" ]] || [[ "$protocol" == "https" && "$nginx_port" != "443" ]]; then
        port_suffix=":${nginx_port}"
    fi

    local base_url="${protocol}://${server_ip}${port_suffix}"
    
    # 创建配置目录
    mkdir -p configs
    
    # 创建SSE配置
    cat > configs/cherry-studio-sse-config.json << EOF
{
  "mcpServers": {
    "mcp-chart-server-sse": {
      "command": "node",
      "args": [
        "-e",
        "const { SSEClientTransport } = require('@modelcontextprotocol/sdk/client/sse.js'); const { Client } = require('@modelcontextprotocol/sdk/client/index.js'); const transport = new SSEClientTransport(new URL('${base_url}/sse')); const client = new Client({ name: 'cherry-studio', version: '1.0.0' }, { capabilities: {} }); client.connect(transport);"
      ],
      "env": {
        "MCP_SERVER_URL": "${base_url}/sse"
      }
    }
  }
}
EOF

    # 创建Streamable配置
    cat > configs/cherry-studio-streamable-config.json << EOF
{
  "mcpServers": {
    "mcp-chart-server-streamable": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/sdk",
        "client",
        "${base_url}/mcp"
      ],
      "env": {
        "MCP_SERVER_URL": "${base_url}/mcp"
      }
    }
  }
}
EOF

    # 创建简化的HTTP配置
    cat > configs/cherry-studio-http-config.json << EOF
{
  "mcpServers": {
    "mcp-chart-server": {
      "command": "curl",
      "args": [
        "-X", "POST",
        "-H", "Content-Type: application/json",
        "${base_url}/sse"
      ],
      "env": {
        "MCP_SERVER_URL": "${base_url}/sse",
        "MCP_STREAMABLE_URL": "${base_url}/mcp"
      }
    }
  }
}
EOF

    log_info "Cherry Studio 配置文件已创建:"
    echo "  - configs/cherry-studio-sse-config.json (SSE传输)"
    echo "  - configs/cherry-studio-streamable-config.json (Streamable传输)"
    echo "  - configs/cherry-studio-http-config.json (HTTP配置)"
}

# 创建测试脚本
create_test_script() {
    local server_ip="$1"
    local use_https="${2:-false}"
    local nginx_port="${3:-80}"
    
    log_step "创建测试脚本..."
    
    local protocol="http"
    if [[ "$use_https" == "true" ]]; then
        protocol="https"
    fi
    
    # 根据协议和端口决定是否显示端口号
    local port_suffix=""
    if [[ "$protocol" == "http" && "$nginx_port" != "80" ]] || [[ "$protocol" == "https" && "$nginx_port" != "443" ]]; then
        port_suffix=":${nginx_port}"
    fi

    local base_url="${protocol}://${server_ip}${port_suffix}"
    
    cat > test-lan-access.sh << 'EOF'
#!/bin/bash

# 局域网访问测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

EOF

    cat >> test-lan-access.sh << EOF
BASE_URL="${base_url}"

log_step "测试局域网访问..."

# 测试健康检查
log_info "测试健康检查: \${BASE_URL}/health"
if curl -s "\${BASE_URL}/health" >/dev/null 2>&1; then
    log_info "✓ 健康检查通过"
else
    log_error "✗ 健康检查失败"
    exit 1
fi

# 测试图表渲染
log_info "测试图表渲染: \${BASE_URL}/api/gpt-vis"
test_data='{
    "type": "line",
    "data": [
        {"time": "2023-01", "value": 100},
        {"time": "2023-02", "value": 120},
        {"time": "2023-03", "value": 150}
    ]
}'

response=\$(curl -s -X POST "\${BASE_URL}/api/gpt-vis" \\
    -H "Content-Type: application/json" \\
    -d "\$test_data")

if echo "\$response" | grep -q '"success":true'; then
    log_info "✓ 图表渲染成功"
    image_url=\$(echo "\$response" | grep -o '"resultObj":"[^"]*"' | cut -d'"' -f4)
    log_info "图片URL: \$image_url"
    
    # 测试图片访问
    if curl -s "\$image_url" >/dev/null 2>&1; then
        log_info "✓ 图片访问成功"
    else
        log_error "✗ 图片访问失败"
    fi
else
    log_error "✗ 图表渲染失败"
    echo "\$response"
fi

# 测试MCP端点
log_info "测试MCP SSE端点: \${BASE_URL}/sse"
if timeout 5 curl -s "\${BASE_URL}/sse" >/dev/null 2>&1; then
    log_info "✓ MCP SSE端点响应正常"
else
    log_error "✗ MCP SSE端点响应异常"
fi

log_info "测试MCP Streamable端点: \${BASE_URL}/mcp"
if timeout 5 curl -s "\${BASE_URL}/mcp" >/dev/null 2>&1; then
    log_info "✓ MCP Streamable端点响应正常"
else
    log_error "✗ MCP Streamable端点响应异常"
fi

log_info "局域网访问测试完成！"
EOF

    chmod +x test-lan-access.sh
    
    log_info "测试脚本已创建: test-lan-access.sh"
}

# 显示配置信息
show_config_info() {
    local server_ip="$1"
    local use_https="${2:-false}"
    local nginx_port="${3:-80}"
    
    local protocol="http"
    if [[ "$use_https" == "true" ]]; then
        protocol="https"
    fi
    
    # 根据协议和端口决定是否显示端口号
    local port_suffix=""
    if [[ "$protocol" == "http" && "$nginx_port" != "80" ]] || [[ "$protocol" == "https" && "$nginx_port" != "443" ]]; then
        port_suffix=":${nginx_port}"
    fi

    local base_url="${protocol}://${server_ip}${port_suffix}"
    
    echo
    log_info "=== 配置完成 ==="
    echo
    echo "服务器IP: $server_ip"
    echo "协议: $protocol"
    echo "端口: $nginx_port"
    echo "基础URL: $base_url"
    echo
    echo "服务访问地址:"
    echo "  - MCP Server (SSE):     ${base_url}/sse"
    echo "  - MCP Server (Stream):  ${base_url}/mcp"
    echo "  - GPT-Vis-SSR API:     ${base_url}/api/gpt-vis"
    echo "  - 健康检查:             ${base_url}/health"
    echo "  - 图片存储:             ${base_url}/images/"
    echo
    echo "图片URL格式:"
    echo "  - 返回的图片URL:        ${protocol}://${server_ip}:3000/images/xxx.png"
    echo
    echo "Cherry Studio 配置文件:"
    echo "  - configs/cherry-studio-sse-config.json"
    echo "  - configs/cherry-studio-streamable-config.json"
    echo "  - configs/cherry-studio-http-config.json"
    echo
    echo "测试命令:"
    echo "  ./test-lan-access.sh"
    echo
    echo "重新部署服务:"
    echo "  docker-compose down && docker-compose up -d"
    echo
}

# 主函数
main() {
    echo "========================================"
    echo "  MCP Chart Server IP 配置工具"
    echo "========================================"
    echo
    
    local server_ip=""
    local use_https="false"
    local nginx_port="80"
    local auto_detect="true"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --ip)
                server_ip="$2"
                auto_detect="false"
                shift 2
                ;;
            --https)
                use_https="true"
                nginx_port="443"
                shift
                ;;
            --port)
                nginx_port="$2"
                shift 2
                ;;
            --no-auto)
                auto_detect="false"
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo
                echo "选项:"
                echo "  --ip IP        指定服务器IP地址"
                echo "  --https        启用HTTPS (默认端口443)"
                echo "  --port PORT    指定端口 (默认80)"
                echo "  --no-auto      不自动检测IP"
                echo "  -h, --help     显示帮助信息"
                echo
                echo "示例:"
                echo "  $0                           # 自动检测IP"
                echo "  $0 --ip **************       # 指定IP"
                echo "  $0 --ip ************** --https  # 使用HTTPS"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 自动检测或手动输入IP
    if [[ "$auto_detect" == "true" ]] && [[ -z "$server_ip" ]]; then
        server_ip=$(detect_server_ip)
    fi
    
    if [[ -z "$server_ip" ]]; then
        echo "请输入服务器IP地址 (例如: **************):"
        read -r server_ip
    fi
    
    # 验证IP地址
    if ! validate_ip "$server_ip"; then
        log_error "无效的IP地址: $server_ip"
        exit 1
    fi
    
    # 确认配置
    echo
    log_info "配置信息:"
    echo "  服务器IP: $server_ip"
    echo "  使用HTTPS: $use_https"
    echo "  端口: $nginx_port"
    echo
    read -p "确认配置? (y/N): " confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "配置取消"
        exit 0
    fi
    
    # 执行配置更新
    local gpt_vis_port="3000"  # GPT-Vis-SSR固定使用3000端口
    update_docker_compose "$server_ip" "$use_https" "$nginx_port" "$gpt_vis_port"
    update_gpt_vis_ssr "$server_ip" "$gpt_vis_port"
    create_cherry_studio_config "$server_ip" "$use_https" "$nginx_port"
    create_test_script "$server_ip" "$use_https" "$nginx_port"
    
    # 显示配置信息
    show_config_info "$server_ip" "$use_https" "$nginx_port"
}

# 如果脚本被直接执行，则运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
