#!/bin/bash

# 修复LobeChat MCP配置脚本
# 为MCP服务器添加manifest端点支持

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 创建manifest端点的Nginx配置
add_manifest_endpoint() {
    log_step "为LobeChat添加manifest端点..."
    
    # 备份原始nginx配置
    cp nginx.conf nginx.conf.lobechat.backup
    
    # 在nginx配置中添加manifest端点
    cat > nginx-lobechat-patch.conf << 'EOF'
        # LobeChat Manifest端点
        location /manifest {
            proxy_pass http://mcp_server_sse/manifest;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 添加CORS头
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            
            if ($request_method = 'OPTIONS') {
                return 204;
            }
        }

        # LobeChat MCP端点 (Streamable HTTP)
        location /mcp/stream {
            proxy_pass http://mcp_server_streamable/mcp;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Streamable特定配置
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 24h;
            
            # 添加CORS头
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        }
EOF

    # 将patch插入到nginx配置中
    sed -i.tmp '/location \/mcp {/r nginx-lobechat-patch.conf' nginx.conf
    
    # 清理临时文件
    rm nginx-lobechat-patch.conf nginx.conf.tmp
    
    log_info "Nginx配置已更新，添加了LobeChat支持"
}

# 创建简单的manifest服务
create_manifest_service() {
    log_step "创建manifest服务..."
    
    mkdir -p lobechat-support
    
    cat > lobechat-support/manifest-server.js << 'EOF'
const express = require('express');
const { spawn } = require('child_process');
const app = express();
const port = process.env.MANIFEST_PORT || 3001;

app.use(express.json());

// 启用CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(204);
        return;
    }
    next();
});

// Manifest端点
app.get('/manifest', async (req, res) => {
    try {
        const manifest = {
            "schemaVersion": "2024-11-05",
            "vendor": "antv",
            "name": "mcp-server-chart",
            "version": "1.0.0",
            "description": "Chart generation server using GPT-Vis-SSR",
            "author": "AntV Team",
            "homepage": "https://github.com/antvis/mcp-server-chart",
            "capabilities": {
                "tools": {}
            },
            "tools": [
                {
                    "name": "generate_area_chart",
                    "description": "Generate area chart to show data trends",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "data": {
                                "type": "array",
                                "description": "Chart data"
                            },
                            "title": {
                                "type": "string",
                                "description": "Chart title"
                            }
                        },
                        "required": ["data"]
                    }
                },
                {
                    "name": "generate_bar_chart",
                    "description": "Generate bar chart for categorical data comparison",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "data": {
                                "type": "array",
                                "description": "Chart data"
                            },
                            "title": {
                                "type": "string",
                                "description": "Chart title"
                            }
                        },
                        "required": ["data"]
                    }
                },
                {
                    "name": "generate_line_chart",
                    "description": "Generate line chart to show trends over time",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "data": {
                                "type": "array",
                                "description": "Chart data"
                            },
                            "title": {
                                "type": "string",
                                "description": "Chart title"
                            }
                        },
                        "required": ["data"]
                    }
                },
                {
                    "name": "generate_pie_chart",
                    "description": "Generate pie chart to show proportions",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "data": {
                                "type": "array",
                                "description": "Chart data"
                            },
                            "title": {
                                "type": "string",
                                "description": "Chart title"
                            }
                        },
                        "required": ["data"]
                    }
                },
                {
                    "name": "generate_column_chart",
                    "description": "Generate column chart for comparing categorical data",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "data": {
                                "type": "array",
                                "description": "Chart data"
                            },
                            "title": {
                                "type": "string",
                                "description": "Chart title"
                            }
                        },
                        "required": ["data"]
                    }
                }
            ]
        };
        
        res.json(manifest);
    } catch (error) {
        console.error('Error generating manifest:', error);
        res.status(500).json({ error: 'Failed to generate manifest' });
    }
});

// MCP代理端点
app.post('/mcp', async (req, res) => {
    try {
        // 代理到实际的MCP服务器
        const response = await fetch('http://mcp-server-chart-streamable:1123/mcp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body)
        });
        
        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('Error proxying MCP request:', error);
        res.status(500).json({ error: 'Failed to proxy MCP request' });
    }
});

app.listen(port, '0.0.0.0', () => {
    console.log(`Manifest server running on http://0.0.0.0:${port}`);
});
EOF

    cat > lobechat-support/package.json << 'EOF'
{
  "name": "lobechat-mcp-support",
  "version": "1.0.0",
  "description": "LobeChat MCP support service",
  "main": "manifest-server.js",
  "scripts": {
    "start": "node manifest-server.js"
  },
  "dependencies": {
    "express": "^4.18.2"
  }
}
EOF

    log_info "Manifest服务创建完成"
}

# 更新Docker Compose配置
update_docker_compose_for_lobechat() {
    log_step "更新Docker Compose配置以支持LobeChat..."
    
    # 备份原始配置
    cp docker-compose.yml docker-compose.yml.lobechat.backup
    
    # 添加manifest服务
    cat >> docker-compose.yml << 'EOF'

  # LobeChat MCP支持服务
  lobechat-mcp-support:
    build: ./lobechat-support
    container_name: lobechat-mcp-support
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MANIFEST_PORT=3001
    depends_on:
      - mcp-server-chart-streamable
    restart: unless-stopped
EOF

    # 更新nginx配置以包含manifest服务
    sed -i.tmp 's/server mcp-server-chart-streamable:1123;/server mcp-server-chart-streamable:1123;\n    }\n\n    upstream lobechat_support {\n        server lobechat-mcp-support:3001;/' docker-compose.yml
    
    log_info "Docker Compose配置已更新"
}

# 创建LobeChat配置文件
create_lobechat_config() {
    log_step "创建LobeChat配置文件..."
    
    mkdir -p configs
    
    local server_ip=$(grep "SERVER_IP=" docker-compose.yml 2>/dev/null | cut -d'=' -f2 | head -1)
    if [[ -z "$server_ip" ]]; then
        server_ip="**************"
    fi
    
    cat > configs/lobechat-mcp-config.json << EOF
{
  "mcpServers": {
    "mcp-chart-server": {
      "type": "streamable-http",
      "url": "http://${server_ip}/mcp/stream",
      "manifest": "http://${server_ip}/manifest",
      "name": "Chart Generator",
      "description": "Generate various types of charts and visualizations"
    }
  }
}
EOF

    log_info "LobeChat配置文件已创建: configs/lobechat-mcp-config.json"
}

# 主函数
main() {
    echo "========================================"
    echo "  修复LobeChat MCP配置"
    echo "========================================"
    echo
    
    # 检查必要文件
    if [[ ! -f "docker-compose.yml" ]]; then
        log_error "docker-compose.yml 不存在，请先运行主部署脚本"
        exit 1
    fi
    
    if [[ ! -f "nginx.conf" ]]; then
        log_error "nginx.conf 不存在，请先运行主部署脚本"
        exit 1
    fi
    
    # 执行修复步骤
    create_manifest_service
    add_manifest_endpoint
    update_docker_compose_for_lobechat
    create_lobechat_config
    
    echo
    log_info "=== 修复完成 ==="
    echo
    echo "下一步操作："
    echo "1. 重启服务: docker-compose down && docker-compose up -d"
    echo "2. 等待服务启动: sleep 15"
    echo "3. 测试manifest端点: curl http://**************/manifest"
    echo "4. 在LobeChat中使用配置文件: configs/lobechat-mcp-config.json"
    echo
}

# 如果脚本被直接执行，则运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
