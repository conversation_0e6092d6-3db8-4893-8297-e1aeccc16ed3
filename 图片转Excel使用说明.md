# 图片转Excel工具使用说明

## 功能描述
本工具使用OCR（光学字符识别）技术，将包含表格的图片转换为Excel文件。特别适用于处理中文表格图片。

## 系统要求
- Python 3.7+
- Windows/Linux/macOS

## 安装步骤

### 1. 安装Python依赖包
```bash
pip install -r requirements.txt
```

### 2. 安装Tesseract OCR引擎

#### Windows系统：
1. 下载Tesseract安装包：https://github.com/UB-Mannheim/tesseract/wiki
2. 运行安装程序，建议安装到默认路径
3. 安装时确保选择中文语言包（chi_sim）

#### Linux系统：
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim

# CentOS/RHEL
sudo yum install tesseract tesseract-langpack-chi-sim
```

#### macOS系统：
```bash
brew install tesseract tesseract-lang
```

## 使用方法

### 方法一：命令行运行
```bash
python image_to_excel.py
```
然后按提示输入图片文件路径。

### 方法二：直接拖拽
1. 运行脚本
2. 将图片文件直接拖拽到命令行窗口
3. 按回车确认

## 支持的图片格式
- JPG/JPEG
- PNG
- BMP
- TIFF
- GIF

## 使用技巧

### 1. 图片质量要求
- **分辨率**：建议300DPI以上
- **清晰度**：文字清晰，无模糊
- **对比度**：黑白对比明显
- **倾斜度**：尽量保持水平，避免倾斜

### 2. 表格格式要求
- **边框清晰**：表格线条清楚
- **字体大小**：不要太小，建议12号字以上
- **背景干净**：避免复杂背景
- **光照均匀**：避免阴影和反光

### 3. 提高识别率的方法
- 使用扫描仪而非手机拍照
- 确保图片水平放置
- 调整图片亮度和对比度
- 裁剪掉不必要的边缘部分

## 输出说明

### 文件命名
输出的Excel文件将以原图片名称命名，例如：
- 输入：`表格.jpg`
- 输出：`表格_转换结果.xlsx`

### Excel格式
- 自动设置边框
- 居中对齐
- 自动调整列宽
- 使用宋体字体

## 常见问题

### Q1: 提示"未找到Tesseract OCR"
**解决方案：**
1. 确认已正确安装Tesseract
2. 检查安装路径是否正确
3. 将Tesseract添加到系统PATH环境变量

### Q2: 识别出的文字有乱码
**解决方案：**
1. 确认安装了中文语言包（chi_sim）
2. 检查图片质量是否足够清晰
3. 尝试调整图片的对比度和亮度

### Q3: 表格结构识别不准确
**解决方案：**
1. 确保表格边框清晰
2. 检查单元格之间是否有足够的间距
3. 手动调整生成的Excel文件

### Q4: 某些字符识别错误
**解决方案：**
1. 提高图片分辨率
2. 确保字体大小适中
3. 检查图片是否有倾斜，必要时旋转图片

## 技术原理

### OCR处理流程
1. **图片预处理**：灰度化、去噪、二值化
2. **文字识别**：使用Tesseract引擎识别文字
3. **结构解析**：分析文字布局，识别表格结构
4. **数据整理**：标准化表格数据
5. **Excel生成**：创建格式化的Excel文件

### 图片预处理技术
- **高斯模糊**：去除图片噪点
- **OTSU阈值**：自动确定最佳二值化阈值
- **形态学操作**：优化字符形状

## 注意事项

1. **版权声明**：请确保您有权处理和转换这些图片内容
2. **数据准确性**：OCR识别可能存在错误，请核对重要数据
3. **隐私保护**：处理敏感信息时请注意数据安全
4. **文件备份**：建议保留原始图片文件作为备份

## 更新日志

### v1.0.0
- 初始版本发布
- 支持中英文OCR识别
- 自动表格结构解析
- Excel格式化输出

## 技术支持

如遇到问题，请检查：
1. Python版本是否符合要求
2. 所有依赖包是否正确安装
3. Tesseract OCR是否正确配置
4. 图片格式和质量是否符合要求

## 许可证
本工具仅供学习和个人使用。
