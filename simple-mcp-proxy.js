const express = require('express');
const { spawn } = require('child_process');
const app = express();
const port = 1123;

app.use(express.json());

// 启用CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(204);
        return;
    }
    next();
});

// 启动本地MCP进程
let mcpProcess = null;

function startMCPProcess() {
    if (mcpProcess) {
        mcpProcess.kill();
    }
    
    mcpProcess = spawn('npx', ['-y', '@antv/mcp-server-chart'], {
        env: {
            ...process.env,
            VIS_REQUEST_SERVER: 'http://gpt-vis-ssr:3000/api/gpt-vis'
        },
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    console.log('MCP进程已启动');
    
    mcpProcess.on('error', (error) => {
        console.error('MCP进程错误:', error);
    });
    
    mcpProcess.on('exit', (code) => {
        console.log('MCP进程退出，代码:', code);
    });
}

// 处理MCP请求
app.post('/', async (req, res) => {
    try {
        if (!mcpProcess) {
            startMCPProcess();
            // 等待进程启动
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        const request = req.body;
        console.log('收到请求:', JSON.stringify(request, null, 2));
        
        // 发送请求到MCP进程
        mcpProcess.stdin.write(JSON.stringify(request) + '\n');
        
        // 等待响应
        let responseData = '';
        
        const responsePromise = new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('请求超时'));
            }, 30000);
            
            const onData = (data) => {
                responseData += data.toString();
                try {
                    const response = JSON.parse(responseData);
                    clearTimeout(timeout);
                    mcpProcess.stdout.removeListener('data', onData);
                    resolve(response);
                } catch (e) {
                    // 继续等待更多数据
                }
            };
            
            mcpProcess.stdout.on('data', onData);
        });
        
        const response = await responsePromise;
        console.log('发送响应:', JSON.stringify(response, null, 2));
        res.json(response);
        
    } catch (error) {
        console.error('处理请求错误:', error);
        res.status(500).json({
            jsonrpc: "2.0",
            id: req.body.id || null,
            error: {
                code: -32603,
                message: "Internal error",
                data: error.message
            }
        });
    }
});

// 健康检查
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器
app.listen(port, '0.0.0.0', () => {
    console.log(`简单MCP代理服务运行在 http://0.0.0.0:${port}`);
    startMCPProcess();
});

// 优雅关闭
process.on('SIGTERM', () => {
    if (mcpProcess) {
        mcpProcess.kill();
    }
    process.exit(0);
});

process.on('SIGINT', () => {
    if (mcpProcess) {
        mcpProcess.kill();
    }
    process.exit(0);
});
