#!/bin/bash

# MCP Server Chart Docker部署脚本
# 使用Docker避免编译问题

set -e

echo "=== MCP Server Chart Docker部署脚本 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 更新系统
echo "更新系统包..."
apt update && apt upgrade -y

# 安装Docker
echo "安装Docker..."
apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# 更新包索引并安装Docker
apt update
apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
systemctl start docker
systemctl enable docker

echo "Docker版本: $(docker --version)"

# 创建应用目录
APP_DIR="/opt/mcp-server-chart"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# 创建Docker Compose文件
echo "创建Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./gpt-vis-ssr:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    command: sh -c "apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++ && npm install && npm start"
    restart: unless-stopped
    networks:
      - mcp-network

  mcp-chart-server:
    image: node:18-alpine
    container_name: mcp-chart-server
    working_dir: /app
    ports:
      - "1122:1122"
    volumes:
      - ./mcp-server-chart:/app
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    command: sh -c "npm install && npm run build && node build/index.js --transport sse --port 1122"
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge

volumes:
  images:
EOF

# 创建渲染服务目录和文件
echo "创建渲染服务..."
mkdir -p gpt-vis-ssr
cd gpt-vis-ssr

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0"
  }
}
EOF

# 创建服务器代码
cat > server.js << 'EOF'
const express = require('express');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();
app.use(express.json({ limit: '10mb' }));

// 创建图片存储目录
const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    // 生成唯一文件名
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    // 保存图片到文件
    fs.writeFileSync(filepath, buffer);
    
    // 返回base64编码的图片
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.json({
      success: false,
      errorMessage: error.message
    });
  }
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 静态文件服务
app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
});
EOF

# 返回主目录
cd $APP_DIR

# 克隆MCP项目
echo "克隆MCP Server Chart项目..."
git clone https://github.com/antvis/mcp-server-chart.git

# 创建图片存储目录
mkdir -p images

# 启动服务
echo "启动Docker服务..."
docker compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
docker compose ps

# 测试服务
echo "测试服务..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✓ 渲染服务启动成功"
else
    echo "✗ 渲染服务启动失败"
fi

if curl -s http://localhost:1122/sse > /dev/null; then
    echo "✓ MCP服务启动成功"
else
    echo "✗ MCP服务启动失败"
fi

echo ""
echo "=== Docker部署完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - MCP服务: http://localhost:1122/sse"
echo ""
echo "管理命令:"
echo "  查看日志: docker compose logs -f"
echo "  重启服务: docker compose restart"
echo "  停止服务: docker compose down"
echo "  查看状态: docker compose ps"
