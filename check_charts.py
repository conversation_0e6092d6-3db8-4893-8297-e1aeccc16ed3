#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import openpyxl

def check_charts():
    """检查生成的Excel文件中的图表和分析内容"""
    
    # 读取最新生成的文件
    result_file = "2025年省抽任务分析结果_20250721_112748.xlsx"
    
    try:
        wb = openpyxl.load_workbook(result_file)
        ws = wb['汇总统计']
        
        print("=== 汇总统计sheet图表分析检查 ===")
        print(f"总行数: {ws.max_row}")
        print(f"总列数: {ws.max_column}")
        print(f"图表数量: {len(ws._charts)}")
        
        print("\n=== 进度分析数据表格 ===")
        # 查找进度分析标题
        analysis_start_row = None
        for row in range(1, ws.max_row + 1):
            cell_value = ws.cell(row=row, column=1).value
            if cell_value and "任务批数进度分析" in str(cell_value):
                analysis_start_row = row
                break
        
        if analysis_start_row:
            print(f"进度分析开始行: {analysis_start_row}")
            
            # 显示数据表格
            data_start_row = analysis_start_row + 2
            print(f"\n数据表格内容（从第{data_start_row}行开始）:")
            
            # 显示表头
            headers = []
            for col in range(1, 6):
                header = ws.cell(row=data_start_row, column=col).value
                if header:
                    headers.append(str(header))
            print("表头:", " | ".join(headers))
            
            # 显示数据行
            for row in range(data_start_row + 1, data_start_row + 6):
                row_data = []
                for col in range(1, 6):
                    cell_value = ws.cell(row=row, column=col).value
                    if cell_value is not None:
                        row_data.append(str(cell_value))
                    else:
                        break
                if row_data:
                    print("数据:", " | ".join(row_data))
        
        print("\n=== 图表信息 ===")
        for i, chart in enumerate(ws._charts):
            print(f"图表 {i+1}:")
            print(f"  标题: {chart.title}")
            print(f"  类型: {type(chart).__name__}")
            print(f"  位置: {chart.anchor}")
            print(f"  宽度: {chart.width}")
            print(f"  高度: {chart.height}")
        
        print("\n=== 关键指标分析 ===")
        # 查找关键指标分析
        kpi_start_row = None
        for row in range(analysis_start_row, ws.max_row + 1):
            cell_value = ws.cell(row=row, column=1).value
            if cell_value and "关键指标分析" in str(cell_value):
                kpi_start_row = row
                break
        
        if kpi_start_row:
            print(f"关键指标分析开始行: {kpi_start_row}")
            
            # 显示KPI数据
            for row in range(kpi_start_row + 2, kpi_start_row + 8):
                indicator = ws.cell(row=row, column=1).value
                value = ws.cell(row=row, column=2).value
                if indicator and value:
                    print(f"  {indicator}: {value}")
        
        print("\n=== 检查完成 ===")
        
    except FileNotFoundError:
        print(f"文件 {result_file} 不存在")
    except Exception as e:
        print(f"检查过程中出现错误: {str(e)}")

if __name__ == "__main__":
    check_charts()
