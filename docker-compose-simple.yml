version: '3.8'

services:
  # GPT-Vis-SSR 图表渲染服务
  gpt-vis-ssr:
    build: ./gpt-vis-ssr
    container_name: gpt-vis-ssr
    ports:
      - "3000:3000"
    volumes:
      - ./charts-storage:/app/public/images
    environment:
      - NODE_ENV=production
      - PORT=3000
      - SERVER_IP=**************
      - NGINX_PORT=80
      - USE_HTTPS=false
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 简化的MCP代理服务
  mcp-proxy:
    image: node:18-alpine
    container_name: mcp-proxy
    ports:
      - "1123:1123"
    environment:
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
      - NODE_ENV=production
    volumes:
      - ./simple-mcp-proxy.js:/app/server.js
    working_dir: /app
    command: >
      sh -c "
        apk add --no-cache curl &&
        npm init -y &&
        npm install express &&
        echo 'MCP代理服务器启动中...' &&
        node server.js
      "
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1123/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./charts-storage:/usr/share/nginx/html/images
    depends_on:
      - mcp-proxy
      - gpt-vis-ssr
    restart: unless-stopped

volumes:
  charts_storage:
    driver: local
