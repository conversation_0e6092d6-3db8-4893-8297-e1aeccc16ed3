#!/bin/bash

# 快速修复Alpine Linux网络问题
# 重新配置现有容器的网络和包管理

set -e

echo "=== 快速修复Alpine网络问题 ==="

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-multi-transport" "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo "错误：找不到MCP部署目录"
    exit 1
fi

echo "使用工作目录: $WORK_DIR"
cd "$WORK_DIR"

echo "1. 停止问题容器..."
docker stop gpt-vis-ssr 2>/dev/null || true
docker rm gpt-vis-ssr 2>/dev/null || true

echo "2. 手动修复容器网络配置..."
# 直接运行一个临时容器来测试网络
echo "测试网络连接..."
docker run --rm --name test-network alpine:latest sh -c "
echo 'nameserver 8.8.8.8' > /etc/resolv.conf
echo 'nameserver 8.8.4.4' >> /etc/resolv.conf
ping -c 2 8.8.8.8 || echo 'DNS测试失败'
" || echo "网络测试容器失败"

echo "3. 使用修复后的配置重新启动渲染服务..."
# 创建临时的修复配置
cat > docker-compose-fix.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-bullseye
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DEBIAN_FRONTEND=noninteractive
    command: 
      - bash
      - -c
      - |
        echo "使用Debian镜像，避免Alpine网络问题..."
        apt-get update
        apt-get install -y \
          libcairo2-dev \
          libpango1.0-dev \
          libjpeg-dev \
          libgif-dev \
          librsvg2-dev \
          libpixman-1-dev \
          pkg-config \
          python3-dev \
          build-essential \
          curl
        
        echo "安装npm依赖..."
        npm install
        
        echo "启动渲染服务..."
        npm start
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

networks:
  mcp-network:
    driver: bridge
EOF

echo "4. 启动修复后的渲染服务..."
docker compose -f docker-compose-fix.yml up -d gpt-vis-ssr

echo "5. 等待渲染服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:3000/health > /dev/null 2>&1; then
        echo "✅ 渲染服务启动成功"
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/30)"
        sleep 10
    fi
done

echo "6. 检查渲染服务状态..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 渲染服务正常运行"
    curl -s http://localhost:3000/health
else
    echo "❌ 渲染服务仍有问题"
    echo "查看日志:"
    docker logs --tail=20 gpt-vis-ssr
    exit 1
fi

echo "7. 重新启动MCP服务..."
# 停止MCP服务
docker stop mcp-sse-server mcp-streamable-server 2>/dev/null || true

# 使用原配置启动MCP服务（它们依赖渲染服务）
docker compose up -d mcp-sse-server mcp-streamable-server

echo "8. 等待MCP服务启动..."
sleep 30

echo "9. 测试所有服务..."
echo "测试渲染服务:"
curl -s http://localhost:3000/health && echo " ✅" || echo " ❌"

echo "测试SSE服务:"
curl -s http://localhost:1122/sse > /dev/null && echo "✅ SSE服务正常" || echo "❌ SSE服务异常"

echo "测试Streamable服务:"
curl -s http://localhost:1123/mcp > /dev/null && echo "✅ Streamable服务正常" || echo "❌ Streamable服务异常"

echo ""
echo "10. 测试图表生成..."
RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "测试", "value": 100}
    ]
  }')

if echo "$RESPONSE" | grep -q '"success":true'; then
    echo "✅ 图表生成测试成功"
else
    echo "❌ 图表生成测试失败"
    echo "响应: $RESPONSE"
fi

echo ""
echo "=== 快速修复完成 ==="
echo ""
echo "如果修复成功，建议运行完整的重新部署:"
echo "  sudo ./deploy-prebuilt-images.sh"
echo ""
echo "这将使用更稳定的Ubuntu镜像替代Alpine镜像"
echo ""
echo "当前服务状态:"
docker compose ps

# 清理临时文件
rm -f docker-compose-fix.yml
