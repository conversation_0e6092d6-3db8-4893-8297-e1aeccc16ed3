#!/bin/bash

# 快速设置PNG图片返回支持
# 在现有服务基础上添加PNG返回功能

set -e

echo "=== 设置PNG图片返回支持 ==="

# 检查现有服务
echo "1. 检查现有服务状态..."
if curl -s http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 渲染服务已运行"
else
    echo "❌ 渲染服务未运行，请先运行部署脚本"
    exit 1
fi

# 创建PNG支持的API包装器
echo "2. 创建PNG API包装器..."
mkdir -p /tmp/png-wrapper
cd /tmp/png-wrapper

# 创建简单的PNG包装服务
cat > png-wrapper.js << 'EOF'
const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
app.use(cors());
app.use(express.json());

// PNG图片API
app.post('/api/gpt-vis/png', async (req, res) => {
  try {
    console.log('PNG请求:', req.body);
    
    const response = await fetch('http://localhost:3000/api/gpt-vis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...req.body,
        format: 'png'
      }),
    });
    
    if (!response.ok) {
      throw new Error(`渲染服务错误: ${response.status}`);
    }
    
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('image/png')) {
      // 直接返回PNG
      res.setHeader('Content-Type', 'image/png');
      response.body.pipe(res);
    } else {
      // 处理JSON响应
      const result = await response.json();
      if (result.success && result.resultObj.startsWith('data:image/png;base64,')) {
        const base64Data = result.resultObj.replace('data:image/png;base64,', '');
        const buffer = Buffer.from(base64Data, 'base64');
        res.setHeader('Content-Type', 'image/png');
        res.send(buffer);
      } else {
        throw new Error('无效的图片数据');
      }
    }
  } catch (error) {
    console.error('PNG API错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// URL图片API
app.post('/api/gpt-vis/url', async (req, res) => {
  try {
    console.log('URL请求:', req.body);
    
    const response = await fetch('http://localhost:3000/api/gpt-vis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(req.body),
    });
    
    const result = await response.json();
    
    if (result.success) {
      // 如果返回的是base64，转换为URL
      if (result.resultObj.startsWith('data:image/png;base64,')) {
        // 保存为临时文件并返回URL
        const base64Data = result.resultObj.replace('data:image/png;base64,', '');
        const buffer = Buffer.from(base64Data, 'base64');
        const filename = `chart_${Date.now()}.png`;
        const fs = require('fs');
        const path = require('path');
        
        const imagePath = path.join(__dirname, 'images', filename);
        fs.mkdirSync(path.dirname(imagePath), { recursive: true });
        fs.writeFileSync(imagePath, buffer);
        
        const imageUrl = `http://localhost:3001/images/${filename}`;
        res.json({
          success: true,
          resultObj: imageUrl,
          filename: filename,
          type: 'url'
        });
      } else {
        res.json(result);
      }
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('URL API错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 静态文件服务
app.use('/images', express.static(__dirname + '/images'));

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok',
    service: 'png-wrapper',
    timestamp: new Date().toISOString()
  });
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`PNG包装器服务运行在端口 ${PORT}`);
  console.log(`PNG API: http://localhost:${PORT}/api/gpt-vis/png`);
  console.log(`URL API: http://localhost:${PORT}/api/gpt-vis/url`);
});
EOF

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "png-wrapper",
  "version": "1.0.0",
  "dependencies": {
    "express": "^4.18.0",
    "cors": "^2.8.5",
    "node-fetch": "^2.6.7"
  }
}
EOF

# 安装依赖并启动
echo "3. 安装依赖..."
npm install

echo "4. 启动PNG包装器服务..."
nohup node png-wrapper.js > png-wrapper.log 2>&1 &
PNG_PID=$!
echo "PNG包装器PID: $PNG_PID"

# 等待服务启动
sleep 5

# 测试PNG功能
echo "5. 测试PNG功能..."
PNG_TEST=$(curl -s -w "%{http_code}" -o /tmp/test_png.png -X POST http://localhost:3001/api/gpt-vis/png \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "PNG测试", "value": 70},
      {"category": "其他", "value": 30}
    ],
    "title": "PNG功能测试"
  }')

if [ "$PNG_TEST" = "200" ] && [ -f "/tmp/test_png.png" ]; then
    FILE_SIZE=$(wc -c < /tmp/test_png.png)
    echo "✅ PNG功能测试成功"
    echo "   文件大小: $FILE_SIZE 字节"
    echo "   文件类型: $(file /tmp/test_png.png)"
else
    echo "❌ PNG功能测试失败，状态码: $PNG_TEST"
fi

# 测试URL功能
echo "6. 测试URL功能..."
URL_TEST=$(curl -s -X POST http://localhost:3001/api/gpt-vis/url \
  -H "Content-Type: application/json" \
  -d '{
    "type": "column",
    "data": [
      {"category": "测试1", "value": 100},
      {"category": "测试2", "value": 80}
    ],
    "title": "URL功能测试"
  }')

if echo "$URL_TEST" | grep -q '"type":"url"'; then
    echo "✅ URL功能测试成功"
    IMAGE_URL=$(echo "$URL_TEST" | grep -o '"resultObj":"[^"]*"' | cut -d'"' -f4)
    echo "   图片URL: $IMAGE_URL"
else
    echo "❌ URL功能测试失败"
fi

echo ""
echo "=== PNG支持设置完成 ==="
echo ""
echo "🎯 新的API端点:"
echo "  - PNG直接返回: http://localhost:3001/api/gpt-vis/png"
echo "  - URL链接返回: http://localhost:3001/api/gpt-vis/url"
echo "  - 健康检查: http://localhost:3001/health"
echo ""
echo "📝 使用示例:"
echo "  # 获取PNG图片"
echo "  curl -X POST http://localhost:3001/api/gpt-vis/png \\"
echo "    -H 'Content-Type: application/json' \\"
echo "    -d '{\"type\":\"pie\",\"data\":[{\"category\":\"测试\",\"value\":100}]}' \\"
echo "    > chart.png"
echo ""
echo "  # 获取图片URL"
echo "  curl -X POST http://localhost:3001/api/gpt-vis/url \\"
echo "    -H 'Content-Type: application/json' \\"
echo "    -d '{\"type\":\"pie\",\"data\":[{\"category\":\"测试\",\"value\":100}]}'"
echo ""
echo "🔧 Cherry Studio配置:"
echo "  如果你想在Cherry Studio中使用PNG返回，需要修改MCP服务器配置"
echo "  指向新的PNG API端点"
echo ""
echo "📊 支持的图表类型:"
echo "  pie, column, line, scatter, bar, area, radar, funnel,"
echo "  boxplot, violin, histogram, treemap, sankey, word_cloud"
echo ""
echo "🔄 服务管理:"
echo "  停止PNG包装器: kill $PNG_PID"
echo "  查看日志: tail -f /tmp/png-wrapper/png-wrapper.log"
