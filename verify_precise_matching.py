#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl

def verify_precise_matching():
    """验证精确匹配功能的效果"""
    
    print("=== 精确匹配功能验证 ===")
    
    # 读取最新的分析结果
    result_file = "2025年省抽任务分析结果_20250801_111208.xlsx"
    wb = openpyxl.load_workbook(result_file)
    
    # 检查汇总统计sheet中的匹配方式
    ws_summary = wb['汇总统计']
    print(f"\n=== 汇总统计sheet分析 ===")
    
    # 查找匹配方式列
    match_type_col = None
    for col in range(1, ws_summary.max_column + 1):
        if ws_summary.cell(row=1, column=col).value == '匹配方式':
            match_type_col = col
            break
    
    if match_type_col:
        print(f"找到匹配方式列: 第{match_type_col}列")
        print(f"各sheet的匹配方式:")
        for row in range(2, ws_summary.max_row + 1):
            task_name = ws_summary.cell(row=row, column=1).value
            match_type = ws_summary.cell(row=row, column=match_type_col).value
            if task_name and match_type and task_name != "任务批数" and task_name != "购样金额":
                print(f"  {task_name}: {match_type}")
    
    # 检查具体的sheet匹配效果
    sheets_to_check = {
        '国家集采': '批准文号',
        '一致性': '上市许可持有人+批准文号',
        '国家基药': '生产企业名称+批准文号',
        'B证企业': '受托企业名称+批准文号',
        '广东省集采': '生产企业+批准文号',
        '2年内新批准': '生产企业+批准文号',
        '医院制剂': '生产企业名称+批准文号',
        '药品注册许可风险排查': '生产企业名称+批准文号'
    }
    
    print(f"\n=== 具体sheet匹配效果分析 ===")
    for sheet_name, expected_match_type in sheets_to_check.items():
        if sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            
            # 统计完成情况
            completed_count = 0
            total_count = 0
            
            # 查找完成情况列
            completion_col = None
            for col in range(1, ws.max_column + 1):
                if ws.cell(row=1, column=col).value == '完成情况':
                    completion_col = col
                    break
            
            if completion_col:
                for row in range(2, ws.max_row + 1):
                    status = ws.cell(row=row, column=completion_col).value
                    if status:
                        total_count += 1
                        if status == '已完成':
                            completed_count += 1
                
                completion_rate = completed_count / total_count * 100 if total_count > 0 else 0
                print(f"  {sheet_name}: {completed_count}/{total_count} ({completion_rate:.1f}%) - {expected_match_type}")
    
    # 对比分析
    print(f"\n=== 精确匹配效果对比 ===")
    print(f"新的精确匹配方式:")
    print(f"  1. 国家集采：仅批准文号匹配（按要求简化）")
    print(f"  2. 一致性：上市许可持有人+批准文号双重验证")
    print(f"  3. 国家基药：生产企业名称+批准文号双重验证")
    print(f"  4. B证企业：受托企业名称+批准文号双重验证")
    print(f"  5. 广东省集采：生产企业+批准文号双重验证")
    print(f"  6. 2年内新批准：生产企业+批准文号双重验证")
    print(f"  7. 医院制剂：生产企业名称+批准文号双重验证")
    print(f"  8. 药品注册许可风险排查：生产企业名称+批准文号双重验证")
    
    print(f"\n精确匹配的优势:")
    print(f"  1. 针对性强：每个sheet使用最适合的匹配字段")
    print(f"  2. 精度更高：双重验证确保匹配准确性")
    print(f"  3. 支持多值：单元格包含多个企业名称时能正确匹配")
    print(f"  4. 业务对齐：匹配逻辑符合实际业务需求")
    
    print(f"\n匹配结果变化:")
    print(f"  1. 部分sheet完成率可能下降：更严格的匹配条件")
    print(f"  2. 数据质量提升：减少误匹配和假阳性")
    print(f"  3. 业务准确性：确保统计结果符合实际情况")

if __name__ == "__main__":
    verify_precise_matching()
