# 省抽任务分析脚本使用说明

## 功能描述
本脚本用于比对检验报告和生产环节任务专项两个Excel文件中的批准文号/备案号，并生成完成情况分析报告。

## 文件说明
- `report_comparison.py` - 主要分析脚本
- `requirements.txt` - Python依赖包列表
- `使用说明.md` - 本说明文档

## 输入文件要求
1. **检验报告.xlsx** - 包含批准文号或备案号和抽样凭证编号的检验报告
2. **生产环节任务专项.xlsx** - 包含多个sheet的生产环节任务文件

## 脚本功能
1. **自动识别列名**：脚本会自动查找包含以下关键词的列：
   - 批准文号相关：批准文号、备案号、批准文号或备案号、文号、许可证号
   - 抽样凭证编号相关：抽样凭证编号、凭证编号、抽样编号、样品编号
   - 被抽样单位名称相关：被抽样单位名称、被抽样单位
   - 医疗机构名称相关：医疗机构名称、机构名称、医疗机构
   - 承检机构相关：承检机构、检验机构、检验单位、检测机构、检测单位

2. **数据过滤**：
   - **检验机构过滤**：只统计承检机构为"广州市药品检验所"的数据
   - **自动过滤**：脚本会自动识别承检机构列并进行过滤
   - **过滤统计**：显示过滤前后的数据量和过滤比例

3. **数据比对**：
   - **精确匹配策略**：每个sheet使用特定的匹配逻辑，确保最高的匹配精度
   - **国家集采**：仅批准文号匹配
   - **一致性**：上市许可持有人+批准文号双重匹配
   - **国家基药**：生产企业名称+批准文号双重匹配
   - **B证企业**：受托企业名称+批准文号双重匹配
   - **广东省集采**：生产企业+批准文号双重匹配
   - **2年内新批准**：生产企业+批准文号双重匹配
   - **医院制剂**：生产企业名称+批准文号双重匹配
   - **药品注册许可风险排查**：生产企业名称+批准文号双重匹配
   - **企业名称比对**：读取检验报告中的药品生产企业名称和对应的抽样凭证编号，与生产企业sheet中的企业名称进行匹配
   - **医疗机构名称比对**：读取检验报告中的被抽样单位名称和对应的抽样凭证编号，与医疗机构sheet中的医疗机构名称进行匹配
   - **跟踪抽检复合比对**：读取检验报告中的药品生产企业名称+药品通用名称，与跟踪抽检sheet中的生产单位（委托方）*+药品通用名*进行匹配，如果复合匹配失败则尝试仅企业名称匹配
   - **多值支持**：支持任务表中单元格包含多个企业名称的情况，只要其中一个匹配即可

3. **结果输出**：
   - 在每个sheet的最后添加三列：
     - **抽样凭证编号**：如果匹配成功，显示对应的抽样凭证编号
     - **完成情况**：匹配则显示"已完成"，否则显示"未完成"
     - **抽样月份**：显示抽样的月份信息
   - **生产企业sheet特殊处理**：使用药品生产企业名称与企业名称进行匹配
   - **医疗机构sheet特殊处理**：使用被抽样单位名称与医疗机构名称进行匹配
   - **跟踪抽检sheet特殊处理**：优先使用药品生产企业名称+药品通用名称与生产单位（委托方）*+药品通用名*进行复合匹配，失败则尝试仅企业名称匹配
   - **其他sheet**：使用批准文号+企业名称复合匹配，确保更高的匹配精度

## 使用方法

### 方法一：快速使用（推荐）
1. **准备文件**：将以下两个Excel文件放在与脚本相同的文件夹中
   - `检验报告.xlsx`
   - `生产环节任务专项.xlsx`
2. **确保Python环境已安装**
3. **安装依赖包**：
   ```bash
   pip install -r requirements.txt
   ```
4. **运行脚本**：
   ```bash
   python report_comparison.py
   ```
5. **查看结果**：在同一文件夹中找到生成的结果文件

### 方法二：自定义文件路径
如果您的Excel文件名称不同或位于其他位置，请按以下步骤修改：

#### 修改输入文件路径
1. **打开脚本文件**：用记事本或任何文本编辑器打开`report_comparison.py`
2. **找到文件路径设置**：按`Ctrl+F`搜索"文件路径"，找到第203-204行：
   ```python
   inspection_report_path = os.path.join(script_dir, "检验报告.xlsx")  # 第203行：检验报告文件路径
   production_task_path = os.path.join(script_dir, "生产环节任务专项.xlsx")  # 第204行：生产环节任务专项文件路径
   ```

3. **修改文件名或路径**：

   **情况1：只修改文件名**
   ```python
   # 将"检验报告.xlsx"改为您的文件名
   inspection_report_path = os.path.join(script_dir, "我的检验报告.xlsx")
   # 将"生产环节任务专项.xlsx"改为您的文件名
   production_task_path = os.path.join(script_dir, "我的生产环节任务.xlsx")
   ```

   **情况2：文件在其他文件夹**
   ```python
   # 文件在上级目录的data文件夹中
   inspection_report_path = os.path.join(script_dir, "..", "data", "检验报告.xlsx")
   production_task_path = os.path.join(script_dir, "..", "data", "生产环节任务专项.xlsx")
   ```

   **情况3：使用完整路径**
   ```python
   # 使用完整的文件路径（注意使用双反斜杠\\或正斜杠/）
   inspection_report_path = r"D:\我的文件夹\检验报告.xlsx"
   production_task_path = r"D:\我的文件夹\生产环节任务专项.xlsx"
   ```

4. **保存文件**：按`Ctrl+S`保存修改

#### 修改检验机构过滤设置
如果您想统计其他检验机构的数据：

1. **找到过滤设置**：在脚本中搜索"检验机构过滤设置"，找到第220行：
   ```python
   target_inspection_institution = "广州市药品检验所"  # 只统计此检验机构的数据
   ```

2. **修改检验机构名称**：
   ```python
   # 修改为您想要统计的检验机构
   target_inspection_institution = "中山市食品药品检验所"
   # 或者
   target_inspection_institution = "东莞市食品药品检验所"
   ```

3. **查看可用的检验机构**：如果不确定检验机构的准确名称，运行脚本后会在控制台显示可用的检验机构列表

4. **保存文件**：按`Ctrl+S`保存修改

#### 修改输出文件路径
如果您想将结果文件保存到其他位置：

1. **找到输出路径设置**：在脚本中搜索"输出结果到Python脚本所在目录"，找到第703-704行：
   ```python
   script_dir = os.path.dirname(os.path.abspath(__file__))  # 获取Python脚本所在目录
   output_path = os.path.join(script_dir, output_filename)
   ```

2. **修改输出目录**：
   ```python
   # 保存到指定文件夹
   output_dir = r"D:\分析结果"  # 修改为您想要的文件夹路径
   output_path = os.path.join(output_dir, output_filename)
   ```

3. **保存文件**：按`Ctrl+S`保存修改

#### 路径修改注意事项
- **文件路径中的反斜杠**：Windows路径使用`\\`或在字符串前加`r`（如`r"D:\文件夹"`）
- **文件名包含中文**：确保文件名正确，注意中英文标点符号
- **文件夹不存在**：如果指定的输出文件夹不存在，脚本会报错，请先创建文件夹
- **权限问题**：确保对指定文件夹有读写权限

#### 常见路径示例
```python
# 同级目录下的子文件夹
inspection_report_path = os.path.join(script_dir, "数据文件", "检验报告.xlsx")

# 上级目录
inspection_report_path = os.path.join(script_dir, "..", "检验报告.xlsx")

# 桌面文件夹（Windows）
inspection_report_path = r"C:\Users\<USER>\Desktop\检验报告.xlsx"

# D盘根目录
inspection_report_path = r"D:\检验报告.xlsx"
```

## 输出结果
- **文件名格式**：`2025年省抽任务分析结果_YYYYMMDD_HHMMSS.xlsx`
- **保存位置**：与Python脚本相同的目录（便于查找和管理）
- **汇总统计sheet**：位于最左边（第1个sheet），包含所有sheet的完成情况汇总
- 包含所有处理过的sheet，每个sheet都添加了抽样凭证编号和完成情况列
- 控制台会显示详细的处理过程和统计信息

### 汇总统计sheet内容
汇总sheet采用月度分组列结构，包含以下列：
- **任务名称**：各个任务sheet的名称、任务批数统计和购样金额统计
- **总任务数**：该任务的总数量（保持不变）
- **月度分组列**：每个月份包含三列
  - **YYYY-MM已完成数**：截至该月的累计完成任务数量
  - **YYYY-MM未完成数**：该月未完成的任务数量（购样金额显示"-"）
  - **YYYY-MM完成率(%)**：累计完成数/总任务数×100%（购样金额显示"-"）
- **匹配方式**：使用的匹配方式（抽样凭证编号/药品总价统计/批准文号/企业名称/医疗机构名称）

### 月度分组统计功能
- **统计原理**：根据检验报告中的抽样日期，将所有已完成和未完成的任务按月分组
- **累计统计**：已完成数为截至该月的累计数量，体现进度积累
- **列结构**：每个发现的月份自动生成三列（已完成数、未完成数、完成率）
- **动态月份**：系统自动识别检验报告中的所有月份并创建对应列
- **月份排序**：按时间从最新到最早排序（2025-06 → 2025-05 → 2025-04 → 2025-03 → 2025-02）
- **当前月份**：2025-02、2025-03、2025-04、2025-05、2025-06
- **特殊处理**：购样金额只显示累计金额，未完成数和完成率显示"-"

### 任务批数统计
- **统计方式**：基于检验报告中唯一的抽样凭证编号数量
- **总任务数**：530（固定值）
- **已完成数**：检验报告中唯一抽样凭证编号的数量
- **完成率**：已完成数/总任务数 × 100%

## 最新运行结果统计
根据最新运行结果，各任务按月度分组的完成情况如下：

### 汇总表结构示例（累计统计，月份从新到旧排序）
| 任务名称 | 总任务数 | 2025-06已完成数 | 2025-06未完成数 | 2025-06完成率(%) | 2025-05已完成数 | ... | 匹配方式 |
|----------|----------|----------------|----------------|-----------------|----------------|-----|----------|
| 任务批数 | 530 | 316 | 0 | 59.6% | 263 | ... | 抽样凭证编号 |
| 生产企业 | 118 | 52 | 0 | 44.1% | 47 | ... | 企业名称 |
| 国家集采 | 36 | 21 | 0 | 58.3% | 19 | ... | 批准文号 |
| 跟踪抽检 | 2 | 0 | 0 | 0.0% | 0 | ... | 企业名称+药品通用名称 |
| 购样金额 | - | 1,039,512.35元 | - | - | 738,199.00元 | ... | 药品总价统计 |

**完成率计算说明**：每月完成率 = 累计完成数 ÷ 总任务数 × 100%
**月份排序说明**：列按时间从最新到最早排序，便于快速查看最新进展
**累计逻辑说明**：每列显示截至该月的累计完成数，如2025-05列显示截至2025年5月的累计数据

### 任务批数进度分析功能
汇总统计sheet在表格下方包含简化的专业数据分析模块：

#### 1. 累计完成进度趋势图
- **图表类型**：折线图展示累计完成数的时间趋势
- **数据展示**：74 → 155 → 220 → 263 → 316批的累计增长
- **专业设计**：企业级配色和样式，清晰的趋势线

#### 2. 完成时间预测分析
基于历史数据的智能预测系统，包含以下关键指标：

**预测逻辑说明**：
- **数据基础**：基于5个月的历史完成数据
- **计算方法**：月均完成速度 = 总完成数 ÷ 历史月数
- **预测公式**：预计完成时间 = 当前时间 + (剩余任务数 ÷ 月均速度)
- **目标评估**：与2025年10月20日截止时间进行对比

**关键预测指标**：
- **当前完成进度**：316/530批 (59.6%)
- **月均完成速度**：63.2批/月
- **剩余任务数量**：214批
- **预计完成时间**：2025年9月
- **能否按时完成**：是（能够按时完成）
- **时间评估**：提前约1个月完成

### Excel格式优化
- **汇总统计sheet特殊优化**：
  - 所有单元格都设置为自动换行
  - 确保长文本内容完整显示
  - 表头和数据行统一的换行处理
- **任务批数进度分析**：
  - 在汇总表格下方添加累计完成进度趋势图
  - 包含完成时间预测分析和关键指标
  - 智能预测是否能在10月20日前完成530批任务
- **表头美化**：
  - 蓝色背景（#4472C4）+ 白色粗体字
  - 自动换行和居中对齐
  - 统一边框和30像素行高
- **数据行格式**：
  - 微软雅黑字体，居中对齐
  - 统一边框和适当行高
  - 已完成任务行：浅绿色背景（#E8F5E8）
- **特殊行颜色**：
  - 任务批数行：浅绿色背景（#F0F8E7）
  - 购样金额行：浅蓝色背景（#E7F3FF）
- **列宽优化**：
  - 汇总表：最大22字符宽度
  - 详细表：最大28字符宽度
  - 根据内容自动调整

### 月度统计亮点
- **2025年2月**：各类任务都有较多完成项目，是活跃月份
  - 任务批数：74个，生产企业：15个，国家集采：13个
- **2025年4月**：广东省集采完成最多（38个），购样金额最高（618,945.54元）
- **2025年5月**：医院制剂集中完成（8个）
- **2025年6月**：B证企业完成较多（21个）

### 总体完成情况
- **任务批数**：316/530 (59.6%)
- **购样金额**：1,039,512.35元
- **各sheet总计**：376/1596 (23.6%)

### 重要更新
- ✅ **实现精确匹配策略**：为每个sheet定制专门的匹配逻辑，支持多值匹配，大幅提高匹配精度和业务准确性
- ✅ **新增检验机构过滤**：只统计承检机构为"广州市药品检验所"的数据，提高分析精确性
- ✅ **文件路径优化**：输入文件路径改为与脚本同级目录，并提供详细的路径修改指导
- ✅ **简化图表分析并增加预测功能**：只保留累计完成进度趋势图，新增基于历史数据的完成时间预测分析
- ✅ **修正累计统计逻辑**：修正了月份排序调整后的累计统计计算，确保数据与表头正确对应
- ✅ **月份排序优化**：汇总表中的月份列按时间从最新到最早排序（2025-06 → 2025-02），便于查看最新进展
- ✅ **新增跟踪抽检统计**：添加跟踪抽检任务的复合匹配逻辑（企业名称+药品通用名称），总任务数2个
- ✅ **输出路径优化**：结果文件保存在Python脚本所在目录，便于查找和管理
- ✅ **汇总统计全部自动换行**：汇总统计sheet的所有单元格都设置为自动换行，确保内容完整显示
- ✅ **Excel排版美化**：专业的表格样式，包括颜色、字体、边框、行高等全面优化
- ✅ **Excel格式优化**：表头设置为自动换行和居中对齐，列宽自动调整
- ✅ **购样金额进一步简化**：总任务数改为"-"，专注于金额统计
- ✅ **改为累计统计**：每月已完成数为截至该月的累计数量，体现进度积累
- ✅ **简化购样金额统计**：购样金额不统计未完成数和完成率，只显示累计金额
- ✅ **修正月度完成率计算**：完成率 = 累计完成数 ÷ 总任务数 × 100%
- ✅ **重新设计月度分组统计**：采用列结构，每个月份包含已完成数、未完成数、完成率三列
- ✅ **动态月份列生成**：系统自动识别检验报告中的月份并生成对应列（2025-02至2025-06）
- ✅ **保持总任务数不变**：总任务数列保持原有数值，月度统计通过独立列展示
- ✅ **新增任务批数统计**：基于检验报告中唯一抽样凭证编号统计，完成率59.6% (316/530)
- ✅ **新增汇总统计sheet**：位于Excel文件最左边，提供所有项目的完成情况汇总
- ✅ **修正生产企业匹配方式**：生产企业sheet现在使用药品生产企业名称与企业名称进行匹配
- ✅ **生产企业完成率大幅提升**：从0.0%提升到44.1% (52/118)
- ✅ **新增医疗机构名称匹配功能**：医疗机构sheet现在使用被抽样单位名称与医疗机构名称进行匹配
- ✅ **医疗机构完成率提升**：从0.0%提升到19.0% (4/21)
- ✅ **建立了102个药品生产企业映射**：用于生产企业名称匹配
- ✅ **建立了73个被抽样单位映射**：用于医疗机构名称匹配
- ✅ **整体完成率提升**：从20.1%提升到23.6%
- ✅ **四种匹配方式**：抽样凭证编号、企业名称、医疗机构名称、批准文号
- ✅ **优化文件结构**：汇总信息一目了然，便于快速查看整体进度

## 注意事项
1. 确保输入的Excel文件路径正确
2. 检验报告必须包含批准文号和抽样凭证编号相关列
3. 生产环节任务专项文件的每个sheet必须包含批准文号相关列
4. 脚本会自动跳过没有找到批准文号列的sheet
5. 输出文件会保存在与输入文件相同的目录下

## 错误处理
- 如果找不到必要的列，脚本会显示错误信息并退出
- 如果某个sheet没有批准文号相关列，会显示警告并跳过该sheet
- 所有处理过程都会在控制台显示详细信息

## 快速参考

### 文件路径修改位置
- **输入文件路径**：第218-219行
- **检验机构过滤**：第220行
- **输出文件路径**：第703-704行

### 默认文件名
- **检验报告**：`检验报告.xlsx`
- **生产环节任务专项**：`生产环节任务专项.xlsx`
- **输出结果**：`2025年省抽任务分析结果_YYYYMMDD_HHMMSS.xlsx`

### 数据过滤设置
- **检验机构过滤**：只统计"广州市药品检验所"的数据（可在第220行修改）
- **过滤列名**：承检机构
- **过滤逻辑**：自动识别承检机构列并进行精确匹配
- **可用检验机构**：广州市药品检验所、中山市食品药品检验所、东莞市食品药品检验所等

### 精确匹配设置
- **匹配策略**：每个sheet使用专门定制的匹配逻辑
- **双重验证**：大部分sheet同时验证批准文号和对应的企业名称字段
- **多值支持**：支持任务表中单元格包含多个企业名称（用分隔符分开）
- **智能分割**：自动识别多种分隔符（、，,;；/\|等）
- **精确对应**：每个sheet的企业名称字段与检验报告的药品生产企业名称精确匹配
- **业务准确**：匹配逻辑完全符合实际业务需求和数据结构

### 常见问题解决
1. **文件找不到**：检查文件名和路径是否正确
2. **权限错误**：确保对文件夹有读写权限
3. **中文乱码**：确保文件名中的中文字符正确
4. **路径格式错误**：Windows路径使用`\\`或在字符串前加`r`

## 技术支持
如有问题，请检查：
1. 文件路径是否正确
2. Excel文件是否能正常打开
3. 列名是否包含预期的关键词
4. Python环境和依赖包是否正确安装
