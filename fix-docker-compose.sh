#!/bin/bash

# 修复Docker Compose配置文件
# 解决YAML格式问题

set -e

echo "=== 修复Docker Compose配置 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

APP_DIR="/opt/mcp-server-chart"

# 检查目录是否存在
if [ ! -d "$APP_DIR" ]; then
    echo "应用目录不存在，创建目录..."
    mkdir -p $APP_DIR
fi

cd $APP_DIR

echo "创建修复后的Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./gpt-vis-ssr:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    command: |
      sh -c "
      apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++ &&
      npm install &&
      npm start
      "
    restart: unless-stopped
    networks:
      - mcp-network

  mcp-chart-server:
    image: node:18-alpine
    container_name: mcp-chart-server
    working_dir: /app
    ports:
      - "1122:1122"
    volumes:
      - ./mcp-server-chart:/app
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    command: |
      sh -c "
      npm install &&
      npm run build &&
      node build/index.js --transport sse --port 1122
      "
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
EOF

echo "验证Docker Compose配置..."
if docker compose config > /dev/null 2>&1; then
    echo "✓ Docker Compose配置验证成功"
else
    echo "✗ Docker Compose配置验证失败"
    echo "尝试使用更简单的配置..."
    
    # 创建更简单的配置
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./gpt-vis-ssr:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++
        npm install
        npm start
    restart: unless-stopped
    networks:
      - mcp-network

  mcp-chart-server:
    image: node:18-alpine
    container_name: mcp-chart-server
    working_dir: /app
    ports:
      - "1122:1122"
    volumes:
      - ./mcp-server-chart:/app
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        npm install
        npm run build
        node build/index.js --transport sse --port 1122
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
EOF

    # 再次验证
    if docker compose config > /dev/null 2>&1; then
        echo "✓ 简化配置验证成功"
    else
        echo "✗ 配置仍有问题，请检查Docker Compose版本"
        exit 1
    fi
fi

# 确保必要的目录和文件存在
echo "检查必要的文件和目录..."

# 创建渲染服务目录
if [ ! -d "gpt-vis-ssr" ]; then
    echo "创建渲染服务目录..."
    mkdir -p gpt-vis-ssr
fi

# 创建package.json（如果不存在）
if [ ! -f "gpt-vis-ssr/package.json" ]; then
    echo "创建package.json..."
    cat > gpt-vis-ssr/package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0"
  }
}
EOF
fi

# 创建server.js（如果不存在）
if [ ! -f "gpt-vis-ssr/server.js" ]; then
    echo "创建server.js..."
    cat > gpt-vis-ssr/server.js << 'EOF'
const express = require('express');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();
app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
});
EOF
fi

# 检查MCP项目
if [ ! -d "mcp-server-chart" ]; then
    echo "克隆MCP项目..."
    git clone https://github.com/antvis/mcp-server-chart.git
fi

# 创建图片目录
mkdir -p images

echo "启动Docker服务..."
docker compose up -d

echo "=== 修复完成 ==="
echo "服务正在启动，请等待1-2分钟后检查状态"
echo ""
echo "检查命令："
echo "  docker compose ps"
echo "  docker compose logs -f"
