# 使用 Node.js 官方镜像作为基础镜像
FROM node:18-alpine

# 安装构建依赖和字体支持
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    # 添加字体和语言支持
    fontconfig \
    ttf-dejavu \
    ttf-liberation \
    font-noto \
    font-noto-cjk \
    font-noto-emoji \
    # 添加中文支持
    icu-libs \
    icu-data-full

# 设置语言环境
ENV LANG=zh_CN.UTF-8 \
    LANGUAGE=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制项目文件
COPY . .

# 创建图片存储目录
RUN mkdir -p public/images

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
