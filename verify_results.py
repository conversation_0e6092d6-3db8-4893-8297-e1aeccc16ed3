import pandas as pd
import os
from datetime import datetime

def verify_analysis_results():
    """验证分析结果是否包含数据"""
    print("=== 验证分析结果 ===")
    
    # 读取原始数据
    df = pd.read_excel('YPdatas.xls', header=1)
    print(f"原始数据: {df.shape[0]} 行, {df.shape[1]} 列")
    
    # 检查关键数据
    print(f"抽样编号数量: {df['抽样编号'].nunique()}")
    print(f"样品名称数量: {df['样品名称'].nunique()}")
    print(f"被抽样单位数量: {df['被抽样单位'].nunique()}")
    print(f"批准文号数量: {df['批准文号'].nunique()}")
    
    # 按月份统计
    df['抽样月份'] = pd.to_datetime(df['抽样时间'], errors='coerce').dt.to_period('M').astype(str)
    month_stats = df.groupby('抽样月份').agg({
        '抽样编号': 'nunique',
        '总价（元）': 'sum',
        '样品名称': 'nunique',
        '批准文号': 'nunique',
        '被抽样单位': 'nunique'
    }).reset_index()
    
    print("\n按月份统计:")
    print(month_stats)
    
    # 检查累积统计
    TOTAL_TASKS = 400
    print(f"\n累积统计 (总任务数: {TOTAL_TASKS}):")
    cumulative_batches = 0
    for _, row in month_stats.iterrows():
        cumulative_batches += row['抽样编号']
        progress = cumulative_batches / TOTAL_TASKS * 100
        print(f"{row['抽样月份']}: {cumulative_batches} 批次 ({progress:.1f}%)")
    
    # 被抽样单位统计
    unit_stats = df['被抽样单位'].value_counts()
    print(f"\n被抽样单位统计 (前10名):")
    print(unit_stats.head(10))
    
    return df

if __name__ == "__main__":
    try:
        df = verify_analysis_results()
        print("\n✅ 数据验证完成，脚本应该能正常生成有数据的分析结果！")
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        import traceback
        traceback.print_exc()
