#!/bin/bash

# MCP Server Chart 简化Docker部署脚本
# 跳过系统更新，直接部署

set -e

echo "=== MCP Server Chart 简化Docker部署脚本 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 检查Docker是否已安装
if command -v docker &> /dev/null; then
    echo "Docker已安装: $(docker --version)"
else
    echo "Docker未安装，正在安装..."
    
    # 安装Docker（使用官方脚本）
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    
    # 启动Docker服务
    systemctl start docker
    systemctl enable docker
    
    echo "Docker安装完成: $(docker --version)"
fi

# 检查Docker Compose是否可用
if docker compose version &> /dev/null; then
    echo "Docker Compose已可用: $(docker compose version)"
elif command -v docker-compose &> /dev/null; then
    echo "使用docker-compose: $(docker-compose --version)"
    # 创建别名
    alias docker-compose='docker compose'
else
    echo "安装Docker Compose..."
    # 下载Docker Compose
    DOCKER_COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    curl -L "https://github.com/docker/compose/releases/download/${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    echo "Docker Compose安装完成"
fi

# 创建应用目录
APP_DIR="/opt/mcp-server-chart"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# 创建Docker Compose文件
echo "创建Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./gpt-vis-ssr:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    command: >
      sh -c "
      apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++ &&
      npm install &&
      npm start
      "
    restart: unless-stopped
    networks:
      - mcp-network

  mcp-chart-server:
    image: node:18-alpine
    container_name: mcp-chart-server
    working_dir: /app
    ports:
      - "1122:1122"
    volumes:
      - ./mcp-server-chart:/app
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    command: >
      sh -c "
      npm install &&
      npm run build &&
      node build/index.js --transport sse --port 1122 --endpoint /sse
      "
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
EOF

# 创建渲染服务目录和文件
echo "创建渲染服务..."
mkdir -p gpt-vis-ssr
cd gpt-vis-ssr

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0"
  }
}
EOF

# 创建服务器代码
cat > server.js << 'EOF'
const express = require('express');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();
app.use(express.json({ limit: '10mb' }));

// 创建图片存储目录
const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    // 生成唯一文件名
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    // 保存图片到文件
    fs.writeFileSync(filepath, buffer);
    
    // 返回base64编码的图片
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.json({
      success: false,
      errorMessage: error.message
    });
  }
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 静态文件服务
app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
});
EOF

# 返回主目录
cd $APP_DIR

# 克隆MCP项目
echo "克隆MCP Server Chart项目..."
if [ ! -d "mcp-server-chart" ]; then
    git clone https://github.com/antvis/mcp-server-chart.git
else
    echo "MCP项目目录已存在，跳过克隆"
fi

# 创建图片存储目录
mkdir -p images

# 启动服务
echo "启动Docker服务..."
docker compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 60

# 检查服务状态
echo "检查服务状态..."
docker compose ps

# 测试服务
echo "测试服务..."
echo "测试渲染服务..."
for i in {1..5}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✓ 渲染服务启动成功"
        break
    else
        echo "等待渲染服务启动... ($i/5)"
        sleep 10
    fi
done

echo "测试MCP服务..."
for i in {1..5}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✓ MCP服务启动成功"
        break
    else
        echo "等待MCP服务启动... ($i/5)"
        sleep 10
    fi
done

echo ""
echo "=== Docker部署完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - 渲染服务健康检查: http://localhost:3000/health"
echo "  - MCP服务: http://localhost:1122/sse"
echo ""
echo "管理命令:"
echo "  查看日志: docker compose logs -f"
echo "  查看特定服务日志: docker compose logs -f gpt-vis-ssr"
echo "  重启服务: docker compose restart"
echo "  停止服务: docker compose down"
echo "  查看状态: docker compose ps"
echo ""
echo "如果服务启动失败，请运行以下命令查看日志："
echo "  docker compose logs gpt-vis-ssr"
echo "  docker compose logs mcp-chart-server"
