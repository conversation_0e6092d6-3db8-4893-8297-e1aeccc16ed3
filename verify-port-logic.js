// 验证端口配置逻辑的JavaScript脚本

function generateImageUrl(serverIP, gptVisPort, useHttps, filename) {
    const protocol = useHttps ? 'https' : 'http';

    // 图片URL直接指向GPT-Vis-SSR服务端口（通常是3000）
    const imageUrl = `${protocol}://${serverIP}:${gptVisPort}/images/${filename}`;

    return imageUrl;
}

function generateBaseUrl(serverIP, serverPort, useHttps) {
    const protocol = useHttps ? 'https' : 'http';
    
    // 根据协议和端口决定是否显示端口号
    let portSuffix = '';
    if ((protocol === 'http' && serverPort !== '80') || (protocol === 'https' && serverPort !== '443')) {
        portSuffix = `:${serverPort}`;
    }
    
    return `${protocol}://${serverIP}${portSuffix}`;
}

// 测试用例
const testCases = [
    // [serverIP, nginxPort, useHttps, expectedBaseUrl, expectedImageUrl (GPT-Vis-SSR port 3000)]
    ['**************', '80', false, 'http://**************', 'http://**************:3000/images/test.png'],
    ['**************', '443', true, 'https://**************', 'https://**************:3000/images/test.png'],
    ['**************', '8080', false, 'http://**************:8080', 'http://**************:3000/images/test.png'],
    ['**************', '8443', true, 'https://**************:8443', 'https://**************:3000/images/test.png'],
    ['**************', '443', false, 'http://**************:443', 'http://**************:3000/images/test.png'],
    ['**************', '80', true, 'https://**************:80', 'https://**************:3000/images/test.png'],
];

console.log('======================================');
console.log('  端口配置逻辑验证');
console.log('======================================');
console.log();

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
    const [serverIP, nginxPort, useHttps, expectedBaseUrl, expectedImageUrl] = testCase;

    console.log(`测试用例 ${index + 1}:`);
    console.log(`  配置: IP=${serverIP}, NginxPort=${nginxPort}, HTTPS=${useHttps}`);

    // 测试基础URL生成 (Nginx代理)
    const actualBaseUrl = generateBaseUrl(serverIP, nginxPort, useHttps);
    const baseUrlCorrect = actualBaseUrl === expectedBaseUrl;

    console.log(`  基础URL: ${actualBaseUrl} ${baseUrlCorrect ? '✓' : '✗'}`);
    if (!baseUrlCorrect) {
        console.log(`    期望: ${expectedBaseUrl}`);
        failed++;
    } else {
        passed++;
    }

    // 测试图片URL生成 (GPT-Vis-SSR直连)
    const gptVisPort = '3000';  // GPT-Vis-SSR固定端口
    const actualImageUrl = generateImageUrl(serverIP, gptVisPort, useHttps, 'test.png');
    const imageUrlCorrect = actualImageUrl === expectedImageUrl;

    console.log(`  图片URL: ${actualImageUrl} ${imageUrlCorrect ? '✓' : '✗'}`);
    if (!imageUrlCorrect) {
        console.log(`    期望: ${expectedImageUrl}`);
        failed++;
    } else {
        passed++;
    }

    console.log();
});

console.log('======================================');
console.log(`测试结果: ${passed} 通过, ${failed} 失败`);
if (failed === 0) {
    console.log('🎉 所有测试通过！端口配置逻辑正确。');
} else {
    console.log('⚠ 有测试失败，需要检查端口配置逻辑。');
}
console.log('======================================');

// 模拟实际使用场景
console.log();
console.log('实际使用示例:');
console.log('1. 标准HTTP部署 (Nginx端口80, GPT-Vis-SSR端口3000):');
console.log(`   图片URL: ${generateImageUrl('**************', '3000', false, 'chart-123.png')}`);
console.log(`   MCP端点: ${generateBaseUrl('**************', '80', false)}/sse`);
console.log();

console.log('2. 自定义端口部署 (Nginx端口8080, GPT-Vis-SSR端口3000):');
console.log(`   图片URL: ${generateImageUrl('**************', '3000', false, 'chart-123.png')}`);
console.log(`   MCP端点: ${generateBaseUrl('**************', '8080', false)}/sse`);
console.log();

console.log('3. HTTPS部署 (Nginx端口443, GPT-Vis-SSR端口3000):');
console.log(`   图片URL: ${generateImageUrl('**************', '3000', true, 'chart-123.png')}`);
console.log(`   MCP端点: ${generateBaseUrl('**************', '443', true)}/sse`);
console.log();

console.log('4. Cherry Studio配置URL:');
console.log(`   SSE端点: ${generateBaseUrl('**************', '80', false)}/sse`);
console.log(`   Streamable端点: ${generateBaseUrl('**************', '80', false)}/mcp`);
console.log();

console.log('5. 重要说明:');
console.log('   - MCP端点通过Nginx代理访问');
console.log('   - 图片URL直接访问GPT-Vis-SSR服务端口3000');
console.log('   - 这样设计是为了避免大文件通过代理传输');
