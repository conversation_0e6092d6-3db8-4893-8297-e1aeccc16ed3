events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # 上游服务器配置
    upstream mcp_server_sse {
        server mcp-server-chart-sse:1122;
    }

    upstream mcp_server_streamable {
        server mcp-server-chart-streamable:1123;
    }

    upstream gpt_vis_ssr {
        server gpt-vis-ssr:3000;
    }

    server {
        listen 80;
        server_name localhost;

        # MCP Server Chart 服务 - SSE
        location /sse {
            proxy_pass http://mcp_server_sse/sse;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # SSE特定配置
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 24h;
        }

        # MCP Server Chart 服务 - Streamable
        location /mcp {
            proxy_pass http://mcp_server_streamable;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # 添加CORS头
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";

            if ($request_method = 'OPTIONS') {
                return 204;
            }

            # Streamable特定配置
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 24h;
        }

        # GPT-Vis-SSR 服务
        location /api/gpt-vis {
            proxy_pass http://gpt_vis_ssr/api/gpt-vis;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 增加超时时间，因为图表渲染可能需要较长时间
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 图片静态文件服务
        location /images/ {
            alias /usr/share/nginx/html/images/;
            expires 7d;
            add_header Cache-Control "public, immutable";
        }

        # 健康检查
        location /health {
            proxy_pass http://gpt_vis_ssr/health;
            proxy_set_header Host $host;
        }



        # 默认页面
        location / {
            return 200 'MCP Chart Server with GPT-Vis-SSR is running!';
            add_header Content-Type text/plain;
        }
    }
}
