#!/bin/bash

# Docker服务诊断脚本
# 检查MCP服务状态和问题

echo "=== Docker服务诊断脚本 ==="

# 检查Docker是否运行
if ! systemctl is-active --quiet docker; then
    echo "❌ Docker服务未运行，正在启动..."
    sudo systemctl start docker
    sleep 5
else
    echo "✅ Docker服务正在运行"
fi

# 进入应用目录
APP_DIR="/opt/mcp-server-chart"
if [ ! -d "$APP_DIR" ]; then
    echo "❌ 应用目录不存在: $APP_DIR"
    exit 1
fi

cd $APP_DIR

echo ""
echo "=== 检查Docker Compose配置 ==="
if [ -f "docker-compose.yml" ]; then
    echo "✅ docker-compose.yml 文件存在"
    
    # 验证配置
    if docker compose config > /dev/null 2>&1; then
        echo "✅ Docker Compose配置有效"
    else
        echo "❌ Docker Compose配置无效"
        echo "配置验证错误："
        docker compose config
        exit 1
    fi
else
    echo "❌ docker-compose.yml 文件不存在"
    exit 1
fi

echo ""
echo "=== 检查容器状态 ==="
docker compose ps

echo ""
echo "=== 检查所有容器 ==="
docker ps -a

echo ""
echo "=== 检查网络 ==="
docker network ls | grep mcp

echo ""
echo "=== 检查端口占用 ==="
echo "端口3000:"
netstat -tuln | grep :3000 || echo "端口3000未被占用"
echo "端口1122:"
netstat -tuln | grep :1122 || echo "端口1122未被占用"

echo ""
echo "=== 查看渲染服务日志 ==="
echo "--- gpt-vis-ssr 最近日志 ---"
docker compose logs --tail=50 gpt-vis-ssr

echo ""
echo "=== 查看MCP服务日志 ==="
echo "--- mcp-chart-server 最近日志 ---"
docker compose logs --tail=50 mcp-chart-server

echo ""
echo "=== 检查文件结构 ==="
echo "应用目录结构:"
ls -la $APP_DIR

echo ""
echo "渲染服务目录:"
if [ -d "$APP_DIR/gpt-vis-ssr" ]; then
    ls -la $APP_DIR/gpt-vis-ssr/
else
    echo "❌ gpt-vis-ssr目录不存在"
fi

echo ""
echo "MCP项目目录:"
if [ -d "$APP_DIR/mcp-server-chart" ]; then
    ls -la $APP_DIR/mcp-server-chart/ | head -10
else
    echo "❌ mcp-server-chart目录不存在"
fi

echo ""
echo "=== 尝试重启服务 ==="
echo "停止所有服务..."
docker compose down

echo "清理旧容器..."
docker container prune -f

echo "重新启动服务..."
docker compose up -d

echo "等待服务启动..."
sleep 30

echo ""
echo "=== 重启后状态检查 ==="
docker compose ps

echo ""
echo "=== 测试连接 ==="
echo "测试渲染服务健康检查..."
for i in {1..5}; do
    if curl -s --connect-timeout 5 http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务响应正常"
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/5)"
        sleep 10
    fi
done

echo "测试MCP服务..."
for i in {1..5}; do
    if curl -s --connect-timeout 5 http://localhost:1122/sse > /dev/null; then
        echo "✅ MCP服务响应正常"
        break
    else
        echo "⏳ 等待MCP服务启动... ($i/5)"
        sleep 10
    fi
done

echo ""
echo "=== 诊断完成 ==="
echo "如果服务仍然无法启动，请检查上面的日志输出"
echo ""
echo "有用的调试命令："
echo "  docker compose logs -f gpt-vis-ssr    # 查看渲染服务实时日志"
echo "  docker compose logs -f mcp-chart-server # 查看MCP服务实时日志"
echo "  docker compose restart gpt-vis-ssr    # 重启渲染服务"
echo "  docker compose restart mcp-chart-server # 重启MCP服务"
echo "  docker compose down && docker compose up -d # 完全重启"
