#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
省抽任务分析脚本
用于比对检验报告和生产环节任务专项两个Excel文件中的批准文号/备案号
"""

import pandas as pd
import os
from datetime import datetime
from openpyxl.chart import Line<PERSON>hart, BarChart, Reference
from openpyxl.chart.axis import DateAxis
from openpyxl.chart.series import DataPoint
from openpyxl.drawing.fill import ColorChoice, PatternFillProperties
from openpyxl.drawing.colors import SchemeColor
import warnings
warnings.filterwarnings('ignore')

def parse_date_to_month(date_value):
    """
    将日期值转换为YYYY-MM格式的月份字符串
    """
    if pd.isna(date_value):
        return None

    try:
        if isinstance(date_value, (int, float)):
            # 处理整数格式的日期，如20250521
            date_str = str(int(date_value))
            if len(date_str) == 8:  # YYYYMMDD格式
                year = date_str[:4]
                month = date_str[4:6]
                return f"{year}-{month}"
        elif isinstance(date_value, str):
            # 尝试解析字符串日期
            date_obj = pd.to_datetime(date_value)
            return date_obj.strftime('%Y-%m')
        else:
            # 处理datetime对象
            return date_value.strftime('%Y-%m')
    except:
        pass

    return None

def find_approval_number_column(df):
    """
    查找包含批准文号或备案号的列
    """
    possible_names = ['批准文号', '备案号', '批准文号或备案号', '文号', '许可证号']
    
    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col
    
    # 如果没有找到，返回包含相关关键词的列
    for col in df.columns:
        col_str = str(col).strip().lower()
        if any(keyword in col_str for keyword in ['批准', '备案', '文号', '许可']):
            return col
    
    return None

def find_sampling_certificate_column(df):
    """
    查找抽样凭证编号列
    """
    possible_names = ['抽样凭证编号', '凭证编号', '抽样编号', '样品编号']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col

    # 如果没有找到，返回包含相关关键词的列
    for col in df.columns:
        col_str = str(col).strip().lower()
        if any(keyword in col_str for keyword in ['抽样', '凭证', '编号']):
            return col

    return None

def find_sampled_unit_column(df):
    """
    查找被抽样单位名称列
    """
    # 精确匹配被抽样单位名称
    for col in df.columns:
        col_str = str(col).strip()
        if col_str == '被抽样单位名称':
            return col

    # 其他可能的名称
    possible_names = ['被抽样单位', '抽样单位名称', '单位名称']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str and '统一社会信用代码' not in col_str:
                return col

    return None

def find_medical_institution_column(df):
    """
    查找医疗机构名称列
    """
    possible_names = ['医疗机构名称', '机构名称', '医疗机构', '医院名称']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col

    # 如果没有找到，返回包含相关关键词的列
    for col in df.columns:
        col_str = str(col).strip().lower()
        if any(keyword in col_str for keyword in ['医疗机构', '机构名称', '医院']):
            return col

    return None

def find_production_enterprise_column(df):
    """
    查找药品生产企业名称列
    """
    # 精确匹配药品生产企业名称
    for col in df.columns:
        col_str = str(col).strip()
        if col_str == '药品生产企业名称':
            return col

    # 其他可能的名称
    possible_names = ['生产企业名称', '生产企业', '企业名称']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str and '受托' not in col_str and '进口' not in col_str:
                return col

    return None

def find_enterprise_name_column(df):
    """
    查找企业名称列（用于生产企业sheet）
    """
    possible_names = ['企业名称', '生产企业名称', '公司名称', '厂家名称']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col

    return None

def find_tracking_inspection_columns(df):
    """
    查找跟踪抽检相关列：生产单位（委托方）*和药品通用名*
    """
    production_unit_col = None
    drug_name_col = None

    # 查找生产单位（委托方）*列
    for col in df.columns:
        col_str = str(col).strip()
        if '生产单位（委托方）' in col_str or '生产单位(委托方)' in col_str:
            production_unit_col = col
            break

    # 查找药品通用名*列
    for col in df.columns:
        col_str = str(col).strip()
        if '药品通用名' in col_str:
            drug_name_col = col
            break

    return production_unit_col, drug_name_col

def find_drug_generic_name_column(df):
    """
    查找检验报告中的药品通用名称列
    """
    possible_names = ['药品通用名称', '药品通用名', '通用名称', '通用名']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col

    return None

def find_inspection_institution_column(df):
    """
    查找检验报告中的承检机构列
    """
    possible_names = ['承检机构', '检验机构', '检验单位', '检测机构', '检测单位']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col

    return None

def main():
    # 文件路径（与脚本同级目录）
    # 如需修改文件路径，请修改下面两行的文件名或路径
    script_dir = os.path.dirname(os.path.abspath(__file__))  # 获取脚本所在目录
    inspection_report_path = os.path.join(script_dir, "检验报告.xlsx")  # 第201行：检验报告文件路径
    production_task_path = os.path.join(script_dir, "生产环节任务专项.xlsx")  # 第202行：生产环节任务专项文件路径

    # 检验机构过滤设置（第206行：如需修改过滤的检验机构，请修改下面的值）
    target_inspection_institution = "广州市药品检验所"  # 只统计此检验机构的数据
    
    # 检查文件是否存在
    if not os.path.exists(inspection_report_path):
        print(f"错误：找不到文件 {inspection_report_path}")
        return
    
    if not os.path.exists(production_task_path):
        print(f"错误：找不到文件 {production_task_path}")
        return
    
    try:
        # 读取检验报告
        print("正在读取检验报告...")
        inspection_df = pd.read_excel(inspection_report_path)
        print(f"检验报告包含 {len(inspection_df)} 行数据")
        print(f"检验报告列名: {list(inspection_df.columns)}")
        
        # 查找批准文号列、抽样凭证编号列、被抽样单位名称列、药品生产企业名称列、药品通用名称列和承检机构列
        approval_col = find_approval_number_column(inspection_df)
        sampling_col = find_sampling_certificate_column(inspection_df)
        sampled_unit_col = find_sampled_unit_column(inspection_df)
        production_enterprise_col = find_production_enterprise_column(inspection_df)
        drug_generic_name_col = find_drug_generic_name_column(inspection_df)
        inspection_institution_col = find_inspection_institution_column(inspection_df)

        if approval_col is None:
            print("错误：在检验报告中找不到批准文号或备案号列")
            print("请检查列名是否包含：批准文号、备案号、批准文号或备案号等关键词")
            return

        if sampling_col is None:
            print("错误：在检验报告中找不到抽样凭证编号列")
            print("请检查列名是否包含：抽样凭证编号、凭证编号、抽样编号等关键词")
            return

        if sampled_unit_col is None:
            print("错误：在检验报告中找不到被抽样单位名称列")
            print("请检查列名是否包含：被抽样单位名称、被抽样单位等关键词")
            return

        if production_enterprise_col is None:
            print("错误：在检验报告中找不到药品生产企业名称列")
            print("请检查列名是否包含：药品生产企业名称、生产企业名称等关键词")
            return

        print(f"找到批准文号列: {approval_col}")
        print(f"找到抽样凭证编号列: {sampling_col}")
        print(f"找到被抽样单位名称列: {sampled_unit_col}")
        print(f"找到药品生产企业名称列: {production_enterprise_col}")
        print(f"找到药品通用名称列: {drug_generic_name_col}")
        print(f"找到承检机构列: {inspection_institution_col}")

        # 过滤只保留指定检验机构的数据
        if inspection_institution_col:
            original_count = len(inspection_df)
            inspection_df = inspection_df[inspection_df[inspection_institution_col] == target_inspection_institution].copy()
            filtered_count = len(inspection_df)
            print(f"\n过滤后数据：只统计{target_inspection_institution}的数据")
            print(f"原始数据：{original_count} 行")
            print(f"过滤后数据：{filtered_count} 行")
            print(f"过滤比例：{filtered_count/original_count*100:.1f}%")

            if filtered_count == 0:
                print(f"警告：过滤后没有数据，请检查检验机构名称是否正确")
                print(f"可用的检验机构：")
                institution_counts = pd.read_excel(inspection_report_path)[inspection_institution_col].value_counts()
                for institution, count in institution_counts.head(10).items():
                    print(f"  {institution}: {count} 条")
                return
        else:
            print("警告：未找到承检机构列，将统计所有数据")

        # 创建批准文号到抽样信息的映射（包含日期）
        approval_to_info = {}
        # 创建批准文号+企业名称的复合映射（用于更精确的匹配）
        approval_enterprise_to_info = {}

        for _, row in inspection_df.iterrows():
            approval_num = str(row[approval_col]).strip() if pd.notna(row[approval_col]) else ""
            enterprise_name = str(row[production_enterprise_col]).strip() if pd.notna(row[production_enterprise_col]) else ""
            sampling_num = str(row[sampling_col]).strip() if pd.notna(row[sampling_col]) else ""
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            if approval_num and approval_num != "nan":
                # 原有的批准文号映射（保留作为备选）
                approval_to_info[approval_num] = {
                    'sampling_num': sampling_num,
                    'month': month
                }

                # 新增的批准文号+企业名称复合映射
                if enterprise_name and enterprise_name != "nan":
                    composite_key = f"{approval_num}|{enterprise_name}"
                    approval_enterprise_to_info[composite_key] = {
                        'sampling_num': sampling_num,
                        'month': month
                    }

        print(f"建立了 {len(approval_to_info)} 个批准文号映射")
        print(f"建立了 {len(approval_enterprise_to_info)} 个批准文号+企业名称复合映射")

        # 创建被抽样单位名称到抽样信息的映射（包含日期）
        unit_to_info = {}
        for _, row in inspection_df.iterrows():
            unit_name = str(row[sampled_unit_col]).strip() if pd.notna(row[sampled_unit_col]) else ""
            sampling_num = str(row[sampling_col]).strip() if pd.notna(row[sampling_col]) else ""
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            if unit_name and unit_name != "nan":
                unit_to_info[unit_name] = {
                    'sampling_num': sampling_num,
                    'month': month
                }

        print(f"建立了 {len(unit_to_info)} 个被抽样单位映射")

        # 创建药品生产企业名称到抽样信息的映射（包含日期）
        enterprise_to_info = {}
        for _, row in inspection_df.iterrows():
            enterprise_name = str(row[production_enterprise_col]).strip() if pd.notna(row[production_enterprise_col]) else ""
            sampling_num = str(row[sampling_col]).strip() if pd.notna(row[sampling_col]) else ""
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            if enterprise_name and enterprise_name != "nan":
                enterprise_to_info[enterprise_name] = {
                    'sampling_num': sampling_num,
                    'month': month
                }

        print(f"建立了 {len(enterprise_to_info)} 个药品生产企业映射")

        # 创建跟踪抽检的复合映射（企业名称+药品通用名称）
        tracking_inspection_to_info = {}
        enterprise_only_to_info = {}  # 仅企业名称的映射，作为备选

        if drug_generic_name_col:
            for _, row in inspection_df.iterrows():
                enterprise_name = str(row[production_enterprise_col]).strip() if pd.notna(row[production_enterprise_col]) else ""
                drug_name = str(row[drug_generic_name_col]).strip() if pd.notna(row[drug_generic_name_col]) else ""
                sampling_num = str(row[sampling_col]).strip() if pd.notna(row[sampling_col]) else ""
                sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
                month = parse_date_to_month(sampling_date)

                if enterprise_name and enterprise_name != "nan":
                    # 创建企业名称+药品通用名称的复合键
                    if drug_name and drug_name != "nan":
                        composite_key = f"{enterprise_name}|{drug_name}"
                        tracking_inspection_to_info[composite_key] = {
                            'sampling_num': sampling_num,
                            'month': month
                        }

                    # 同时创建仅企业名称的映射作为备选
                    if enterprise_name not in enterprise_only_to_info:
                        enterprise_only_to_info[enterprise_name] = {
                            'sampling_num': sampling_num,
                            'month': month
                        }

        print(f"建立了 {len(tracking_inspection_to_info)} 个跟踪抽检复合映射（企业+药品名称）")
        print(f"建立了 {len(enterprise_only_to_info)} 个跟踪抽检企业映射（仅企业名称）")
        
        # 读取生产环节任务专项文件的所有sheet
        print("正在读取生产环节任务专项文件...")
        production_sheets = pd.read_excel(production_task_path, sheet_name=None)
        
        print(f"生产环节任务专项文件包含 {len(production_sheets)} 个sheet:")
        for sheet_name in production_sheets.keys():
            print(f"  - {sheet_name}")
        
        # 处理每个sheet
        results = {}
        
        for sheet_name, df in production_sheets.items():
            print(f"\n处理sheet: {sheet_name}")
            print(f"该sheet包含 {len(df)} 行数据")
            print(f"该sheet列名: {list(df.columns)}")

            # 复制数据框
            result_df = df.copy()

            # 特殊处理生产企业sheet
            if sheet_name == "生产企业":
                # 查找企业名称列
                enterprise_col = find_enterprise_name_column(df)

                if enterprise_col is None:
                    print(f"  警告：在sheet '{sheet_name}' 中找不到企业名称列，跳过")
                    continue

                print(f"  找到企业名称列: {enterprise_col}")

                # 添加抽样凭证编号列（基于企业名称匹配）
                sampling_numbers = []
                completion_status = []
                months = []

                for _, row in result_df.iterrows():
                    enterprise_name = str(row[enterprise_col]).strip() if pd.notna(row[enterprise_col]) else ""

                    if enterprise_name and enterprise_name != "nan" and enterprise_name in enterprise_to_info:
                        info = enterprise_to_info[enterprise_name]
                        sampling_numbers.append(info['sampling_num'])
                        completion_status.append("已完成")
                        months.append(info['month'])
                    else:
                        sampling_numbers.append("")
                        completion_status.append("未完成")
                        months.append(None)

                result_df['抽样凭证编号'] = sampling_numbers
                result_df['完成情况'] = completion_status
                result_df['抽样月份'] = months

                # 统计完成情况
                completed_count = sum(1 for status in completion_status if status == "已完成")
                total_count = len(completion_status)

                print(f"  {sheet_name}: 已完成 {completed_count}/{total_count} ({completed_count/total_count*100:.1f}%)")

            # 特殊处理跟踪抽检sheet
            elif sheet_name == "跟踪抽检":
                # 查找跟踪抽检相关列
                production_unit_col, drug_name_col = find_tracking_inspection_columns(df)

                if production_unit_col is None:
                    print(f"  警告：在sheet '{sheet_name}' 中找不到生产单位（委托方）列，跳过")
                    continue

                print(f"  找到生产单位（委托方）列: {production_unit_col}")
                print(f"  找到药品通用名列: {drug_name_col}")

                # 添加抽样凭证编号列（基于企业名称+药品通用名称匹配）
                sampling_numbers = []
                completion_status = []
                months = []

                for _, row in result_df.iterrows():
                    production_unit = str(row[production_unit_col]).strip() if pd.notna(row[production_unit_col]) else ""
                    drug_name = str(row[drug_name_col]).strip() if pd.notna(row[drug_name_col]) and drug_name_col else ""

                    matched = False

                    # 首先尝试企业名称+药品通用名称的复合匹配
                    if production_unit and production_unit != "nan" and drug_name and drug_name != "nan":
                        composite_key = f"{production_unit}|{drug_name}"
                        if composite_key in tracking_inspection_to_info:
                            info = tracking_inspection_to_info[composite_key]
                            sampling_numbers.append(info['sampling_num'])
                            completion_status.append("已完成")
                            months.append(info['month'])
                            matched = True

                    # 如果复合匹配失败，尝试仅企业名称匹配
                    if not matched and production_unit and production_unit != "nan":
                        if production_unit in enterprise_only_to_info:
                            info = enterprise_only_to_info[production_unit]
                            sampling_numbers.append(info['sampling_num'])
                            completion_status.append("已完成")
                            months.append(info['month'])
                            matched = True

                    # 如果都没有匹配到
                    if not matched:
                        sampling_numbers.append("")
                        completion_status.append("未完成")
                        months.append(None)

                result_df['抽样凭证编号'] = sampling_numbers
                result_df['完成情况'] = completion_status
                result_df['抽样月份'] = months

                # 统计完成情况
                completed_count = sum(1 for status in completion_status if status == "已完成")
                total_count = len(completion_status)

                print(f"  {sheet_name}: 已完成 {completed_count}/{total_count} ({completed_count/total_count*100:.1f}%)")

            # 特殊处理医疗机构sheet
            elif sheet_name == "医疗机构":
                # 查找医疗机构名称列
                medical_col = find_medical_institution_column(df)

                if medical_col is None:
                    print(f"  警告：在sheet '{sheet_name}' 中找不到医疗机构名称列，跳过")
                    continue

                print(f"  找到医疗机构名称列: {medical_col}")

                # 添加抽样凭证编号列（基于医疗机构名称匹配）
                sampling_numbers = []
                completion_status = []
                months = []

                for _, row in result_df.iterrows():
                    institution_name = str(row[medical_col]).strip() if pd.notna(row[medical_col]) else ""

                    if institution_name and institution_name != "nan" and institution_name in unit_to_info:
                        info = unit_to_info[institution_name]
                        sampling_numbers.append(info['sampling_num'])
                        completion_status.append("已完成")
                        months.append(info['month'])
                    else:
                        sampling_numbers.append("")
                        completion_status.append("未完成")
                        months.append(None)

                result_df['抽样凭证编号'] = sampling_numbers
                result_df['完成情况'] = completion_status
                result_df['抽样月份'] = months

                # 统计完成情况
                completed_count = sum(1 for status in completion_status if status == "已完成")
                total_count = len(completion_status)

                print(f"  {sheet_name}: 已完成 {completed_count}/{total_count} ({completed_count/total_count*100:.1f}%)")

            else:
                # 其他sheet按复合匹配逻辑处理（基于批准文号+企业名称匹配）
                approval_col_prod = find_approval_number_column(df)

                if approval_col_prod is None:
                    print(f"  警告：在sheet '{sheet_name}' 中找不到批准文号相关列，跳过")
                    continue

                # 查找企业名称相关列
                enterprise_col_prod = find_production_enterprise_column(df)
                if enterprise_col_prod is None:
                    # 尝试查找其他可能的企业名称列
                    possible_enterprise_cols = ['生产企业', '企业名称', '生产单位', '持有人名称', '上市许可持有人']
                    for col in df.columns:
                        col_str = str(col).strip()
                        for name in possible_enterprise_cols:
                            if name in col_str:
                                enterprise_col_prod = col
                                break
                        if enterprise_col_prod:
                            break

                print(f"  找到批准文号列: {approval_col_prod}")
                print(f"  找到企业名称列: {enterprise_col_prod}")

                # 添加抽样凭证编号列（使用复合匹配：批准文号+企业名称）
                sampling_numbers = []
                completion_status = []
                months = []

                for _, row in result_df.iterrows():
                    approval_num = str(row[approval_col_prod]).strip() if pd.notna(row[approval_col_prod]) else ""
                    enterprise_name = str(row[enterprise_col_prod]).strip() if pd.notna(row[enterprise_col_prod]) and enterprise_col_prod else ""

                    matched = False

                    # 优先使用批准文号+企业名称的复合匹配
                    if approval_num and approval_num != "nan" and enterprise_name and enterprise_name != "nan":
                        composite_key = f"{approval_num}|{enterprise_name}"
                        if composite_key in approval_enterprise_to_info:
                            info = approval_enterprise_to_info[composite_key]
                            sampling_numbers.append(info['sampling_num'])
                            completion_status.append("已完成")
                            months.append(info['month'])
                            matched = True

                    # 如果复合匹配失败，尝试仅批准文号匹配（作为备选）
                    if not matched and approval_num and approval_num != "nan" and approval_num in approval_to_info:
                        # 但是需要验证企业名称是否匹配（如果有企业名称列的话）
                        if enterprise_col_prod is None:  # 如果没有企业名称列，则使用批准文号匹配
                            info = approval_to_info[approval_num]
                            sampling_numbers.append(info['sampling_num'])
                            completion_status.append("已完成")
                            months.append(info['month'])
                            matched = True

                    # 如果都没有匹配到
                    if not matched:
                        sampling_numbers.append("")
                        completion_status.append("未完成")
                        months.append(None)

                result_df['抽样凭证编号'] = sampling_numbers
                result_df['完成情况'] = completion_status
                result_df['抽样月份'] = months

                # 统计完成情况
                completed_count = sum(1 for status in completion_status if status == "已完成")
                total_count = len(completion_status)

                print(f"  {sheet_name}: 已完成 {completed_count}/{total_count} ({completed_count/total_count*100:.1f}%)")
                if enterprise_col_prod:
                    print(f"    匹配方式: 批准文号+企业名称复合匹配")
                else:
                    print(f"    匹配方式: 仅批准文号匹配（未找到企业名称列）")

            results[sheet_name] = result_df
        
        # 创建汇总数据
        print("\n正在创建汇总统计...")

        # 获取所有可能的月份
        all_months = set()
        for _, row in inspection_df.iterrows():
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)
            if month:
                all_months.add(month)

        all_months = sorted(list(all_months), reverse=True)  # 从最大到最小排序
        print(f"发现的月份: {all_months}")

        # 创建动态列名
        columns = ['任务名称', '总任务数']
        for month in all_months:
            columns.extend([f'{month}已完成数', f'{month}未完成数', f'{month}完成率(%)'])
        columns.append('匹配方式')

        summary_data = []
        total_completed = 0
        total_tasks = 0

        # 统计任务批数（基于抽样凭证编号）
        unique_sampling_certificates = set()
        monthly_batch_stats = {}

        for _, row in inspection_df.iterrows():
            sampling_num = str(row[sampling_col]).strip() if pd.notna(row[sampling_col]) else ""
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            if sampling_num and sampling_num != "nan":
                unique_sampling_certificates.add(sampling_num)
                if month:
                    if month not in monthly_batch_stats:
                        monthly_batch_stats[month] = 0
                    monthly_batch_stats[month] += 1

        batch_completed = len(unique_sampling_certificates)
        batch_total = 530  # 总任务数是530

        # 创建任务批数行（累计统计，按月份从大到小累计）
        batch_row = {'任务名称': '任务批数', '总任务数': batch_total}

        # 先计算所有月份的累计值（按时间顺序从小到大累计）
        sorted_months_asc = sorted(all_months)  # 从小到大排序用于累计计算
        cumulative_values = {}
        cumulative_completed = 0

        for month in sorted_months_asc:
            completed_month = monthly_batch_stats.get(month, 0)
            cumulative_completed += completed_month
            cumulative_values[month] = cumulative_completed

        # 然后按显示顺序（从大到小）填充数据
        for month in all_months:
            cumulative_completed = cumulative_values[month]
            uncompleted_month = 0  # 任务批数没有未完成的概念
            completion_rate = (cumulative_completed / batch_total * 100) if batch_total > 0 else 0.0
            batch_row[f'{month}已完成数'] = cumulative_completed
            batch_row[f'{month}未完成数'] = uncompleted_month
            batch_row[f'{month}完成率(%)'] = round(completion_rate, 1)
        batch_row['匹配方式'] = '抽样凭证编号'
        summary_data.append(batch_row)

        for sheet_name, df in results.items():
            total = len(df)

            # 确定匹配方式
            if sheet_name == "医疗机构":
                match_type = "医疗机构名称"
            elif sheet_name == "生产企业":
                match_type = "企业名称"
            elif sheet_name == "跟踪抽检":
                match_type = "企业名称+药品通用名称"
            else:
                match_type = "批准文号+企业名称"

            # 按月统计
            monthly_stats = {}
            for _, row in df.iterrows():
                month = row.get('抽样月份')
                status = row['完成情况']

                if month:
                    if month not in monthly_stats:
                        monthly_stats[month] = {'已完成': 0, '未完成': 0}
                    monthly_stats[month][status] += 1
                else:
                    # 未知月份的都算作未完成
                    if '未知' not in monthly_stats:
                        monthly_stats['未知'] = {'已完成': 0, '未完成': 0}
                    monthly_stats['未知'][status] += 1

            # 创建该sheet的行（累计统计，按月份从大到小累计）
            sheet_row = {'任务名称': sheet_name, '总任务数': total}

            # 先计算所有月份的累计值（按时间顺序从小到大累计）
            sorted_months_asc = sorted(all_months)  # 从小到大排序用于累计计算
            cumulative_values = {}
            cumulative_completed = 0

            for month in sorted_months_asc:
                completed_month = monthly_stats.get(month, {}).get('已完成', 0)
                cumulative_completed += completed_month
                cumulative_values[month] = cumulative_completed

            # 然后按显示顺序（从大到小）填充数据
            for month in all_months:
                cumulative_completed = cumulative_values[month]
                uncompleted_month = monthly_stats.get(month, {}).get('未完成', 0)
                completion_rate = (cumulative_completed / total * 100) if total > 0 else 0.0

                sheet_row[f'{month}已完成数'] = cumulative_completed
                sheet_row[f'{month}未完成数'] = uncompleted_month
                sheet_row[f'{month}完成率(%)'] = round(completion_rate, 1)

            sheet_row['匹配方式'] = match_type
            summary_data.append(sheet_row)

            completed = len(df[df['完成情况'] == '已完成'])
            total_completed += completed
            total_tasks += total

        # 统计购样金额（基于药品总价）并按月分组
        total_amount = 0
        monthly_amounts = {}

        for _, row in inspection_df.iterrows():
            amount = row["药品总价（元）"] if pd.notna(row["药品总价（元）"]) else 0
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            try:
                amount_float = float(amount)
                total_amount += amount_float

                if month:
                    if month not in monthly_amounts:
                        monthly_amounts[month] = 0
                    monthly_amounts[month] += amount_float
            except (ValueError, TypeError):
                pass

        # 创建购样金额行（累计金额，按月份从大到小累计）
        amount_row = {'任务名称': '购样金额', '总任务数': "-"}  # 不统计总任务数

        # 先计算所有月份的累计值（按时间顺序从小到大累计）
        sorted_months_asc = sorted(all_months)  # 从小到大排序用于累计计算
        cumulative_amount_values = {}
        cumulative_amount = 0

        for month in sorted_months_asc:
            month_amount = monthly_amounts.get(month, 0)
            cumulative_amount += month_amount
            cumulative_amount_values[month] = cumulative_amount

        # 然后按显示顺序（从大到小）填充数据
        for month in all_months:
            cumulative_amount = cumulative_amount_values[month]
            amount_row[f'{month}已完成数'] = f"{cumulative_amount:.2f}元"
            amount_row[f'{month}未完成数'] = "-"  # 不统计
            amount_row[f'{month}完成率(%)'] = "-"  # 不统计

        amount_row['匹配方式'] = '药品总价统计'
        summary_data.append(amount_row)

        # 创建汇总DataFrame
        summary_df = pd.DataFrame(summary_data)

        # 导出结果到Python脚本所在目录
        output_filename = f"2025年省抽任务分析结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        script_dir = os.path.dirname(os.path.abspath(__file__))  # 获取Python脚本所在目录
        output_path = os.path.join(script_dir, output_filename)

        print(f"\n正在导出结果到: {output_path}")

        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 首先写入汇总sheet
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

            # 然后写入其他sheet
            for sheet_name, df in results.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 设置格式
            from openpyxl.styles import Alignment, Font, PatternFill, Border, Side

            # 定义样式
            header_font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
            header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
            data_font = Font(name='微软雅黑', size=10)
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            header_alignment = Alignment(wrap_text=True, horizontal='center', vertical='center')
            data_alignment = Alignment(horizontal='center', vertical='center')

            # 设置汇总统计sheet的格式
            summary_ws = writer.sheets['汇总统计']

            # 为汇总统计sheet的所有单元格设置自动换行
            data_alignment_wrap = Alignment(horizontal='center', vertical='center', wrap_text=True)

            # 设置表头格式
            for cell in summary_ws[1]:  # 第一行是表头
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment  # 表头已经包含自动换行
                cell.border = thin_border

            # 设置数据行格式（所有单元格都自动换行）
            for row in summary_ws.iter_rows(min_row=2, max_row=summary_ws.max_row):
                for cell in row:
                    cell.font = data_font
                    cell.alignment = data_alignment_wrap  # 使用包含自动换行的对齐方式
                    cell.border = thin_border

                    # 为购样金额行设置特殊颜色
                    if cell.row > 1 and summary_ws.cell(row=cell.row, column=1).value == '购样金额':
                        cell.fill = PatternFill(start_color='E7F3FF', end_color='E7F3FF', fill_type='solid')
                    # 为任务批数行设置特殊颜色
                    elif cell.row > 1 and summary_ws.cell(row=cell.row, column=1).value == '任务批数':
                        cell.fill = PatternFill(start_color='F0F8E7', end_color='F0F8E7', fill_type='solid')

            # 调整列宽
            for column in summary_ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 3, 22)  # 增加宽度
                summary_ws.column_dimensions[column_letter].width = adjusted_width

            # 设置行高
            summary_ws.row_dimensions[1].height = 30  # 表头行高
            for row_num in range(2, summary_ws.max_row + 1):
                summary_ws.row_dimensions[row_num].height = 25  # 数据行高

            # 设置其他sheet的格式
            for sheet_name in results.keys():
                ws = writer.sheets[sheet_name]

                # 设置表头格式
                for cell in ws[1]:  # 第一行是表头
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                    cell.border = thin_border

                # 设置数据行格式
                for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
                    for cell in row:
                        cell.font = data_font
                        cell.alignment = data_alignment
                        cell.border = thin_border

                        # 为已完成的行设置绿色背景
                        if cell.column == ws.max_column - 1:  # 完成情况列
                            if cell.value == '已完成':
                                for c in row:
                                    c.fill = PatternFill(start_color='E8F5E8', end_color='E8F5E8', fill_type='solid')

                # 调整列宽
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 3, 28)  # 增加宽度
                    ws.column_dimensions[column_letter].width = adjusted_width

                # 设置行高
                ws.row_dimensions[1].height = 30  # 表头行高
                for row_num in range(2, ws.max_row + 1):
                    ws.row_dimensions[row_num].height = 22  # 数据行高

            # 在汇总统计sheet中添加任务批数进度分析图表
            create_progress_analysis_charts(writer.sheets['汇总统计'], all_months, monthly_batch_stats, batch_total)

        print("导出完成！")

        # 打印总体统计
        print("\n=== 总体统计 ===")
        print(f"任务批数: {batch_completed}/{batch_total} ({batch_completed/batch_total*100:.1f}%)")
        print(f"购样金额: {total_amount:.2f}元")

        if all_months:
            print("\n月度统计:")
            for month in all_months:
                print(f"  {month}:")
                for _, row in summary_df.iterrows():
                    if row['任务名称'] not in ['任务批数', '购样金额']:
                        completed_col = f'{month}已完成数'
                        uncompleted_col = f'{month}未完成数'
                        if completed_col in row and row[completed_col] > 0:
                            print(f"    {row['任务名称']}: {row[completed_col]}完成/{row[uncompleted_col]}未完成")

        print(f"\n各sheet总计: {total_completed}/{total_tasks} ({total_completed/total_tasks*100:.1f}%)")
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

def create_progress_analysis_charts(ws, all_months, monthly_batch_stats, batch_total):
    """
    在汇总统计sheet中创建任务批数进度分析图表（简化版）
    """
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from datetime import datetime, timedelta
    import calendar

    # 定义样式
    data_font = Font(name='微软雅黑', size=10)
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # 计算图表数据
    sorted_months_asc = sorted(all_months)  # 按时间顺序排序用于图表

    # 准备数据
    chart_data = []
    cumulative_completed = 0

    for month in sorted_months_asc:
        monthly_count = monthly_batch_stats.get(month, 0)
        cumulative_completed += monthly_count
        completion_rate = (cumulative_completed / batch_total * 100) if batch_total > 0 else 0

        chart_data.append({
            'month': month,
            'monthly_count': monthly_count,
            'cumulative_count': cumulative_completed,
            'completion_rate': completion_rate,
            'remaining': batch_total - cumulative_completed
        })

    # 在表格下方添加分析标题
    start_row = ws.max_row + 3

    # 添加分析标题
    title_cell = ws.cell(row=start_row, column=1)
    title_cell.value = "任务批数进度分析"
    title_cell.font = Font(name='微软雅黑', size=14, bold=True, color='2F5597')
    title_cell.alignment = Alignment(horizontal='left', vertical='center')

    # 添加数据分析表格（用于图表数据源，但不显示）
    data_start_row = start_row + 2

    # 表头（隐藏）
    headers = ['月份', '累计完成数']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=data_start_row, column=col)
        cell.value = header
        # 设置为白色字体（隐藏效果）
        cell.font = Font(name='微软雅黑', size=1, color='FFFFFF')

    # 数据行（隐藏）
    for i, data in enumerate(chart_data):
        row = data_start_row + 1 + i

        # 月份
        cell = ws.cell(row=row, column=1)
        cell.value = data['month']
        cell.font = Font(name='微软雅黑', size=1, color='FFFFFF')

        # 累计完成数
        cell = ws.cell(row=row, column=2)
        cell.value = data['cumulative_count']
        cell.font = Font(name='微软雅黑', size=1, color='FFFFFF')

    # 创建累计进度折线图
    chart_start_row = start_row + 2

    # 创建折线图
    line_chart = LineChart()
    line_chart.title = "任务批数累计完成趋势"
    line_chart.style = 10
    line_chart.y_axis.title = '累计完成数'
    line_chart.x_axis.title = '月份'
    line_chart.width = 15
    line_chart.height = 10

    # 数据引用
    data_ref = Reference(ws, min_col=2, min_row=data_start_row, max_row=data_start_row + len(chart_data))
    cats_ref = Reference(ws, min_col=1, min_row=data_start_row + 1, max_row=data_start_row + len(chart_data))

    line_chart.add_data(data_ref, titles_from_data=True)
    line_chart.set_categories(cats_ref)

    # 将图表添加到工作表
    ws.add_chart(line_chart, f"A{chart_start_row + 1}")

    # 添加完成时间预测分析
    prediction_start_row = chart_start_row + 20

    # 预测标题
    prediction_title_cell = ws.cell(row=prediction_start_row, column=1)
    prediction_title_cell.value = "完成时间预测分析"
    prediction_title_cell.font = Font(name='微软雅黑', size=12, bold=True, color='2F5597')
    prediction_title_cell.alignment = Alignment(horizontal='left', vertical='center')

    # 计算预测逻辑
    if len(chart_data) >= 2:
        # 计算月均完成速度（基于最近几个月的数据）
        total_completed = chart_data[-1]['cumulative_count']
        total_months = len(chart_data)
        avg_monthly_rate = total_completed / total_months

        # 计算剩余任务数
        remaining_tasks = batch_total - total_completed

        # 预测完成所需月数
        months_needed = remaining_tasks / avg_monthly_rate if avg_monthly_rate > 0 else float('inf')

        # 计算预测完成日期
        last_month = sorted_months_asc[-1]  # 最后一个月份
        last_year, last_month_num = int(last_month.split('-')[0]), int(last_month.split('-')[1])

        # 从最后一个月开始计算
        predicted_year = last_year
        predicted_month = last_month_num + int(months_needed)

        # 处理跨年情况
        while predicted_month > 12:
            predicted_month -= 12
            predicted_year += 1

        predicted_date = f"{predicted_year}年{predicted_month}月"

        # 检查是否能在10月20日前完成
        target_date = datetime(2025, 10, 20)
        if predicted_year < 2025 or (predicted_year == 2025 and predicted_month <= 10):
            can_complete_on_time = "是"
            time_status = "能够按时完成"
        else:
            can_complete_on_time = "否"
            delay_months = (predicted_year - 2025) * 12 + (predicted_month - 10)
            time_status = f"预计延期约{delay_months}个月"

        # 添加预测分析数据
        prediction_data = [
            ('当前完成进度', f"{total_completed}/{batch_total} 批 ({total_completed/batch_total*100:.1f}%)"),
            ('月均完成速度', f"{avg_monthly_rate:.1f} 批/月"),
            ('剩余任务数量', f"{remaining_tasks} 批"),
            ('预计完成时间', predicted_date),
            ('能否在10月20日前完成', can_complete_on_time),
            ('时间评估', time_status)
        ]

        # 添加预测逻辑说明
        logic_start_row = prediction_start_row + 2
        logic_cell = ws.cell(row=logic_start_row, column=1)
        logic_cell.value = "预测逻辑说明："
        logic_cell.font = Font(name='微软雅黑', size=10, bold=True)
        logic_cell.alignment = Alignment(horizontal='left', vertical='center')

        logic_text = (
            f"基于{total_months}个月的历史数据，计算月均完成速度为{avg_monthly_rate:.1f}批/月。"
            f"剩余{remaining_tasks}批任务，按此速度需要{months_needed:.1f}个月完成，"
            f"预计在{predicted_date}完成全部530批任务。"
        )

        logic_detail_cell = ws.cell(row=logic_start_row + 1, column=1)
        logic_detail_cell.value = logic_text
        logic_detail_cell.font = Font(name='微软雅黑', size=9, color='666666')
        logic_detail_cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

        # 添加预测数据
        for i, (indicator, value) in enumerate(prediction_data):
            row = logic_start_row + 3 + i

            # 指标名称
            name_cell = ws.cell(row=row, column=1)
            name_cell.value = indicator
            name_cell.font = Font(name='微软雅黑', size=10, bold=True)
            name_cell.alignment = Alignment(horizontal='left', vertical='center')

            # 指标值
            value_cell = ws.cell(row=row, column=2)
            value_cell.value = value
            # 如果是"否"或包含"延期"，使用红色字体
            if "否" in str(value) or "延期" in str(value):
                value_cell.font = Font(name='微软雅黑', size=10, color='DC143C', bold=True)
            else:
                value_cell.font = Font(name='微软雅黑', size=10, color='2F5597')
            value_cell.alignment = Alignment(horizontal='left', vertical='center')

    # 调整列宽
    ws.column_dimensions['A'].width = 25
    ws.column_dimensions['B'].width = 30

if __name__ == "__main__":
    main()
