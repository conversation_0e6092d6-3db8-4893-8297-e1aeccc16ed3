#!/bin/bash

# 调试MCP容器问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "========================================"
echo "  调试MCP容器问题"
echo "========================================"
echo

# 1. 检查当前容器状态
log_step "1. 检查当前容器状态"
echo "所有容器状态："
docker-compose ps
echo

echo "MCP相关容器详细状态："
docker ps -a | grep mcp || echo "没有找到MCP容器"
echo

# 2. 检查容器日志
log_step "2. 检查容器日志"

echo "=== MCP SSE 容器日志 ==="
if docker-compose logs mcp-server-chart-sse 2>/dev/null; then
    echo "SSE容器日志获取成功"
else
    log_error "无法获取SSE容器日志"
fi
echo

echo "=== MCP Streamable 容器日志 ==="
if docker-compose logs mcp-server-chart-streamable 2>/dev/null; then
    echo "Streamable容器日志获取成功"
else
    log_error "无法获取Streamable容器日志"
fi
echo

# 3. 检查目录结构
log_step "3. 检查目录结构"
echo "当前目录内容："
ls -la
echo

echo "检查mcp-server-chart目录："
if [[ -d "mcp-server-chart" ]]; then
    ls -la mcp-server-chart/
else
    log_error "mcp-server-chart目录不存在！"
fi
echo

# 4. 尝试手动启动容器
log_step "4. 尝试手动启动MCP容器"

echo "停止现有容器..."
docker-compose stop mcp-server-chart-sse mcp-server-chart-streamable 2>/dev/null || true

echo "删除现有容器..."
docker-compose rm -f mcp-server-chart-sse mcp-server-chart-streamable 2>/dev/null || true

echo "重新启动MCP容器..."
docker-compose up -d mcp-server-chart-sse mcp-server-chart-streamable

echo "等待容器启动..."
sleep 15

# 5. 检查启动后状态
log_step "5. 检查启动后状态"
docker-compose ps | grep mcp
echo

# 6. 测试容器内部
log_step "6. 测试容器内部"

echo "测试SSE容器内部..."
if docker exec mcp-server-chart-sse ps aux 2>/dev/null; then
    echo "SSE容器内部进程正常"
else
    log_error "无法访问SSE容器内部"
fi
echo

echo "测试Streamable容器内部..."
if docker exec mcp-server-chart-streamable ps aux 2>/dev/null; then
    echo "Streamable容器内部进程正常"
else
    log_error "无法访问Streamable容器内部"
fi
echo

# 7. 检查端口监听
log_step "7. 检查端口监听"

echo "检查SSE容器端口监听..."
docker exec mcp-server-chart-sse netstat -tlnp 2>/dev/null | grep 1122 || echo "端口1122未监听"

echo "检查Streamable容器端口监听..."
docker exec mcp-server-chart-streamable netstat -tlnp 2>/dev/null | grep 1123 || echo "端口1123未监听"
echo

# 8. 尝试在容器内手动安装和启动
log_step "8. 尝试手动安装MCP服务器"

echo "在SSE容器中手动安装..."
docker exec mcp-server-chart-sse sh -c "
    echo '正在安装@antv/mcp-server-chart...'
    npm install -g @antv/mcp-server-chart
    echo '安装完成，检查版本...'
    mcp-server-chart --version || echo '版本检查失败'
" 2>&1

echo

echo "在Streamable容器中手动安装..."
docker exec mcp-server-chart-streamable sh -c "
    echo '正在安装@antv/mcp-server-chart...'
    npm install -g @antv/mcp-server-chart
    echo '安装完成，检查版本...'
    mcp-server-chart --version || echo '版本检查失败'
" 2>&1

echo

# 9. 最终测试
log_step "9. 最终测试"

echo "等待服务启动..."
sleep 10

echo "测试端口连通性..."
if curl -s --connect-timeout 5 http://192.168.50.105:1122 >/dev/null 2>&1; then
    log_info "✓ 端口1122可访问"
else
    log_error "✗ 端口1122不可访问"
fi

if curl -s --connect-timeout 5 http://192.168.50.105:1123 >/dev/null 2>&1; then
    log_info "✓ 端口1123可访问"
else
    log_error "✗ 端口1123不可访问"
fi

echo

# 10. 生成解决方案
log_step "10. 解决方案建议"

echo "基于诊断结果，建议："
echo

if curl -s --connect-timeout 5 http://192.168.50.105:1123 >/dev/null 2>&1; then
    echo "✓ MCP服务器已修复，可以在LobeChat中使用："
    echo '{'
    echo '  "name": "Chart Generator",'
    echo '  "url": "http://192.168.50.105:1123",'
    echo '  "type": "streamable-http"'
    echo '}'
else
    echo "✗ MCP服务器仍有问题，建议："
    echo "1. 检查Docker和Node.js环境"
    echo "2. 手动重新部署"
    echo "3. 使用Cherry Studio的方式（已验证可用）"
fi

echo

echo "========================================"
echo "  调试完成"
echo "========================================"
