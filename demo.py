#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片转Excel工具演示脚本
展示如何使用工具的各种功能
"""

import os
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill

def create_demo_data():
    """创建演示数据"""
    # 基于您图片中的表格结构创建示例数据
    data = [
        ['序号', '项目名称', '规格型号', '数量', '单价(元)', '金额(元)', '备注'],
        ['1', '办公桌', '1.2m×0.6m', '10', '450.00', '4500.00', '已采购'],
        ['2', '办公椅', '可调节高度', '10', '280.00', '2800.00', '已采购'],
        ['3', '电脑', 'i5/8G/256G', '5', '3500.00', '17500.00', '待验收'],
        ['4', '打印机', 'A4激光', '2', '1200.00', '2400.00', '已安装'],
        ['5', '文件柜', '四层钢制', '3', '680.00', '2040.00', '已到货'],
        ['6', '会议桌', '2.4m×1.2m', '1', '1800.00', '1800.00', '待安装'],
        ['7', '投影仪', '1080P', '1', '2500.00', '2500.00', '已采购'],
        ['8', '白板', '1.2m×0.9m', '2', '320.00', '640.00', '已安装'],
        ['合计', '', '', '', '', '33180.00', '']
    ]
    return data

def create_styled_excel(data, filename):
    """创建带样式的Excel文件"""
    wb = Workbook()
    ws = wb.active
    ws.title = "采购清单"
    
    # 定义样式
    header_font = Font(name='宋体', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    data_font = Font(name='宋体', size=10)
    total_font = Font(name='宋体', size=11, bold=True)
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 写入数据并设置样式
    for r_idx, row in enumerate(data, 1):
        for c_idx, value in enumerate(row, 1):
            cell = ws.cell(row=r_idx, column=c_idx, value=value)
            cell.border = border
            cell.alignment = Alignment(horizontal='center', vertical='center')
            
            if r_idx == 1:  # 标题行
                cell.font = header_font
                cell.fill = header_fill
            elif r_idx == len(data):  # 合计行
                cell.font = total_font
                if c_idx == 6:  # 金额列
                    cell.alignment = Alignment(horizontal='right', vertical='center')
            else:  # 数据行
                cell.font = data_font
                if c_idx in [4, 5, 6]:  # 数量、单价、金额列右对齐
                    cell.alignment = Alignment(horizontal='right', vertical='center')
    
    # 自动调整列宽
    column_widths = [6, 15, 15, 8, 12, 12, 10]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width
    
    # 设置行高
    for row in range(1, len(data) + 1):
        ws.row_dimensions[row].height = 25
    
    wb.save(filename)
    return True

def demo_simple_version():
    """演示简化版功能"""
    print("=== 简化版演示 ===")
    
    # 创建演示数据
    data = create_demo_data()
    filename = "演示_采购清单.xlsx"
    
    print(f"正在创建演示文件: {filename}")
    
    if create_styled_excel(data, filename):
        print(f"✓ 演示文件创建成功: {filename}")
        
        # 显示数据预览
        print("\n数据预览:")
        for i, row in enumerate(data[:3]):
            print(f"第{i+1}行: {row}")
        print("...")
        print(f"第{len(data)}行: {data[-1]}")
        
        return True
    else:
        print("✗ 演示文件创建失败")
        return False

def show_file_info():
    """显示项目文件信息"""
    print("\n=== 项目文件说明 ===")
    
    files_info = {
        'image_to_excel.py': '完整版OCR工具 - 自动识别图片中的表格',
        'simple_image_to_excel.py': '简化版工具 - 手动输入数据创建Excel',
        'install_dependencies.py': '依赖安装脚本 - 自动安装所需的Python包',
        '运行工具.bat': 'Windows批处理文件 - 一键启动工具',
        'requirements.txt': 'Python依赖列表 - 完整版功能所需的包',
        'README.md': '项目说明文档 - 详细的使用指南',
        '图片转Excel使用说明.md': '详细使用说明 - 包含技巧和常见问题'
    }
    
    print("核心文件:")
    for filename, description in files_info.items():
        status = "✓" if os.path.exists(filename) else "✗"
        print(f"{status} {filename:<25} - {description}")

def main():
    """主演示函数"""
    print("=" * 50)
    print("        图片转Excel工具演示")
    print("=" * 50)
    
    # 演示简化版功能
    if demo_simple_version():
        print("\n✓ 简化版功能演示完成")
    
    # 显示文件信息
    show_file_info()
    
    print("\n" + "=" * 50)
    print("使用建议:")
    print("1. 新手推荐使用简化版: python simple_image_to_excel.py")
    print("2. 需要OCR功能先安装依赖: python install_dependencies.py")
    print("3. Windows用户可直接运行: 运行工具.bat")
    print("4. 详细说明请查看: README.md")
    print("=" * 50)

if __name__ == "__main__":
    main()
