# 图片转Excel工具

## 项目简介
这是一个将图片中的表格数据转换为Excel文件的工具集。支持两种模式：
1. **完整版**：使用OCR技术自动识别图片中的表格
2. **简化版**：手动输入数据创建Excel文件

## 文件说明

### 核心工具
- `image_to_excel.py` - 完整版OCR工具
- `simple_image_to_excel.py` - 简化版手动输入工具
- `install_dependencies.py` - 依赖包安装脚本
- `运行工具.bat` - Windows批处理启动器

### 配置文件
- `requirements.txt` - Python依赖包列表
- `图片转Excel使用说明.md` - 详细使用说明

## 快速开始

### 方法一：使用批处理文件（推荐）
1. 双击 `运行工具.bat`
2. 根据提示选择工具版本
3. 按照界面指引操作

### 方法二：命令行运行
```bash
# 简化版（推荐新手）
python simple_image_to_excel.py

# 完整版（需要安装OCR依赖）
python image_to_excel.py

# 安装依赖
python install_dependencies.py
```

## 功能特点

### 简化版工具
- ✅ 无需额外依赖
- ✅ 手动输入数据
- ✅ 自动格式化Excel
- ✅ 支持中文
- ✅ 即装即用

### 完整版工具
- 🔧 需要安装OCR依赖
- 🔧 自动识别图片文字
- 🔧 智能解析表格结构
- 🔧 支持多种图片格式
- 🔧 中英文混合识别

## 系统要求
- Python 3.7+
- Windows/Linux/macOS

## 安装步骤

### 1. 基础安装
```bash
pip install pandas openpyxl
```

### 2. 完整功能安装（可选）
```bash
# 运行自动安装脚本
python install_dependencies.py

# 或手动安装
pip install -r requirements.txt
```

### 3. Tesseract OCR安装（完整版需要）
- **Windows**: 下载安装包 https://github.com/UB-Mannheim/tesseract/wiki
- **Linux**: `sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim`
- **macOS**: `brew install tesseract tesseract-lang`

## 使用示例

### 简化版使用
1. 运行 `python simple_image_to_excel.py`
2. 选择输入方式（手动输入或示例数据）
3. 按格式输入数据：`列1,列2,列3`
4. 输入空行结束
5. 查看生成的Excel文件

### 完整版使用
1. 准备清晰的表格图片
2. 运行 `python image_to_excel.py`
3. 输入图片路径
4. 等待OCR识别完成
5. 查看生成的Excel文件

## 支持的图片格式
- JPG/JPEG
- PNG
- BMP
- TIFF
- GIF

## 输出格式
- Excel文件 (.xlsx)
- 自动设置边框和格式
- 居中对齐
- 自动调整列宽

## 常见问题

### Q: 提示找不到模块
A: 运行 `pip install pandas openpyxl` 安装基础依赖

### Q: OCR识别不准确
A: 
- 确保图片清晰度足够
- 检查是否安装了中文语言包
- 尝试调整图片对比度

### Q: 无法识别表格结构
A: 
- 确保表格边框清晰
- 使用简化版手动输入
- 检查图片是否倾斜

## 技术栈
- **Python**: 核心编程语言
- **pandas**: 数据处理
- **openpyxl**: Excel文件操作
- **Tesseract**: OCR文字识别
- **OpenCV**: 图像预处理
- **PIL**: 图像处理

## 项目结构
```
├── image_to_excel.py          # 完整版OCR工具
├── simple_image_to_excel.py   # 简化版工具
├── install_dependencies.py    # 依赖安装脚本
├── 运行工具.bat               # Windows启动器
├── requirements.txt           # 依赖列表
├── 图片转Excel使用说明.md     # 详细说明
└── README.md                  # 项目说明
```

## 更新日志

### v1.0.0 (2025-01-21)
- 初始版本发布
- 支持OCR图片识别
- 支持手动数据输入
- 自动Excel格式化
- 中英文混合支持

## 许可证
本项目仅供学习和个人使用。

## 贡献
欢迎提交问题和改进建议！

---
**注意**: 使用OCR功能需要额外安装依赖包。如果只需要基础功能，建议使用简化版工具。
