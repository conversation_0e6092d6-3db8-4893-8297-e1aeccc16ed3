# MCP客户端配置指南

## 传输方式说明

MCP支持三种传输方式：
1. **stdio** - 标准输入输出，用于本地进程通信
2. **sse** - Server-Sent Events，用于HTTP长连接
3. **streamable** - 流式传输，用于HTTP流式通信

## 1. stdio传输配置（推荐用于本地客户端）

### Claude Desktop配置

#### macOS配置文件位置
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

#### Windows配置文件位置
```
%APPDATA%\Claude\claude_desktop_config.json
```

#### 配置内容（使用全局安装的包）
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "mcp-server-chart",
      "args": ["--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://localhost:3000/api/gpt-vis"
      }
    }
  }
}
```

#### 配置内容（使用npx）
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://localhost:3000/api/gpt-vis"
      }
    }
  }
}
```

#### Windows系统配置
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "cmd",
      "args": ["/c", "npx", "-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://localhost:3000/api/gpt-vis"
      }
    }
  }
}
```

### VSCode + Cline配置

在VSCode的设置中添加：
```json
{
  "cline.mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://localhost:3000/api/gpt-vis"
      }
    }
  }
}
```

### Cursor配置

在Cursor的设置中添加：
```json
{
  "mcp.servers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://localhost:3000/api/gpt-vis"
      }
    }
  }
}
```

## 2. SSE传输配置（推荐用于Web客户端）

### Cherry Studio配置
```
服务器类型: MCP Server
传输方式: SSE
服务器URL: http://localhost:1122/sse
```

### 自定义Web客户端配置
```javascript
const mcpClient = new MCPClient({
  transport: 'sse',
  url: 'http://localhost:1122/sse'
});
```

### 远程服务器配置
如果MCP服务部署在远程服务器上：
```
服务器URL: http://YOUR_SERVER_IP:1122/sse
```

## 3. Streamable传输配置

### 支持Streamable的客户端配置
```javascript
const mcpClient = new MCPClient({
  transport: 'streamable',
  url: 'http://localhost:1123/mcp'
});
```

### Cherry Studio Streamable配置
```
服务器类型: MCP Server
传输方式: Streamable
服务器URL: http://localhost:1123/mcp
```

## 环境变量说明

### VIS_REQUEST_SERVER
- **作用**: 指定图表渲染服务的URL
- **默认值**: `https://antv-studio.alipay.com/api/gpt-vis`（官方服务）
- **本地部署**: `http://localhost:3000/api/gpt-vis`
- **容器内**: `http://gpt-vis-ssr:3000/api/gpt-vis`

### SERVICE_ID（可选）
- **作用**: 用于保存图表生成记录
- **获取方式**: 通过支付宝小程序生成

### DISABLED_TOOLS（可选）
- **作用**: 禁用特定的图表生成工具
- **格式**: 逗号分隔的工具名称
- **示例**: `generate_fishbone_diagram,generate_mind_map`

## 故障排除

### 1. stdio传输问题

#### 检查Node.js和npm
```bash
node --version
npm --version
```

#### 检查包安装
```bash
npm list -g @antv/mcp-server-chart
```

#### 测试命令行工具
```bash
mcp-server-chart --help
```

#### 检查环境变量
```bash
echo $VIS_REQUEST_SERVER
```

### 2. SSE/Streamable传输问题

#### 检查服务状态
```bash
curl http://localhost:1122/sse
curl http://localhost:1123/mcp
```

#### 检查端口占用
```bash
netstat -tlnp | grep -E ":(1122|1123|3000)"
```

#### 检查防火墙
```bash
sudo ufw status
sudo ufw allow 1122
sudo ufw allow 1123
sudo ufw allow 3000
```

### 3. 渲染服务问题

#### 测试渲染服务
```bash
curl http://localhost:3000/health
```

#### 测试图表生成
```bash
curl -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{"type": "pie", "data": [{"category": "测试", "value": 100}]}'
```

## 推荐配置

### 本地开发环境
- **推荐**: stdio传输
- **优势**: 性能最好，配置简单
- **适用**: Claude Desktop, VSCode, Cursor

### 远程服务器环境
- **推荐**: SSE传输
- **优势**: 支持HTTP协议，易于部署
- **适用**: Web客户端，Cherry Studio

### 高并发环境
- **推荐**: Streamable传输
- **优势**: 支持流式处理，适合大量数据
- **适用**: 企业级应用

## 安全注意事项

1. **生产环境**: 使用HTTPS和认证
2. **防火墙**: 只开放必要端口
3. **网络**: 使用VPN或内网部署
4. **监控**: 添加日志和监控系统
