#!/bin/bash

# 修复Node Canvas依赖问题
# 解决gpt-vis-ssr容器中的Canvas编译失败

set -e

SERVER_IP="**************"
echo "=== 修复Node Canvas依赖问题 ==="

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-all-transports" "/opt/mcp-multi-transport" "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo "错误：找不到MCP部署目录"
    exit 1
fi

echo "使用工作目录: $WORK_DIR"
cd "$WORK_DIR"

# 停止现有服务
echo "1. 停止现有服务..."
docker compose down

# 创建修复后的package.json
echo "2. 创建修复后的package.json..."
cat > render-service/package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service - Canvas fixed",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "start-simple": "node server-simple.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5"
  },
  "optionalDependencies": {
    "@antv/gpt-vis-ssr": "^1.0.0",
    "canvas": "^2.11.2"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}
EOF

# 创建简化版服务器（不依赖Canvas）
echo "3. 创建简化版服务器..."
cat > render-service/server-simple.js << EOF
const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const https = require('https');

const app = express();

// 配置外部IP
const EXTERNAL_IP = '$SERVER_IP';
const PORT = process.env.PORT || 3000;

console.log(\`配置外部IP地址: \${EXTERNAL_IP}\`);

// 配置CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false
}));

app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

// 生成外部可访问的URL
function getExternalImageUrl(filename) {
  return \`http://\${EXTERNAL_IP}:\${PORT}/images/\${filename}\`;
}

// 使用在线服务生成图表
app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    // 使用在线渲染服务
    const postData = JSON.stringify(options);
    const requestOptions = {
      hostname: 'antv-studio.alipay.com',
      port: 443,
      path: '/api/gpt-vis',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const onlineReq = https.request(requestOptions, (onlineRes) => {
      let data = '';
      onlineRes.on('data', (chunk) => {
        data += chunk;
      });
      
      onlineRes.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.success) {
            // 如果在线服务返回base64，转换为本地URL
            if (result.resultObj && result.resultObj.startsWith('data:image')) {
              const base64Data = result.resultObj.replace(/^data:image\/png;base64,/, '');
              const buffer = Buffer.from(base64Data, 'base64');
              
              const filename = \`chart_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}.png\`;
              const filepath = path.join(imageDir, filename);
              
              fs.writeFileSync(filepath, buffer);
              
              const imageUrl = getExternalImageUrl(filename);
              
              console.log('图表生成成功:', filename);
              console.log('外部图片URL:', imageUrl);
              
              res.json({
                success: true,
                resultObj: imageUrl,
                imageUrl: imageUrl,
                filename: filename,
                size: buffer.length,
                timestamp: new Date().toISOString(),
                externalIP: EXTERNAL_IP,
                method: 'online-service'
              });
            } else {
              res.json(result);
            }
          } else {
            res.status(500).json(result);
          }
        } catch (error) {
          console.error('解析在线服务响应失败:', error);
          res.status(500).json({
            success: false,
            errorMessage: '在线服务响应解析失败'
          });
        }
      });
    });
    
    onlineReq.on('error', (error) => {
      console.error('在线服务请求失败:', error);
      res.status(500).json({
        success: false,
        errorMessage: '在线服务不可用，请检查网络连接'
      });
    });
    
    onlineReq.write(postData);
    onlineReq.end();
    
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr-simple',
    externalIP: EXTERNAL_IP,
    port: PORT,
    imageBaseUrl: \`http://\${EXTERNAL_IP}:\${PORT}/images/\`,
    imageCount: fs.readdirSync(imageDir).filter(f => f.endsWith('.png')).length,
    version: '1.0.0-simple',
    renderMethod: 'online-service'
  });
});

// 静态文件服务
app.use('/images', express.static(imageDir, {
  maxAge: '1d',
  setHeaders: (res, path) => {
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cache-Control', 'public, max-age=86400');
  }
}));

// 图片列表API
app.get('/api/images', (req, res) => {
  try {
    const files = fs.readdirSync(imageDir)
      .filter(f => f.endsWith('.png'))
      .map(f => {
        const filepath = path.join(imageDir, f);
        const stats = fs.statSync(filepath);
        return {
          filename: f,
          url: getExternalImageUrl(f),
          size: stats.size,
          created: stats.birthtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));
    
    res.json({
      success: true,
      images: files,
      total: files.length,
      externalIP: EXTERNAL_IP,
      baseUrl: \`http://\${EXTERNAL_IP}:\${PORT}/images/\`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(\`GPT-Vis-SSR简化服务运行在端口 \${PORT}\`);
  console.log(\`外部访问地址: http://\${EXTERNAL_IP}:\${PORT}\`);
  console.log(\`健康检查: http://\${EXTERNAL_IP}:\${PORT}/health\`);
  console.log(\`API端点: http://\${EXTERNAL_IP}:\${PORT}/api/gpt-vis\`);
  console.log(\`使用在线渲染服务，避免Canvas依赖问题\`);
});
EOF

# 创建智能启动脚本
echo "4. 创建智能启动脚本..."
cat > render-service/start.sh << 'EOF'
#!/bin/sh

echo "=== 智能启动脚本 ==="

# 首先尝试安装基础依赖
echo "安装基础依赖..."
npm install express cors --no-optional

# 检查是否可以安装Canvas相关依赖
echo "尝试安装Canvas依赖..."
if npm install @antv/gpt-vis-ssr canvas --no-optional 2>/dev/null; then
    echo "✅ Canvas依赖安装成功，启动完整服务..."
    if [ -f "server.js" ]; then
        node server.js
    else
        echo "❌ server.js不存在，启动简化服务..."
        node server-simple.js
    fi
else
    echo "⚠️ Canvas依赖安装失败，启动简化服务（使用在线渲染）..."
    node server-simple.js
fi
EOF

chmod +x render-service/start.sh

# 更新Docker Compose配置
echo "5. 更新Docker Compose配置..."
cat > docker-compose.yml << EOF
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:16-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
      - EXTERNAL_IP=$SERVER_IP
      - NPM_CONFIG_UNSAFE_PERM=true
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        echo "更新包管理器..."
        apk update
        echo "安装系统依赖..."
        apk add --no-cache \\
          cairo-dev \\
          pango-dev \\
          jpeg-dev \\
          giflib-dev \\
          librsvg-dev \\
          pixman-dev \\
          pkgconfig \\
          python3 \\
          make \\
          g++ \\
          curl \\
          git || echo "部分系统依赖安装失败，继续..."
        echo "设置npm配置..."
        npm config set unsafe-perm true
        npm config set registry https://registry.npmjs.org/
        echo "运行智能启动脚本..."
        sh start.sh
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # SSE传输方式
  mcp-sse-server:
    image: node:16-alpine
    container_name: mcp-sse-server
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl git
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..60}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... (\$i/60)"
          sleep 2
        done
        echo "启动MCP SSE服务器..."
        mcp-server-chart --transport sse --port 1122
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

  # Streamable传输方式
  mcp-streamable-server:
    image: node:16-alpine
    container_name: mcp-streamable-server
    working_dir: /app
    ports:
      - "1123:1123"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl git
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..60}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... (\$i/60)"
          sleep 2
        done
        echo "启动MCP Streamable服务器..."
        mcp-server-chart --transport streamable --port 1123
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

# 确保图片目录存在
mkdir -p images

# 启动服务
echo "6. 启动修复后的服务..."
docker compose up -d

echo "等待服务启动..."
sleep 90

# 检查服务状态
echo "7. 检查服务状态..."
docker compose ps

echo ""
echo "8. 查看服务日志..."
echo "--- 渲染服务日志 (最近30行) ---"
docker compose logs --tail=30 gpt-vis-ssr

echo ""
echo "9. 测试服务..."
echo "健康检查:"
for i in {1..5}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务响应正常"
        curl -s http://localhost:3000/health | jq . 2>/dev/null || curl -s http://localhost:3000/health
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "测试图表生成:"
RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "在线渲染", "value": 70},
      {"category": "本地渲染", "value": 30}
    ],
    "title": "Canvas修复方案"
  }')

echo "$RESPONSE" | jq . 2>/dev/null || echo "$RESPONSE"

if echo "$RESPONSE" | grep -q '"success":true'; then
    echo "✅ 图表生成测试成功"
    IMAGE_URL=$(echo "$RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    if [ "$IMAGE_URL" != "null" ]; then
        echo "图片URL: $IMAGE_URL"
    fi
else
    echo "❌ 图表生成测试失败"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "🔧 修复方案:"
echo "  ✅ 使用Node.js 16（更好的兼容性）"
echo "  ✅ 创建简化版服务（使用在线渲染，避免Canvas依赖）"
echo "  ✅ 智能启动脚本（优先尝试本地渲染，失败则使用在线渲染）"
echo "  ✅ 优化依赖安装流程"
echo ""
echo "🌐 服务地址:"
echo "  - 渲染服务: http://$SERVER_IP:3000"
echo "  - SSE服务: http://$SERVER_IP:1122/sse"
echo "  - Streamable服务: http://$SERVER_IP:1123/mcp"
echo ""
echo "📋 渲染方式:"
echo "  - 如果Canvas安装成功：使用本地渲染"
echo "  - 如果Canvas安装失败：使用在线渲染服务"
echo ""
echo "🔍 如果问题仍然存在:"
echo "  1. 查看详细日志: docker compose logs gpt-vis-ssr"
echo "  2. 重启服务: docker compose restart"
echo "  3. 检查网络连接: curl http://$SERVER_IP:3000/health"
