#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片转Excel工具
使用OCR技术识别图片中的表格内容，并转换为Excel文件
"""

import os
import pandas as pd
import numpy as np
from PIL import Image
import pytesseract
import cv2
import re
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

def setup_tesseract():
    """配置Tesseract OCR"""
    # Windows系统下Tesseract的常见安装路径
    possible_paths = [
        r'C:\Program Files\Tesseract-OCR\tesseract.exe',
        r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
        r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
        'tesseract'  # 如果已添加到PATH
    ]
    
    for path in possible_paths:
        if os.path.exists(path) or path == 'tesseract':
            pytesseract.pytesseract.tesseract_cmd = path
            print(f"使用Tesseract路径: {path}")
            return True
    
    print("错误: 未找到Tesseract OCR。请安装Tesseract OCR。")
    print("下载地址: https://github.com/UB-Mannheim/tesseract/wiki")
    return False

def preprocess_image(image_path):
    """预处理图片以提高OCR识别率"""
    # 读取图片
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")
    
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 应用高斯模糊去噪
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 应用阈值处理，增强对比度
    _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 形态学操作，去除噪点
    kernel = np.ones((1, 1), np.uint8)
    processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    
    return processed

def extract_text_from_image(image_path):
    """从图片中提取文字"""
    try:
        # 预处理图片
        processed_image = preprocess_image(image_path)
        
        # 使用PIL转换
        pil_image = Image.fromarray(processed_image)
        
        # OCR配置 - 针对中文优化
        custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
        
        # 提取文字
        text = pytesseract.image_to_string(pil_image, config=custom_config)
        
        return text
    except Exception as e:
        print(f"OCR识别出错: {e}")
        return ""

def parse_table_text(text):
    """解析OCR识别的文字，转换为表格数据"""
    lines = text.strip().split('\n')
    
    # 过滤空行和只包含空格的行
    lines = [line.strip() for line in lines if line.strip()]
    
    if not lines:
        return []
    
    # 尝试识别表格结构
    table_data = []
    
    for line in lines:
        # 使用多种分隔符尝试分割
        # 先尝试空格分割
        cells = re.split(r'\s{2,}', line)  # 两个或更多空格作为分隔符
        
        if len(cells) == 1:
            # 如果空格分割失败，尝试其他分隔符
            for separator in ['|', '\t', '，', ',']:
                test_cells = line.split(separator)
                if len(test_cells) > len(cells):
                    cells = test_cells
        
        # 清理单元格内容
        cells = [cell.strip() for cell in cells if cell.strip()]
        
        if cells:  # 只添加非空行
            table_data.append(cells)
    
    return table_data

def normalize_table_data(table_data):
    """标准化表格数据，确保所有行有相同的列数"""
    if not table_data:
        return []
    
    # 找到最大列数
    max_cols = max(len(row) for row in table_data)
    
    # 补齐所有行到相同列数
    normalized_data = []
    for row in table_data:
        normalized_row = row + [''] * (max_cols - len(row))
        normalized_data.append(normalized_row)
    
    return normalized_data

def create_excel_file(table_data, output_path):
    """创建Excel文件"""
    if not table_data:
        print("没有数据可写入Excel")
        return False
    
    # 创建DataFrame
    df = pd.DataFrame(table_data)
    
    # 创建Excel工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "图片数据"
    
    # 写入数据
    for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=False), 1):
        for c_idx, value in enumerate(row, 1):
            cell = ws.cell(row=r_idx, column=c_idx, value=value)
            
            # 设置字体
            cell.font = Font(name='宋体', size=10)
            
            # 设置对齐
            cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # 设置边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            cell.border = thin_border
    
    # 自动调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        
        adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # 保存文件
    wb.save(output_path)
    return True

def main():
    """主函数"""
    print("=== 图片转Excel工具 ===")
    
    # 检查Tesseract是否可用
    if not setup_tesseract():
        return
    
    # 获取图片文件路径
    image_path = input("请输入图片文件路径（或直接拖拽图片到此处）: ").strip().strip('"')
    
    if not os.path.exists(image_path):
        print(f"错误: 图片文件不存在: {image_path}")
        return
    
    # 检查文件格式
    valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    if not any(image_path.lower().endswith(ext) for ext in valid_extensions):
        print(f"错误: 不支持的图片格式。支持的格式: {', '.join(valid_extensions)}")
        return
    
    print(f"正在处理图片: {image_path}")
    
    # 提取文字
    print("正在进行OCR识别...")
    text = extract_text_from_image(image_path)
    
    if not text.strip():
        print("错误: 未能从图片中识别出文字")
        return
    
    print("OCR识别完成，正在解析表格结构...")
    
    # 解析表格数据
    table_data = parse_table_text(text)
    
    if not table_data:
        print("错误: 未能解析出表格数据")
        return
    
    # 标准化数据
    table_data = normalize_table_data(table_data)
    
    print(f"解析完成，共识别出 {len(table_data)} 行数据")
    
    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(image_path))[0]
    output_path = f"{base_name}_转换结果.xlsx"
    
    # 创建Excel文件
    print(f"正在创建Excel文件: {output_path}")
    
    if create_excel_file(table_data, output_path):
        print(f"转换完成！Excel文件已保存为: {output_path}")
        
        # 显示预览
        print("\n=== 数据预览 ===")
        for i, row in enumerate(table_data[:5]):  # 只显示前5行
            print(f"第{i+1}行: {row}")
        
        if len(table_data) > 5:
            print(f"... 还有 {len(table_data) - 5} 行数据")
    else:
        print("错误: Excel文件创建失败")

if __name__ == "__main__":
    main()
