#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
依赖包安装脚本
自动安装图片转Excel工具所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_tesseract():
    """检查Tesseract是否已安装"""
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Tesseract OCR 已安装")
            print(f"版本信息: {result.stdout.split()[1]}")
            return True
    except FileNotFoundError:
        pass
    
    print("✗ Tesseract OCR 未安装")
    return False

def main():
    """主函数"""
    print("=== 图片转Excel工具依赖安装 ===")
    print()
    
    # 需要安装的包
    packages = [
        'pandas>=1.3.0',
        'openpyxl>=3.0.0',
        'pillow>=8.0.0',
        'pytesseract>=0.3.8',
        'opencv-python>=4.5.0',
        'numpy>=1.20.0'
    ]
    
    print("正在安装Python依赖包...")
    
    success_count = 0
    for package in packages:
        print(f"安装 {package}...", end=' ')
        if install_package(package):
            print("✓")
            success_count += 1
        else:
            print("✗")
    
    print(f"\nPython包安装完成: {success_count}/{len(packages)} 成功")
    
    # 检查Tesseract
    print("\n检查Tesseract OCR...")
    if not check_tesseract():
        print("\n需要手动安装Tesseract OCR:")
        print("Windows: https://github.com/UB-Mannheim/tesseract/wiki")
        print("Linux: sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim")
        print("macOS: brew install tesseract tesseract-lang")
        print("\n安装完成后请重新运行此脚本验证")
    
    print("\n安装完成！")
    
    if success_count == len(packages) and check_tesseract():
        print("所有依赖已就绪，可以使用完整版OCR功能")
    else:
        print("部分依赖缺失，建议使用简化版工具")

if __name__ == "__main__":
    main()
