#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

def test_file_paths():
    """测试文件路径设置"""
    
    # 模拟脚本中的路径设置
    script_dir = os.path.dirname(os.path.abspath(__file__))
    inspection_report_path = os.path.join(script_dir, "检验报告.xlsx")
    production_task_path = os.path.join(script_dir, "生产环节任务专项.xlsx")
    
    print("=== 文件路径测试 ===")
    print(f"脚本所在目录: {script_dir}")
    print(f"检验报告路径: {inspection_report_path}")
    print(f"生产环节任务专项路径: {production_task_path}")
    
    print("\n=== 文件存在性检查 ===")
    print(f"检验报告文件存在: {os.path.exists(inspection_report_path)}")
    print(f"生产环节任务专项文件存在: {os.path.exists(production_task_path)}")
    
    # 输出路径测试
    from datetime import datetime
    output_filename = f"2025年省抽任务分析结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    output_path = os.path.join(script_dir, output_filename)
    
    print(f"\n=== 输出路径测试 ===")
    print(f"输出文件路径: {output_path}")
    print(f"输出目录存在: {os.path.exists(script_dir)}")
    print(f"输出目录可写: {os.access(script_dir, os.W_OK)}")

if __name__ == "__main__":
    test_file_paths()
