#!/bin/bash

# 修复Alpine Linux网络和包管理问题
# 解决Docker容器中apk包管理器无法连接仓库的问题

set -e

echo "=== 修复Alpine Linux网络问题 ==="

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-multi-transport" "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo "错误：找不到MCP部署目录"
    exit 1
fi

echo "使用工作目录: $WORK_DIR"
cd "$WORK_DIR"

echo "1. 停止现有服务..."
docker compose down

echo "2. 创建修复后的Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    dns:
      - *******
      - *******
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        echo "配置DNS和网络..."
        echo "nameserver *******" > /etc/resolv.conf
        echo "nameserver *******" >> /etc/resolv.conf
        
        echo "更新包索引..."
        apk update --no-cache || {
          echo "使用国内镜像源..."
          echo "https://mirrors.aliyun.com/alpine/v3.18/main" > /etc/apk/repositories
          echo "https://mirrors.aliyun.com/alpine/v3.18/community" >> /etc/apk/repositories
          apk update --no-cache
        }
        
        echo "安装系统依赖..."
        apk add --no-cache \
          cairo-dev \
          pango-dev \
          jpeg-dev \
          giflib-dev \
          librsvg-dev \
          pixman-dev \
          pkgconfig \
          python3 \
          make \
          g++ \
          curl \
          ca-certificates
        
        echo "安装npm依赖..."
        npm config set registry https://registry.npmmirror.com
        npm install
        
        echo "启动渲染服务..."
        npm start
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # SSE传输方式
  mcp-sse-server:
    image: node:18-alpine
    container_name: mcp-sse-server
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    dns:
      - *******
      - *******
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "配置DNS..."
        echo "nameserver *******" > /etc/resolv.conf
        echo "nameserver *******" >> /etc/resolv.conf
        
        echo "更新包索引..."
        apk update --no-cache || {
          echo "使用国内镜像源..."
          echo "https://mirrors.aliyun.com/alpine/v3.18/main" > /etc/apk/repositories
          echo "https://mirrors.aliyun.com/alpine/v3.18/community" >> /etc/apk/repositories
          apk update --no-cache
        }
        
        echo "安装系统工具..."
        apk add --no-cache curl ca-certificates
        
        echo "配置npm镜像..."
        npm config set registry https://registry.npmmirror.com
        
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        
        echo "等待渲染服务启动..."
        for i in {1..60}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... ($i/60)"
          sleep 2
        done
        
        echo "测试渲染服务连接..."
        curl -s http://gpt-vis-ssr:3000/health || echo "渲染服务连接测试失败"
        
        echo "启动MCP SSE服务器..."
        mcp-server-chart --transport sse --port 1122
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

  # Streamable传输方式
  mcp-streamable-server:
    image: node:18-alpine
    container_name: mcp-streamable-server
    working_dir: /app
    ports:
      - "1123:1123"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    dns:
      - *******
      - *******
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "配置DNS..."
        echo "nameserver *******" > /etc/resolv.conf
        echo "nameserver *******" >> /etc/resolv.conf
        
        echo "更新包索引..."
        apk update --no-cache || {
          echo "使用国内镜像源..."
          echo "https://mirrors.aliyun.com/alpine/v3.18/main" > /etc/apk/repositories
          echo "https://mirrors.aliyun.com/alpine/v3.18/community" >> /etc/apk/repositories
          apk update --no-cache
        }
        
        echo "安装系统工具..."
        apk add --no-cache curl ca-certificates
        
        echo "配置npm镜像..."
        npm config set registry https://registry.npmmirror.com
        
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        
        echo "等待渲染服务启动..."
        for i in {1..60}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... ($i/60)"
          sleep 2
        done
        
        echo "启动MCP Streamable服务器..."
        mcp-server-chart --transport streamable --port 1123
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

echo "3. 确保渲染服务目录和文件存在..."
if [ ! -d "render-service" ]; then
    mkdir -p render-service
fi

cd render-service

# 创建优化的package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0",
    "cors": "^2.8.5"
  }
}
EOF

# 创建优化的服务器代码
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();

// 启用CORS
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// 添加请求日志
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
  next();
});

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    console.log('开始渲染图表...');
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename, '大小:', buffer.length, 'bytes');
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr',
    uptime: process.uptime()
  });
});

app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log(`API端点: http://localhost:${PORT}/api/gpt-vis`);
  console.log(`启动时间: ${new Date().toISOString()}`);
});
EOF

cd ..

echo "4. 确保图片目录存在..."
mkdir -p images

echo "5. 清理Docker缓存..."
docker system prune -f

echo "6. 启动服务..."
docker compose up -d

echo "7. 监控启动过程..."
echo "查看渲染服务启动日志..."
timeout 120 docker compose logs -f gpt-vis-ssr &
LOGS_PID=$!

# 等待服务启动
sleep 60

# 停止日志监控
kill $LOGS_PID 2>/dev/null || true

echo ""
echo "8. 检查服务状态..."
docker compose ps

echo ""
echo "9. 测试服务..."
for i in {1..10}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务启动成功"
        curl -s http://localhost:3000/health | jq . 2>/dev/null || curl -s http://localhost:3000/health
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/10)"
        sleep 10
    fi
done

echo ""
echo "=== 修复完成 ==="
echo "如果问题仍然存在，请检查："
echo "1. 网络连接: ping *******"
echo "2. DNS解析: nslookup dl-cdn.alpinelinux.org"
echo "3. Docker网络: docker network ls"
echo "4. 查看详细日志: docker compose logs gpt-vis-ssr"
