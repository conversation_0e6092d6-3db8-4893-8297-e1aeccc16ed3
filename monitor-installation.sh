#!/bin/bash

# 监控Docker容器安装进度
# 提供实时状态更新和进度信息

echo "=== 监控MCP服务安装进度 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-prebuilt-images" "/opt/mcp-multi-transport" "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo -e "${RED}错误：找不到MCP部署目录${NC}"
    exit 1
fi

echo -e "${BLUE}使用工作目录: $WORK_DIR${NC}"
cd "$WORK_DIR"

echo ""
echo -e "${YELLOW}📊 当前容器状态:${NC}"
docker compose ps

echo ""
echo -e "${YELLOW}🔍 监控安装进度...${NC}"
echo "注意：npm警告信息是正常的，不会影响功能"
echo ""

# 监控函数
monitor_container() {
    local container_name="$1"
    local service_name="$2"
    local port="$3"
    
    echo -e "${BLUE}监控 $service_name ($container_name)...${NC}"
    
    # 检查容器是否存在
    if ! docker ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
        echo -e "${RED}❌ 容器 $container_name 不存在${NC}"
        return 1
    fi
    
    # 检查容器状态
    local status=$(docker inspect --format='{{.State.Status}}' $container_name 2>/dev/null)
    echo "容器状态: $status"
    
    if [ "$status" = "running" ]; then
        echo -e "${GREEN}✅ 容器正在运行${NC}"
        
        # 检查服务是否响应
        if [ -n "$port" ]; then
            if curl -s --connect-timeout 2 http://localhost:$port/health > /dev/null 2>&1; then
                echo -e "${GREEN}✅ 服务响应正常 (端口 $port)${NC}"
                return 0
            else
                echo -e "${YELLOW}⏳ 服务还未就绪 (端口 $port)${NC}"
            fi
        fi
    elif [ "$status" = "exited" ]; then
        echo -e "${RED}❌ 容器已退出${NC}"
        echo "最近日志:"
        docker logs --tail=5 $container_name
        return 1
    else
        echo -e "${YELLOW}⏳ 容器状态: $status${NC}"
    fi
    
    return 2
}

# 显示实时日志
show_logs() {
    local container_name="$1"
    local lines="$2"
    
    echo -e "${BLUE}📋 $container_name 最新日志 (最近 $lines 行):${NC}"
    docker logs --tail=$lines $container_name 2>/dev/null | tail -$lines
    echo ""
}

# 主监控循环
echo "开始监控安装进度..."
echo "按 Ctrl+C 停止监控"
echo ""

start_time=$(date +%s)
max_wait_time=600  # 10分钟超时

while true; do
    current_time=$(date +%s)
    elapsed=$((current_time - start_time))
    
    echo -e "${YELLOW}⏰ 已等待: ${elapsed}秒 / ${max_wait_time}秒${NC}"
    echo ""
    
    # 监控渲染服务
    echo -e "${BLUE}🎨 检查渲染服务...${NC}"
    monitor_container "gpt-vis-ssr" "渲染服务" "3000"
    render_status=$?
    
    if [ $render_status -eq 0 ]; then
        echo -e "${GREEN}✅ 渲染服务已就绪${NC}"
        
        # 测试图表生成
        echo "测试图表生成功能..."
        test_response=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
          -H "Content-Type: application/json" \
          -d '{"type": "pie", "data": [{"category": "测试", "value": 100}]}' \
          --connect-timeout 5 --max-time 10 2>/dev/null || echo "")
        
        if echo "$test_response" | grep -q '"success":true'; then
            echo -e "${GREEN}✅ 图表生成功能正常${NC}"
        else
            echo -e "${YELLOW}⚠️ 图表生成功能还未就绪${NC}"
        fi
        
        # 检查MCP服务
        echo ""
        echo -e "${BLUE}🔗 检查MCP SSE服务...${NC}"
        monitor_container "mcp-sse-server" "MCP SSE服务" "1122"
        sse_status=$?
        
        echo ""
        echo -e "${BLUE}🚀 检查MCP Streamable服务...${NC}"
        monitor_container "mcp-streamable-server" "MCP Streamable服务" "1123"
        streamable_status=$?
        
        # 如果所有服务都就绪
        if [ $sse_status -eq 0 ] && [ $streamable_status -eq 0 ]; then
            echo ""
            echo -e "${GREEN}🎉 所有服务已成功启动！${NC}"
            echo ""
            echo -e "${GREEN}📍 服务地址:${NC}"
            echo "  - 渲染服务: http://localhost:3000"
            echo "  - MCP SSE: http://localhost:1122/sse"
            echo "  - MCP Streamable: http://localhost:1123/mcp"
            echo ""
            echo -e "${GREEN}🔧 Cherry Studio配置:${NC}"
            echo "  SSE URL: http://localhost:1122/sse"
            break
        fi
    elif [ $render_status -eq 1 ]; then
        echo -e "${RED}❌ 渲染服务启动失败${NC}"
        show_logs "gpt-vis-ssr" 10
        break
    else
        # 显示安装进度
        show_logs "gpt-vis-ssr" 3
    fi
    
    # 超时检查
    if [ $elapsed -gt $max_wait_time ]; then
        echo -e "${RED}⏰ 安装超时 (${max_wait_time}秒)${NC}"
        echo "可能的原因："
        echo "1. 网络连接缓慢"
        echo "2. 依赖包下载失败"
        echo "3. 系统资源不足"
        echo ""
        echo "建议操作："
        echo "1. 检查网络连接"
        echo "2. 重新运行部署脚本"
        echo "3. 查看详细日志: docker compose logs gpt-vis-ssr"
        break
    fi
    
    echo "----------------------------------------"
    sleep 10
done

echo ""
echo -e "${BLUE}📊 最终状态检查:${NC}"
docker compose ps

echo ""
echo -e "${BLUE}💡 有用的命令:${NC}"
echo "查看实时日志: docker compose logs -f gpt-vis-ssr"
echo "重启服务: docker compose restart"
echo "停止服务: docker compose down"
echo "测试服务: sudo ./test-all-transports.sh"
