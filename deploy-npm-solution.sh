#!/bin/bash

# 使用npm包部署MCP解决方案
# 避免源码构建问题，直接使用已发布的npm包

set -e

echo "=== 使用NPM包部署MCP解决方案 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 安装Node.js（如果没有安装）
if ! command -v node &> /dev/null; then
    echo "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    apt-get install -y nodejs
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

APP_DIR="/opt/mcp-npm-solution"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# 停止现有服务
echo "停止现有Docker服务..."
cd /opt/mcp-server-chart 2>/dev/null && docker compose down 2>/dev/null || true
cd /opt/mcp-prebuilt 2>/dev/null && docker compose down 2>/dev/null || true
docker stop mcp-all-in-one 2>/dev/null || true
docker rm mcp-all-in-one 2>/dev/null || true

cd $APP_DIR

# 全局安装mcp-server-chart
echo "全局安装mcp-server-chart..."
npm install -g @antv/mcp-server-chart

# 创建渲染服务目录
echo "创建渲染服务..."
mkdir -p render-service
cd render-service

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0"
  }
}
EOF

# 创建服务器代码
cat > server.js << 'EOF'
const express = require('express');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();
app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
});
EOF

cd $APP_DIR

# 创建Docker Compose配置
echo "创建Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++
        npm install
        npm start
    restart: unless-stopped
    networks:
      - mcp-network

  mcp-chart-server:
    image: node:18-alpine
    container_name: mcp-chart-server
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "MCP服务启动中..."
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "检查安装结果..."
        which mcp-server-chart || echo "命令行工具未找到"
        npm list -g @antv/mcp-server-chart || echo "包未找到"
        echo "尝试直接运行..."
        # 尝试不同的启动方式
        if command -v mcp-server-chart >/dev/null 2>&1; then
          echo "使用命令行工具启动..."
          mcp-server-chart --transport sse --port 1122
        else
          echo "使用npx启动..."
          npx @antv/mcp-server-chart --transport sse --port 1122
        fi
    depends_on:
      - gpt-vis-ssr
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
EOF

# 创建图片目录
mkdir -p images

echo "启动Docker服务..."
docker compose up -d

echo "等待服务启动..."
sleep 60

echo "检查服务状态..."
docker compose ps

echo "查看服务日志..."
echo "--- 渲染服务日志 ---"
docker compose logs --tail=10 gpt-vis-ssr

echo ""
echo "--- MCP服务日志 ---"
docker compose logs --tail=10 mcp-chart-server

echo ""
echo "=== 测试服务 ==="
echo "测试渲染服务..."
for i in {1..5}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务响应正常"
        curl -s http://localhost:3000/health | jq . 2>/dev/null || curl -s http://localhost:3000/health
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "测试MCP服务..."
for i in {1..5}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✅ MCP服务响应正常"
        break
    else
        echo "⏳ 等待MCP服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "=== 部署完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - 渲染服务健康检查: http://localhost:3000/health"
echo "  - MCP服务: http://localhost:1122/sse"
echo ""
echo "管理命令:"
echo "  查看日志: docker compose logs -f"
echo "  重启服务: docker compose restart"
echo "  停止服务: docker compose down"
echo ""
echo "如果服务仍有问题，请查看日志："
echo "  docker compose logs -f mcp-chart-server"
echo "  docker compose logs -f gpt-vis-ssr"
