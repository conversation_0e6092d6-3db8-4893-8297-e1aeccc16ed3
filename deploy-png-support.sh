#!/bin/bash

# 部署支持PNG图片返回的MCP图表服务
# 支持多种返回格式：PNG图片、URL链接、base64编码

set -e

echo "=== 部署支持PNG返回的MCP图表服务 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 安装Node.js（如果没有安装）
if ! command -v node &> /dev/null; then
    echo "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    apt-get install -y nodejs
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

APP_DIR="/opt/mcp-png-support"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# 停止现有服务
echo "停止现有Docker服务..."
for dir in "/opt/mcp-server-chart" "/opt/mcp-prebuilt" "/opt/mcp-npm-solution" "/opt/mcp-multi-transport"; do
    if [ -d "$dir" ]; then
        cd "$dir" 2>/dev/null && docker compose down 2>/dev/null || true
    fi
done

cd $APP_DIR

# 创建渲染服务目录
echo "创建渲染服务..."
mkdir -p render-service
cd render-service

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-png-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service with PNG support",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0",
    "cors": "^2.8.5",
    "multer": "^1.4.5-lts.1"
  }
}
EOF

# 创建支持PNG返回的服务器代码
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();

// 启用CORS
app.use(cors());
app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

// 主要的图表生成API
app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    // 检查请求参数，决定返回格式
    const returnFormat = req.query.format || req.body.format || 'auto';
    const acceptHeader = req.headers.accept || '';
    
    console.log('返回格式:', returnFormat, '接受头:', acceptHeader);
    
    if (returnFormat === 'png' || acceptHeader.includes('image/png')) {
      // 直接返回PNG图片
      console.log('返回PNG图片格式');
      res.setHeader('Content-Type', 'image/png');
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
      res.setHeader('Cache-Control', 'public, max-age=3600');
      res.send(buffer);
    } else if (returnFormat === 'url') {
      // 返回图片URL
      const imageUrl = `http://localhost:3000/images/${filename}`;
      console.log('返回URL格式:', imageUrl);
      res.json({
        success: true,
        resultObj: imageUrl,
        filename: filename,
        type: 'url'
      });
    } else if (returnFormat === 'file') {
      // 返回文件路径
      console.log('返回文件路径格式');
      res.json({
        success: true,
        resultObj: filepath,
        filename: filename,
        type: 'file'
      });
    } else {
      // 默认返回base64编码（保持兼容性）
      const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
      console.log('返回base64格式');
      res.json({
        success: true,
        resultObj: imageUrl,
        filename: filename,
        type: 'base64'
      });
    }
    
    console.log('图表生成成功:', filename);
    
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

// PNG图片专用API
app.post('/api/gpt-vis/png', async (req, res) => {
  try {
    console.log('收到PNG渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    // 直接返回PNG图片
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
    res.setHeader('Cache-Control', 'public, max-age=3600');
    res.send(buffer);
    
    console.log('PNG图表生成成功:', filename);
    
  } catch (error) {
    console.error('PNG渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

// URL图片专用API
app.post('/api/gpt-vis/url', async (req, res) => {
  try {
    console.log('收到URL渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    // 返回图片URL
    const imageUrl = `http://localhost:3000/images/${filename}`;
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename,
      type: 'url'
    });
    
    console.log('URL图表生成成功:', filename, imageUrl);
    
  } catch (error) {
    console.error('URL渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr-png',
    formats: ['png', 'url', 'base64', 'file']
  });
});

// 静态文件服务
app.use('/images', express.static(imageDir));

// API文档
app.get('/api/docs', (req, res) => {
  res.json({
    endpoints: {
      '/api/gpt-vis': {
        method: 'POST',
        description: '通用图表生成API',
        parameters: {
          format: 'png|url|base64|file - 返回格式'
        }
      },
      '/api/gpt-vis/png': {
        method: 'POST',
        description: '直接返回PNG图片'
      },
      '/api/gpt-vis/url': {
        method: 'POST',
        description: '返回图片URL链接'
      }
    },
    examples: {
      png: 'curl -X POST http://localhost:3000/api/gpt-vis/png -H "Content-Type: application/json" -d \'{"type":"pie","data":[{"category":"A","value":30}]}\'',
      url: 'curl -X POST http://localhost:3000/api/gpt-vis/url -H "Content-Type: application/json" -d \'{"type":"pie","data":[{"category":"A","value":30}]}\''
    }
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR PNG服务运行在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log(`API文档: http://localhost:${PORT}/api/docs`);
  console.log(`通用API: http://localhost:${PORT}/api/gpt-vis`);
  console.log(`PNG API: http://localhost:${PORT}/api/gpt-vis/png`);
  console.log(`URL API: http://localhost:${PORT}/api/gpt-vis/url`);
});
EOF

cd $APP_DIR

# 创建Docker Compose配置
echo "创建Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr-png:
    image: node:18-alpine
    container_name: gpt-vis-ssr-png
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        echo "安装系统依赖..."
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++ curl
        echo "安装npm依赖..."
        npm install
        echo "启动PNG支持的渲染服务..."
        npm start
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # SSE传输方式
  mcp-sse-server:
    image: node:18-alpine
    container_name: mcp-sse-server-png
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr-png:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..30}; do
          if curl -s http://gpt-vis-ssr-png:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... ($i/30)"
          sleep 2
        done
        echo "启动MCP SSE服务器..."
        mcp-server-chart --transport sse --port 1122
    depends_on:
      gpt-vis-ssr-png:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

# 创建图片目录
mkdir -p images

echo "启动Docker服务..."
docker compose up -d

echo "等待服务启动..."
sleep 60

echo "检查服务状态..."
docker compose ps

echo ""
echo "=== 测试PNG支持 ==="

# 测试PNG直接返回
echo "1. 测试PNG直接返回..."
PNG_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/test_chart.png -X POST http://localhost:3000/api/gpt-vis/png \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "PNG测试", "value": 60},
      {"category": "其他", "value": 40}
    ]
  }')

if [ "$PNG_RESPONSE" = "200" ]; then
    echo "✅ PNG直接返回测试成功"
    echo "   文件大小: $(wc -c < /tmp/test_chart.png) 字节"
else
    echo "❌ PNG直接返回测试失败，HTTP状态码: $PNG_RESPONSE"
fi

# 测试URL返回
echo ""
echo "2. 测试URL返回..."
URL_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis/url \
  -H "Content-Type: application/json" \
  -d '{
    "type": "column",
    "data": [
      {"category": "URL测试", "value": 100}
    ]
  }')

if echo "$URL_RESPONSE" | grep -q '"type":"url"'; then
    echo "✅ URL返回测试成功"
    IMAGE_URL=$(echo "$URL_RESPONSE" | grep -o '"resultObj":"[^"]*"' | cut -d'"' -f4)
    echo "   图片URL: $IMAGE_URL"
else
    echo "❌ URL返回测试失败"
fi

echo ""
echo "=== 部署完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - PNG API: http://localhost:3000/api/gpt-vis/png"
echo "  - URL API: http://localhost:3000/api/gpt-vis/url"
echo "  - 通用API: http://localhost:3000/api/gpt-vis"
echo "  - API文档: http://localhost:3000/api/docs"
echo "  - MCP SSE服务: http://localhost:1122/sse"
echo ""
echo "使用示例:"
echo "  PNG格式: curl -X POST http://localhost:3000/api/gpt-vis/png -H 'Content-Type: application/json' -d '{\"type\":\"pie\",\"data\":[{\"category\":\"测试\",\"value\":100}]}' > chart.png"
echo "  URL格式: curl -X POST http://localhost:3000/api/gpt-vis/url -H 'Content-Type: application/json' -d '{\"type\":\"pie\",\"data\":[{\"category\":\"测试\",\"value\":100}]}'"
echo ""
echo "Cherry Studio配置: http://localhost:1122/sse"
