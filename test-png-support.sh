#!/bin/bash

# 测试PNG图片返回功能
# 验证各种返回格式是否正常工作

echo "=== 测试PNG图片返回功能 ==="

# 创建测试目录
mkdir -p /tmp/mcp-png-test
cd /tmp/mcp-png-test

# 测试渲染服务健康状态
echo "1. 测试渲染服务健康状态..."
HEALTH_RESPONSE=$(curl -s http://localhost:3000/health)
if echo "$HEALTH_RESPONSE" | grep -q '"status":"ok"'; then
    echo "✅ 渲染服务正常"
    echo "   支持格式: $(echo "$HEALTH_RESPONSE" | grep -o '"formats":\[[^]]*\]')"
else
    echo "❌ 渲染服务异常"
    echo "   响应: $HEALTH_RESPONSE"
    exit 1
fi

# 测试PNG直接返回
echo ""
echo "2. 测试PNG直接返回..."
PNG_STATUS=$(curl -s -w "%{http_code}" -o test_pie_chart.png -X POST http://localhost:3000/api/gpt-vis/png \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "苹果", "value": 30},
      {"category": "香蕉", "value": 25},
      {"category": "橙子", "value": 20},
      {"category": "葡萄", "value": 15},
      {"category": "其他", "value": 10}
    ],
    "title": "水果销量分布"
  }')

if [ "$PNG_STATUS" = "200" ] && [ -f "test_pie_chart.png" ]; then
    FILE_SIZE=$(wc -c < test_pie_chart.png)
    if [ "$FILE_SIZE" -gt 1000 ]; then
        echo "✅ PNG饼图生成成功"
        echo "   文件大小: $FILE_SIZE 字节"
        echo "   文件类型: $(file test_pie_chart.png)"
    else
        echo "❌ PNG文件太小，可能生成失败"
    fi
else
    echo "❌ PNG饼图生成失败，HTTP状态码: $PNG_STATUS"
fi

# 测试URL返回
echo ""
echo "3. 测试URL返回..."
URL_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis/url \
  -H "Content-Type: application/json" \
  -d '{
    "type": "column",
    "data": [
      {"category": "一月", "value": 100},
      {"category": "二月", "value": 120},
      {"category": "三月", "value": 90},
      {"category": "四月", "value": 150}
    ],
    "title": "月度销售额"
  }')

if echo "$URL_RESPONSE" | grep -q '"type":"url"'; then
    echo "✅ URL返回测试成功"
    IMAGE_URL=$(echo "$URL_RESPONSE" | grep -o '"resultObj":"[^"]*"' | cut -d'"' -f4)
    echo "   图片URL: $IMAGE_URL"
    
    # 测试URL是否可访问
    URL_STATUS=$(curl -s -w "%{http_code}" -o test_from_url.png "$IMAGE_URL")
    if [ "$URL_STATUS" = "200" ]; then
        echo "   ✅ URL可正常访问"
        echo "   下载文件大小: $(wc -c < test_from_url.png) 字节"
    else
        echo "   ❌ URL无法访问，状态码: $URL_STATUS"
    fi
else
    echo "❌ URL返回测试失败"
    echo "   响应: $URL_RESPONSE"
fi

# 测试通用API的format参数
echo ""
echo "4. 测试通用API的format参数..."

# 测试format=png
PNG_FORMAT_STATUS=$(curl -s -w "%{http_code}" -o test_format_png.png -X POST "http://localhost:3000/api/gpt-vis?format=png" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "line",
    "data": [
      {"time": "2024-01", "value": 100},
      {"time": "2024-02", "value": 120},
      {"time": "2024-03", "value": 90}
    ],
    "title": "趋势分析"
  }')

if [ "$PNG_FORMAT_STATUS" = "200" ]; then
    echo "✅ 通用API format=png 测试成功"
    echo "   文件大小: $(wc -c < test_format_png.png) 字节"
else
    echo "❌ 通用API format=png 测试失败"
fi

# 测试format=url
URL_FORMAT_RESPONSE=$(curl -s -X POST "http://localhost:3000/api/gpt-vis?format=url" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "scatter",
    "data": [
      {"x": 10, "y": 15},
      {"x": 20, "y": 25},
      {"x": 30, "y": 35}
    ],
    "title": "散点图测试"
  }')

if echo "$URL_FORMAT_RESPONSE" | grep -q '"type":"url"'; then
    echo "✅ 通用API format=url 测试成功"
else
    echo "❌ 通用API format=url 测试失败"
fi

# 测试默认base64返回
echo ""
echo "5. 测试默认base64返回..."
BASE64_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "bar",
    "data": [
      {"category": "产品A", "value": 80},
      {"category": "产品B", "value": 60}
    ],
    "title": "产品对比"
  }')

if echo "$BASE64_RESPONSE" | grep -q '"type":"base64"' && echo "$BASE64_RESPONSE" | grep -q 'data:image/png;base64,'; then
    echo "✅ 默认base64返回测试成功"
    BASE64_LENGTH=$(echo "$BASE64_RESPONSE" | grep -o 'data:image/png;base64,[^"]*' | wc -c)
    echo "   base64长度: $BASE64_LENGTH 字符"
else
    echo "❌ 默认base64返回测试失败"
fi

# 测试Accept头
echo ""
echo "6. 测试Accept头..."
ACCEPT_PNG_STATUS=$(curl -s -w "%{http_code}" -o test_accept_png.png -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -H "Accept: image/png" \
  -d '{
    "type": "radar",
    "data": [
      {"name": "性能", "value": 80},
      {"name": "价格", "value": 60},
      {"name": "质量", "value": 90}
    ],
    "title": "产品评估"
  }')

if [ "$ACCEPT_PNG_STATUS" = "200" ]; then
    echo "✅ Accept: image/png 测试成功"
    echo "   文件大小: $(wc -c < test_accept_png.png) 字节"
else
    echo "❌ Accept: image/png 测试失败"
fi

# 显示生成的文件
echo ""
echo "7. 生成的文件列表:"
ls -la *.png 2>/dev/null || echo "   没有PNG文件生成"

# 测试API文档
echo ""
echo "8. 测试API文档..."
API_DOCS=$(curl -s http://localhost:3000/api/docs)
if echo "$API_DOCS" | grep -q "endpoints"; then
    echo "✅ API文档可访问"
else
    echo "❌ API文档不可访问"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📊 支持的返回格式:"
echo "  1. PNG图片 - 直接返回二进制PNG数据"
echo "  2. URL链接 - 返回可访问的图片URL"
echo "  3. base64编码 - 返回base64编码的图片数据"
echo "  4. 文件路径 - 返回服务器上的文件路径"
echo ""
echo "🔗 API端点:"
echo "  - PNG专用: http://localhost:3000/api/gpt-vis/png"
echo "  - URL专用: http://localhost:3000/api/gpt-vis/url"
echo "  - 通用API: http://localhost:3000/api/gpt-vis?format=png|url|base64"
echo "  - 文档: http://localhost:3000/api/docs"
echo ""
echo "💡 使用建议:"
echo "  - 如需直接显示图片，使用PNG格式"
echo "  - 如需在网页中引用，使用URL格式"
echo "  - 如需嵌入数据，使用base64格式"
echo ""
echo "🎯 Cherry Studio配置:"
echo "  服务器URL: http://localhost:1122/sse"
echo "  传输方式: SSE"
