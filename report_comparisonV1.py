#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
省抽任务分析脚本
用于比对检验报告和生产环节任务专项两个Excel文件中的批准文号/备案号
"""

import pandas as pd
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def parse_date_to_month(date_value):
    """
    将日期值转换为YYYY-MM格式的月份字符串
    """
    if pd.isna(date_value):
        return None

    try:
        if isinstance(date_value, (int, float)):
            # 处理整数格式的日期，如20250521
            date_str = str(int(date_value))
            if len(date_str) == 8:  # YYYYMMDD格式
                year = date_str[:4]
                month = date_str[4:6]
                return f"{year}-{month}"
        elif isinstance(date_value, str):
            # 尝试解析字符串日期
            date_obj = pd.to_datetime(date_value)
            return date_obj.strftime('%Y-%m')
        else:
            # 处理datetime对象
            return date_value.strftime('%Y-%m')
    except:
        pass

    return None

def find_approval_number_column(df):
    """
    查找包含批准文号或备案号的列
    """
    possible_names = ['批准文号', '备案号', '批准文号或备案号', '文号', '许可证号']
    
    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col
    
    # 如果没有找到，返回包含相关关键词的列
    for col in df.columns:
        col_str = str(col).strip().lower()
        if any(keyword in col_str for keyword in ['批准', '备案', '文号', '许可']):
            return col
    
    return None

def find_sampling_certificate_column(df):
    """
    查找抽样凭证编号列
    """
    possible_names = ['抽样凭证编号', '凭证编号', '抽样编号', '样品编号']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col

    # 如果没有找到，返回包含相关关键词的列
    for col in df.columns:
        col_str = str(col).strip().lower()
        if any(keyword in col_str for keyword in ['抽样', '凭证', '编号']):
            return col

    return None

def find_sampled_unit_column(df):
    """
    查找被抽样单位名称列
    """
    # 精确匹配被抽样单位名称
    for col in df.columns:
        col_str = str(col).strip()
        if col_str == '被抽样单位名称':
            return col

    # 其他可能的名称
    possible_names = ['被抽样单位', '抽样单位名称', '单位名称']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str and '统一社会信用代码' not in col_str:
                return col

    return None

def find_medical_institution_column(df):
    """
    查找医疗机构名称列
    """
    possible_names = ['医疗机构名称', '机构名称', '医疗机构', '医院名称']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col

    # 如果没有找到，返回包含相关关键词的列
    for col in df.columns:
        col_str = str(col).strip().lower()
        if any(keyword in col_str for keyword in ['医疗机构', '机构名称', '医院']):
            return col

    return None

def find_production_enterprise_column(df):
    """
    查找药品生产企业名称列
    """
    # 精确匹配药品生产企业名称
    for col in df.columns:
        col_str = str(col).strip()
        if col_str == '药品生产企业名称':
            return col

    # 其他可能的名称
    possible_names = ['生产企业名称', '生产企业', '企业名称']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str and '受托' not in col_str and '进口' not in col_str:
                return col

    return None

def find_enterprise_name_column(df):
    """
    查找企业名称列（用于生产企业sheet）
    """
    possible_names = ['企业名称', '生产企业名称', '公司名称', '厂家名称']

    for col in df.columns:
        col_str = str(col).strip()
        for name in possible_names:
            if name in col_str:
                return col

    return None

def main():
    # 文件路径
    inspection_report_path = r"c:\Users\<USER>\Documents\augment-projects\python\检验报告.xlsx"
    production_task_path = r"c:\Users\<USER>\Documents\augment-projects\python\生产环节任务专项.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(inspection_report_path):
        print(f"错误：找不到文件 {inspection_report_path}")
        return
    
    if not os.path.exists(production_task_path):
        print(f"错误：找不到文件 {production_task_path}")
        return
    
    try:
        # 读取检验报告
        print("正在读取检验报告...")
        inspection_df = pd.read_excel(inspection_report_path)
        print(f"检验报告包含 {len(inspection_df)} 行数据")
        print(f"检验报告列名: {list(inspection_df.columns)}")
        
        # 查找批准文号列、抽样凭证编号列、被抽样单位名称列和药品生产企业名称列
        approval_col = find_approval_number_column(inspection_df)
        sampling_col = find_sampling_certificate_column(inspection_df)
        sampled_unit_col = find_sampled_unit_column(inspection_df)
        production_enterprise_col = find_production_enterprise_column(inspection_df)

        if approval_col is None:
            print("错误：在检验报告中找不到批准文号或备案号列")
            print("请检查列名是否包含：批准文号、备案号、批准文号或备案号等关键词")
            return

        if sampling_col is None:
            print("错误：在检验报告中找不到抽样凭证编号列")
            print("请检查列名是否包含：抽样凭证编号、凭证编号、抽样编号等关键词")
            return

        if sampled_unit_col is None:
            print("错误：在检验报告中找不到被抽样单位名称列")
            print("请检查列名是否包含：被抽样单位名称、被抽样单位等关键词")
            return

        if production_enterprise_col is None:
            print("错误：在检验报告中找不到药品生产企业名称列")
            print("请检查列名是否包含：药品生产企业名称、生产企业名称等关键词")
            return

        print(f"找到批准文号列: {approval_col}")
        print(f"找到抽样凭证编号列: {sampling_col}")
        print(f"找到被抽样单位名称列: {sampled_unit_col}")
        print(f"找到药品生产企业名称列: {production_enterprise_col}")

        # 创建批准文号到抽样信息的映射（包含日期）
        approval_to_info = {}
        for _, row in inspection_df.iterrows():
            approval_num = str(row[approval_col]).strip() if pd.notna(row[approval_col]) else ""
            sampling_num = str(row[sampling_col]).strip() if pd.notna(row[sampling_col]) else ""
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            if approval_num and approval_num != "nan":
                approval_to_info[approval_num] = {
                    'sampling_num': sampling_num,
                    'month': month
                }

        print(f"建立了 {len(approval_to_info)} 个批准文号映射")

        # 创建被抽样单位名称到抽样信息的映射（包含日期）
        unit_to_info = {}
        for _, row in inspection_df.iterrows():
            unit_name = str(row[sampled_unit_col]).strip() if pd.notna(row[sampled_unit_col]) else ""
            sampling_num = str(row[sampling_col]).strip() if pd.notna(row[sampling_col]) else ""
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            if unit_name and unit_name != "nan":
                unit_to_info[unit_name] = {
                    'sampling_num': sampling_num,
                    'month': month
                }

        print(f"建立了 {len(unit_to_info)} 个被抽样单位映射")

        # 创建药品生产企业名称到抽样信息的映射（包含日期）
        enterprise_to_info = {}
        for _, row in inspection_df.iterrows():
            enterprise_name = str(row[production_enterprise_col]).strip() if pd.notna(row[production_enterprise_col]) else ""
            sampling_num = str(row[sampling_col]).strip() if pd.notna(row[sampling_col]) else ""
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            if enterprise_name and enterprise_name != "nan":
                enterprise_to_info[enterprise_name] = {
                    'sampling_num': sampling_num,
                    'month': month
                }

        print(f"建立了 {len(enterprise_to_info)} 个药品生产企业映射")
        
        # 读取生产环节任务专项文件的所有sheet
        print("正在读取生产环节任务专项文件...")
        production_sheets = pd.read_excel(production_task_path, sheet_name=None)
        
        print(f"生产环节任务专项文件包含 {len(production_sheets)} 个sheet:")
        for sheet_name in production_sheets.keys():
            print(f"  - {sheet_name}")
        
        # 处理每个sheet
        results = {}
        
        for sheet_name, df in production_sheets.items():
            print(f"\n处理sheet: {sheet_name}")
            print(f"该sheet包含 {len(df)} 行数据")
            print(f"该sheet列名: {list(df.columns)}")

            # 复制数据框
            result_df = df.copy()

            # 特殊处理生产企业sheet
            if sheet_name == "生产企业":
                # 查找企业名称列
                enterprise_col = find_enterprise_name_column(df)

                if enterprise_col is None:
                    print(f"  警告：在sheet '{sheet_name}' 中找不到企业名称列，跳过")
                    continue

                print(f"  找到企业名称列: {enterprise_col}")

                # 添加抽样凭证编号列（基于企业名称匹配）
                sampling_numbers = []
                completion_status = []
                months = []

                for _, row in result_df.iterrows():
                    enterprise_name = str(row[enterprise_col]).strip() if pd.notna(row[enterprise_col]) else ""

                    if enterprise_name and enterprise_name != "nan" and enterprise_name in enterprise_to_info:
                        info = enterprise_to_info[enterprise_name]
                        sampling_numbers.append(info['sampling_num'])
                        completion_status.append("已完成")
                        months.append(info['month'])
                    else:
                        sampling_numbers.append("")
                        completion_status.append("未完成")
                        months.append(None)

                result_df['抽样凭证编号'] = sampling_numbers
                result_df['完成情况'] = completion_status
                result_df['抽样月份'] = months

                # 统计完成情况
                completed_count = sum(1 for status in completion_status if status == "已完成")
                total_count = len(completion_status)

                print(f"  {sheet_name}: 已完成 {completed_count}/{total_count} ({completed_count/total_count*100:.1f}%)")

            # 特殊处理医疗机构sheet
            elif sheet_name == "医疗机构":
                # 查找医疗机构名称列
                medical_col = find_medical_institution_column(df)

                if medical_col is None:
                    print(f"  警告：在sheet '{sheet_name}' 中找不到医疗机构名称列，跳过")
                    continue

                print(f"  找到医疗机构名称列: {medical_col}")

                # 添加抽样凭证编号列（基于医疗机构名称匹配）
                sampling_numbers = []
                completion_status = []
                months = []

                for _, row in result_df.iterrows():
                    institution_name = str(row[medical_col]).strip() if pd.notna(row[medical_col]) else ""

                    if institution_name and institution_name != "nan" and institution_name in unit_to_info:
                        info = unit_to_info[institution_name]
                        sampling_numbers.append(info['sampling_num'])
                        completion_status.append("已完成")
                        months.append(info['month'])
                    else:
                        sampling_numbers.append("")
                        completion_status.append("未完成")
                        months.append(None)

                result_df['抽样凭证编号'] = sampling_numbers
                result_df['完成情况'] = completion_status
                result_df['抽样月份'] = months

                # 统计完成情况
                completed_count = sum(1 for status in completion_status if status == "已完成")
                total_count = len(completion_status)

                print(f"  {sheet_name}: 已完成 {completed_count}/{total_count} ({completed_count/total_count*100:.1f}%)")

            else:
                # 其他sheet按原来的逻辑处理（基于批准文号匹配）
                approval_col_prod = find_approval_number_column(df)

                if approval_col_prod is None:
                    print(f"  警告：在sheet '{sheet_name}' 中找不到批准文号相关列，跳过")
                    continue

                print(f"  找到批准文号列: {approval_col_prod}")

                # 添加抽样凭证编号列
                sampling_numbers = []
                completion_status = []
                months = []

                for _, row in result_df.iterrows():
                    approval_num = str(row[approval_col_prod]).strip() if pd.notna(row[approval_col_prod]) else ""

                    if approval_num and approval_num != "nan" and approval_num in approval_to_info:
                        info = approval_to_info[approval_num]
                        sampling_numbers.append(info['sampling_num'])
                        completion_status.append("已完成")
                        months.append(info['month'])
                    else:
                        sampling_numbers.append("")
                        completion_status.append("未完成")
                        months.append(None)

                result_df['抽样凭证编号'] = sampling_numbers
                result_df['完成情况'] = completion_status
                result_df['抽样月份'] = months

                # 统计完成情况
                completed_count = sum(1 for status in completion_status if status == "已完成")
                total_count = len(completion_status)

                print(f"  {sheet_name}: 已完成 {completed_count}/{total_count} ({completed_count/total_count*100:.1f}%)")

            results[sheet_name] = result_df
        
        # 创建汇总数据
        print("\n正在创建汇总统计...")

        # 获取所有可能的月份
        all_months = set()
        for _, row in inspection_df.iterrows():
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)
            if month:
                all_months.add(month)

        all_months = sorted(list(all_months))
        print(f"发现的月份: {all_months}")

        # 创建动态列名
        columns = ['任务名称', '总任务数']
        for month in all_months:
            columns.extend([f'{month}已完成数', f'{month}未完成数', f'{month}完成率(%)'])
        columns.append('匹配方式')

        summary_data = []
        total_completed = 0
        total_tasks = 0

        # 统计任务批数（基于抽样凭证编号）
        unique_sampling_certificates = set()
        monthly_batch_stats = {}

        for _, row in inspection_df.iterrows():
            sampling_num = str(row[sampling_col]).strip() if pd.notna(row[sampling_col]) else ""
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            if sampling_num and sampling_num != "nan":
                unique_sampling_certificates.add(sampling_num)
                if month:
                    if month not in monthly_batch_stats:
                        monthly_batch_stats[month] = 0
                    monthly_batch_stats[month] += 1

        batch_completed = len(unique_sampling_certificates)
        batch_total = 530  # 总任务数是530

        # 创建任务批数行（累计统计）
        batch_row = {'任务名称': '任务批数', '总任务数': batch_total}
        cumulative_completed = 0
        for month in all_months:
            completed_month = monthly_batch_stats.get(month, 0)
            cumulative_completed += completed_month  # 累计完成数
            uncompleted_month = 0  # 任务批数没有未完成的概念
            # 修正：完成率 = 累计完成数 / 总任务数
            completion_rate = (cumulative_completed / batch_total * 100) if batch_total > 0 else 0.0
            batch_row[f'{month}已完成数'] = cumulative_completed
            batch_row[f'{month}未完成数'] = uncompleted_month
            batch_row[f'{month}完成率(%)'] = round(completion_rate, 1)
        batch_row['匹配方式'] = '抽样凭证编号'
        summary_data.append(batch_row)

        for sheet_name, df in results.items():
            total = len(df)

            # 确定匹配方式
            if sheet_name == "医疗机构":
                match_type = "医疗机构名称"
            elif sheet_name == "生产企业":
                match_type = "企业名称"
            else:
                match_type = "批准文号"

            # 按月统计
            monthly_stats = {}
            for _, row in df.iterrows():
                month = row.get('抽样月份')
                status = row['完成情况']

                if month:
                    if month not in monthly_stats:
                        monthly_stats[month] = {'已完成': 0, '未完成': 0}
                    monthly_stats[month][status] += 1
                else:
                    # 未知月份的都算作未完成
                    if '未知' not in monthly_stats:
                        monthly_stats['未知'] = {'已完成': 0, '未完成': 0}
                    monthly_stats['未知'][status] += 1

            # 创建该sheet的行（累计统计）
            sheet_row = {'任务名称': sheet_name, '总任务数': total}
            cumulative_completed = 0
            for month in all_months:
                completed_month = monthly_stats.get(month, {}).get('已完成', 0)
                cumulative_completed += completed_month  # 累计完成数
                uncompleted_month = monthly_stats.get(month, {}).get('未完成', 0)
                # 修正：完成率 = 累计完成数 / 总任务数
                completion_rate = (cumulative_completed / total * 100) if total > 0 else 0.0

                sheet_row[f'{month}已完成数'] = cumulative_completed
                sheet_row[f'{month}未完成数'] = uncompleted_month
                sheet_row[f'{month}完成率(%)'] = round(completion_rate, 1)

            sheet_row['匹配方式'] = match_type
            summary_data.append(sheet_row)

            completed = len(df[df['完成情况'] == '已完成'])
            total_completed += completed
            total_tasks += total

        # 统计购样金额（基于药品总价）并按月分组
        total_amount = 0
        monthly_amounts = {}

        for _, row in inspection_df.iterrows():
            amount = row["药品总价（元）"] if pd.notna(row["药品总价（元）"]) else 0
            sampling_date = row["抽样日期"] if pd.notna(row["抽样日期"]) else None
            month = parse_date_to_month(sampling_date)

            try:
                amount_float = float(amount)
                total_amount += amount_float

                if month:
                    if month not in monthly_amounts:
                        monthly_amounts[month] = 0
                    monthly_amounts[month] += amount_float
            except (ValueError, TypeError):
                pass

        # 创建购样金额行（累计金额，不统计总任务数、未完成数、完成率）
        amount_row = {'任务名称': '购样金额', '总任务数': "-"}  # 不统计总任务数
        cumulative_amount = 0
        for month in all_months:
            month_amount = monthly_amounts.get(month, 0)
            cumulative_amount += month_amount  # 累计金额
            amount_row[f'{month}已完成数'] = f"{cumulative_amount:.2f}元"
            amount_row[f'{month}未完成数'] = "-"  # 不统计
            amount_row[f'{month}完成率(%)'] = "-"  # 不统计
        amount_row['匹配方式'] = '药品总价统计'
        summary_data.append(amount_row)

        # 创建汇总DataFrame
        summary_df = pd.DataFrame(summary_data)

        # 导出结果
        output_filename = f"2025年省抽任务分析结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        output_path = os.path.join(os.path.dirname(production_task_path), output_filename)

        print(f"\n正在导出结果到: {output_path}")

        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 首先写入汇总sheet
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

            # 然后写入其他sheet
            for sheet_name, df in results.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 设置格式
            from openpyxl.styles import Alignment

            # 设置汇总统计sheet的格式
            summary_ws = writer.sheets['汇总统计']

            # 设置表头自动换行和居中对齐
            for cell in summary_ws[1]:  # 第一行是表头
                cell.alignment = Alignment(wrap_text=True, horizontal='center', vertical='center')

            # 调整列宽
            for column in summary_ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 20)  # 最大宽度20
                summary_ws.column_dimensions[column_letter].width = adjusted_width

            # 设置其他sheet的格式
            for sheet_name in results.keys():
                ws = writer.sheets[sheet_name]

                # 设置表头自动换行和居中对齐
                for cell in ws[1]:  # 第一行是表头
                    cell.alignment = Alignment(wrap_text=True, horizontal='center', vertical='center')

                # 调整列宽
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 25)  # 最大宽度25
                    ws.column_dimensions[column_letter].width = adjusted_width

        print("导出完成！")

        # 打印总体统计
        print("\n=== 总体统计 ===")
        print(f"任务批数: {batch_completed}/{batch_total} ({batch_completed/batch_total*100:.1f}%)")
        print(f"购样金额: {total_amount:.2f}元")

        if all_months:
            print("\n月度统计:")
            for month in all_months:
                print(f"  {month}:")
                for _, row in summary_df.iterrows():
                    if row['任务名称'] not in ['任务批数', '购样金额']:
                        completed_col = f'{month}已完成数'
                        uncompleted_col = f'{month}未完成数'
                        if completed_col in row and row[completed_col] > 0:
                            print(f"    {row['任务名称']}: {row[completed_col]}完成/{row[uncompleted_col]}未完成")

        print(f"\n各sheet总计: {total_completed}/{total_tasks} ({total_completed/total_tasks*100:.1f}%)")
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
