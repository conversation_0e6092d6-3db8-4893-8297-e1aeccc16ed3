# Python 审批表批量生成脚本详细指南

## 脚本概述

这个脚本的主要功能是批量生成审批表Excel文件。它读取一个数据源Excel文件，然后根据模板文件为每一行数据生成一个单独的审批表Excel文件。每个生成的文件都会包含相应的数据，并在右下角（Q17单元格）显示与文件名一致的序号，且序号为右对齐格式。

## 1. 导入模块解释

```python
import os
import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Alignment
import re
from decimal import Decimal, ROUND_HALF_UP
```

### 各模块作用详解

- **os模块**：提供与操作系统交互的功能，用于创建目录、处理文件路径
- **pandas (pd)**：强大的数据分析库，用于读取和处理Excel数据
- **openpyxl.load_workbook**：用于读取和修改Excel文件
- **openpyxl.styles.Alignment**：控制Excel单元格的对齐方式
- **re模块**：提供正则表达式操作，用于字符串处理和清理
- **decimal.Decimal和ROUND_HALF_UP**：提供精确的十进制数学计算，避免浮点数精度问题

## 2. 配置部分和全局变量

```python
# --- 配置部分 ---
SOURCE_EXCEL_PATH = "data.xlsx"
TEMPLATE_EXCEL_PATH = "template.xlsx"
OUTPUT_DIRECTORY = "output"

DISTRICT_CODE_MAP = {
    'th': '天河区局', 'yx': '越秀区局', 'ns': '南沙区局',
    'lw': '荔湾区局', 'zc': '增城区局', 'hp': '黄埔区局',
    'hd': '花都区局', 'hz': '海珠区局', 'py': '番禺区局',
    'ch': '从化区局', 'by': '白云区局'
}
```

### 配置变量详解

- **SOURCE_EXCEL_PATH**：数据源Excel文件的路径
- **TEMPLATE_EXCEL_PATH**：模板Excel文件的路径
- **OUTPUT_DIRECTORY**：生成的Excel文件保存的目录
- **DISTRICT_CODE_MAP**：区域代码到完整区域名称的映射字典

这些配置变量放在脚本开头，使得用户可以轻松地修改这些值，而不需要深入了解脚本的内部逻辑。

## 3. 辅助函数详解

### 3.1 write_to_cell函数

```python
def write_to_cell(ws, cell_coord, value):
    """安全地向一个单元格写入数据,能自动处理合并单元格。"""
    merged_range_str = None
    for r in ws.merged_cells.ranges:
        if cell_coord in r:
            merged_range_str = str(r)
            break
    
    if merged_range_str:
        top_left_cell_coord = merged_range_str.split(':')[0]
        ws.unmerge_cells(merged_range_str)
        ws[top_left_cell_coord] = value
        ws.merge_cells(merged_range_str)
    else:
        ws[cell_coord] = value
```

**功能说明：**
- **作用**：安全地向Excel单元格写入数据，自动处理合并单元格的情况
- **参数**：
  - `ws`：工作表对象
  - `cell_coord`：单元格坐标（如"A1"）
  - `value`：要写入的值

**工作原理：**
1. 检查目标单元格是否在合并单元格范围内
2. 如果是合并单元格，先取消合并，写入数据，再重新合并
3. 如果不是合并单元格，直接写入数据

### 3.2 convert_to_chinese_currency函数

```python
def convert_to_chinese_currency(n):
    """将数字金额转换为中文大写金融字符串。"""
    if not isinstance(n, Decimal):
        n = Decimal(str(n))

    n = n.quantize(Decimal('0.01'))

    units = ['', '拾', '佰', '仟']
    big_units = ['', '万', '亿']
    chars = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    
    # 详细转换逻辑...
```

**功能说明：**
- **作用**：将数字金额转换为中文大写金额
- **参数**：`n`：要转换的数字金额
- **关键数组**：
  - `chars`：中文数字字符（零到玖）
  - `units`：基本单位（拾、佰、仟）
  - `big_units`：大单位（万、亿）

**工作原理：**
1. 将输入转换为Decimal类型，确保精度
2. 分离整数部分和小数部分
3. 使用中文数字字符和单位进行转换
4. 处理特殊情况（如零、万、亿等）

### 3.3 sanitize_filename函数

```python
def sanitize_filename(filename):
    """清理字符串,使其成为一个有效的文件名。"""
    return re.sub(r'[\\/:\*\?"<>\|]', '_', str(filename)).strip(' .')
```

**功能说明：**
- **作用**：清理字符串，使其成为有效的文件名
- **参数**：`filename`：要清理的文件名字符串

**工作原理：**
1. 使用正则表达式查找非法字符：`[\\/:\*\?"<>\|]`
2. 将这些字符替换为下划线
3. 去除首尾的空格和点号

**被替换的字符：**
- `\` 和 `/`：路径分隔符
- `:`：驱动器分隔符
- `*` 和 `?`：通配符
- `"` 和 `<` 和 `>`：引号和比较符
- `|`：管道符

## 4. 主函数逻辑详解

### 4.1 初始化和数据加载

```python
def main():
    """主函数,执行批量生成审批表的任务"""
    if not os.path.exists(OUTPUT_DIRECTORY):
        os.makedirs(OUTPUT_DIRECTORY)
        print(f"创建输出目录: {OUTPUT_DIRECTORY}")

    try:
        df = pd.read_excel(SOURCE_EXCEL_PATH, dtype={'抽样编号': str, '抽样单位': str})
        print(f"成功加载数据源: {SOURCE_EXCEL_PATH}")
    except FileNotFoundError:
        print(f"错误: 数据源文件 '{SOURCE_EXCEL_PATH}' 未找到。")
        return
```

**关键概念解释：**

1. **目录检查和创建**：
   - `os.path.exists()`：检查目录是否存在
   - `os.makedirs()`：创建目录（如果不存在）
   - 为什么需要：确保输出目录存在，避免保存文件时出错

2. **数据加载**：
   - `pd.read_excel()`：读取Excel文件
   - `dtype`参数：指定列的数据类型
   - 为什么指定dtype：确保抽样编号和抽样单位被读取为字符串，避免数字被自动转换

3. **异常处理**：
   - `try-except`：捕获可能的文件不存在错误
   - `return`：如果文件不存在，提前结束函数执行

### 4.2 序号计算

```python
# 计算序号需要补零的位数
total_rows = len(df)
padding_width = len(str(total_rows))
```

**为什么需要这个计算：**
- 如果有100行数据，序号应该是001、002、...、100
- 这样可以保证文件名排序的正确性
- `padding_width`确定需要多少位数字

### 4.3 逐行处理数据

```python
for index, row in df.iterrows():
    try:
        wb = load_workbook(TEMPLATE_EXCEL_PATH)
        ws = wb.active
```

**关键概念：**
- `df.iterrows()`：遍历DataFrame的每一行
- `index`：行索引（从0开始）
- `row`：当前行的数据
- `load_workbook()`：加载Excel模板文件
- `wb.active`：获取活动工作表

### 4.4 数据填充

#### 基本信息填充

```python
# 1. 基本信息
dt = pd.to_datetime(row['日期'])
date_str = f"{dt.year}年{dt.month}月{dt.day}日"
write_to_cell(ws, 'E3', date_str)
write_to_cell(ws, 'J4', row['经办人'])
```

- `pd.to_datetime()`：将日期字符串转换为日期对象
- f-string格式化：将日期格式化为中文格式
- `write_to_cell()`：使用自定义函数安全地写入数据

#### 序号和摘要处理

```python
# 2. 序号和摘要
write_to_cell(ws, 'C7', 1)
write_to_cell(ws, 'C8', 2)

sample_id = row.get('抽样编号', '')
try:
    batch_count = int(row.get('批数', 0))
except (ValueError, TypeError):
    batch_count = 0

if batch_count > 1:
    display_sample_id = f"{sample_id}等"
else:
    display_sample_id = sample_id
```

**关键概念：**
- `row.get()`：安全地获取列值，如果不存在返回默认值
- 异常处理：防止批数转换为整数时出错
- 条件判断：根据批数决定是否添加"等"字

#### 金额处理（最复杂的部分）

```python
# 3. 金额和附件
amount = Decimal(row.get('付款金额', 0)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
amount_in_cents = int(amount * 100)
amount_str_padded = f'{amount_in_cents:08d}'

def fill_amount_row(cells_list):
    found_first_digit = False
    for i, digit_char in enumerate(amount_str_padded):
        digit = int(digit_char)
        if not found_first_digit and digit != 0:
            found_first_digit = True
            if i > 0:
                previous_cell = cells_list[i - 1]
                write_to_cell(ws, previous_cell, '¥')
        if found_first_digit:
            current_cell = cells_list[i]
            write_to_cell(ws, current_cell, digit)
```

**复杂逻辑解释：**
1. **精确计算**：使用Decimal避免浮点数精度问题
2. **转换为分**：`amount * 100`将元转换为分
3. **补零格式化**：`f'{amount_in_cents:08d}'`确保8位数字，不足补零
4. **智能填充**：只在第一个非零数字前添加¥符号

### 4.5 序号和文件保存

```python
# 5. 生成序号并写入右下角
serial_number = f"{index + 1:0{padding_width}d}"
write_to_cell(ws, 'Q17', serial_number)
ws['Q17'].alignment = Alignment(horizontal='right')

# 6. 文件保存
company_name = row.get('收款单位', '未知单位')
file_sample_id = row.get('抽样编号', f'行{index+2}')
sanitized_company = sanitize_filename(company_name)
sanitized_id = sanitize_filename(file_sample_id)

output_filename = f"{serial_number}_支付审批单_{sanitized_company}_{sanitized_id}.xlsx"
output_path = os.path.join(OUTPUT_DIRECTORY, output_filename)
wb.save(output_path)
```

**关键概念：**
- **序号生成**：`f"{index + 1:0{padding_width}d}"`生成补零的序号
- **右对齐设置**：使用Alignment设置单元格对齐方式
- **文件名构造**：使用多个部分组成有意义的文件名
- **路径拼接**：`os.path.join()`安全地拼接路径
- **文件保存**：`wb.save()`保存Excel文件

### 4.6 错误处理

```python
except Exception as e:
    print(f"处理第 {index + 2} 行数据时发生严重错误 (收款单位: {row.get('收款单位', 'N/A')}) - 错误: {e}")
```

**为什么需要错误处理：**
- 数据可能有问题（缺失值、格式错误等）
- 模板文件可能有问题
- 磁盘空间可能不足
- 错误处理确保一行数据出错不会影响其他行的处理

## 5. 核心概念和技巧总结

### 5.1 Python编程核心概念

#### 模块化设计
```python
# 配置与代码分离
SOURCE_EXCEL_PATH = "data.xlsx"
TEMPLATE_EXCEL_PATH = "template.xlsx"

# 功能函数化
def write_to_cell(ws, cell_coord, value):
def convert_to_chinese_currency(n):
def sanitize_filename(filename):
```

**学习要点：**
- 将配置参数放在脚本开头，便于修改
- 将重复使用的功能封装成函数
- 每个函数只做一件事（单一职责原则）

#### 异常处理
```python
try:
    df = pd.read_excel(SOURCE_EXCEL_PATH, dtype={'抽样编号': str, '抽样单位': str})
except FileNotFoundError:
    print(f"错误: 数据源文件 '{SOURCE_EXCEL_PATH}' 未找到。")
    return
```

**学习要点：**
- 预期可能出错的地方使用try-except
- 提供有意义的错误信息
- 优雅地处理错误，不让程序崩溃

#### 数据类型处理
```python
# 确保数据类型正确
dtype={'抽样编号': str, '抽样单位': str}

# 使用Decimal处理金额
amount = Decimal(row.get('付款金额', 0)).quantize(Decimal('0.01'))

# 安全地获取数据
sample_id = row.get('抽样编号', '')
```

**学习要点：**
- 明确指定数据类型，避免自动转换导致的问题
- 使用Decimal处理需要精确计算的数值
- 使用.get()方法安全地获取字典值

#### 字符串格式化
```python
# f-string格式化
date_str = f"{dt.year}年{dt.month}月{dt.day}日"
serial_number = f"{index + 1:0{padding_width}d}"
output_filename = f"{serial_number}_支付审批单_{sanitized_company}_{sanitized_id}.xlsx"
```

**学习要点：**
- f-string是现代Python推荐的字符串格式化方法
- 可以在{}内进行计算和格式化
- `:0{padding_width}d`表示补零到指定位数

#### 文件和路径操作
```python
# 检查和创建目录
if not os.path.exists(OUTPUT_DIRECTORY):
    os.makedirs(OUTPUT_DIRECTORY)

# 安全地拼接路径
output_path = os.path.join(OUTPUT_DIRECTORY, output_filename)
```

**学习要点：**
- 使用os.path.exists()检查文件/目录是否存在
- 使用os.makedirs()创建目录
- 使用os.path.join()拼接路径，确保跨平台兼容性

### 5.2 实际应用技巧

#### 批量处理模式
```python
for index, row in df.iterrows():
    # 处理每一行数据
```

**适用场景：**
- 批量处理Excel数据
- 批量生成报告
- 批量转换文件格式

#### 模板填充模式
```python
wb = load_workbook(TEMPLATE_EXCEL_PATH)
ws = wb.active
# 填充数据到模板
write_to_cell(ws, 'E3', date_str)
```

**适用场景：**
- 生成标准化文档
- 批量制作证书
- 自动化报表生成

#### 数据验证和清理
```python
# 验证和转换数据类型
try:
    batch_count = int(row.get('批数', 0))
except (ValueError, TypeError):
    batch_count = 0

# 清理文件名
sanitized_company = sanitize_filename(company_name)
```

**适用场景：**
- 数据导入前的清理
- 用户输入验证
- 文件名标准化

### 5.3 如何应用到其他项目

#### 修改配置适应新需求
```python
# 只需修改这些配置
SOURCE_EXCEL_PATH = "你的数据文件.xlsx"
TEMPLATE_EXCEL_PATH = "你的模板文件.xlsx"
OUTPUT_DIRECTORY = "你的输出目录"
```

#### 扩展功能
```python
# 添加新的数据处理函数
def process_new_data_type(data):
    # 你的处理逻辑
    return processed_data

# 在主函数中调用
processed_value = process_new_data_type(row['新字段'])
write_to_cell(ws, 'A1', processed_value)
```

#### 添加新的验证规则
```python
def validate_data(row):
    """验证数据的有效性"""
    errors = []
    if not row.get('必填字段'):
        errors.append("必填字段不能为空")
    if row.get('金额', 0) < 0:
        errors.append("金额不能为负数")
    return errors
```

### 5.4 编程最佳实践体现

1. **可读性**：函数名和变量名清楚地表达了意图
2. **可维护性**：配置与代码分离，便于修改
3. **健壮性**：充分的异常处理和数据验证
4. **可扩展性**：模块化设计便于添加新功能
5. **文档化**：每个函数都有清楚的文档字符串

## 6. 学习建议

### 对于Python初学者

1. **理解基础概念**：
   - 先理解变量、函数、循环、条件判断等基础概念
   - 学习如何使用pandas处理数据
   - 了解openpyxl库的基本用法

2. **实践练习**：
   - 尝试修改配置参数，看看效果
   - 添加新的数据字段处理
   - 修改输出格式

3. **逐步扩展**：
   - 添加数据验证功能
   - 增加错误日志记录
   - 优化性能

### 常见问题和解决方案

1. **文件路径问题**：
   - 确保数据文件和模板文件存在
   - 使用绝对路径或相对路径要正确

2. **数据格式问题**：
   - 检查Excel文件的列名是否正确
   - 确保数据类型匹配

3. **权限问题**：
   - 确保有写入输出目录的权限
   - 检查Excel文件是否被其他程序占用

## 总结

这个脚本是一个很好的Python实例，展示了如何使用Python处理Excel文件、批量生成文档、处理数据等常见任务。通过理解这个脚本的工作原理和编程技巧，你可以将这些知识应用到自己的项目中，解决类似的自动化问题。

作为Python初学者，建议你尝试修改这个脚本，例如添加新的字段处理、修改输出格式或增加数据验证，这将帮助你更好地理解和掌握Python编程。

记住，编程是一个实践的过程，多动手、多尝试、多思考，你会逐渐掌握这些技能并能够独立解决问题。
