#!/bin/bash

# 停止所有MCP相关服务
# 清理卡住的容器和进程

echo "=== 停止所有MCP服务 ==="

echo "1. 停止所有Docker Compose服务..."
for dir in "/opt/mcp-server-chart" "/opt/mcp-prebuilt" "/opt/mcp-npm-solution" "/opt/mcp-multi-transport" "/opt/mcp-prebuilt-images" "/opt/mcp-host-build"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        echo "停止 $dir 中的服务..."
        cd "$dir"
        docker compose down --remove-orphans 2>/dev/null || true
    fi
done

echo "2. 强制停止MCP相关容器..."
docker stop $(docker ps -q --filter "name=mcp") 2>/dev/null || true
docker stop $(docker ps -q --filter "name=gpt-vis") 2>/dev/null || true
docker rm $(docker ps -aq --filter "name=mcp") 2>/dev/null || true
docker rm $(docker ps -aq --filter "name=gpt-vis") 2>/dev/null || true

echo "3. 停止PM2进程..."
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true

echo "4. 停止可能的后台进程..."
pkill -f "mcp-server-chart" 2>/dev/null || true
pkill -f "gpt-vis-ssr" 2>/dev/null || true
pkill -f "node.*server.js" 2>/dev/null || true

echo "5. 检查端口占用..."
echo "端口3000占用情况:"
lsof -ti:3000 | xargs kill -9 2>/dev/null || echo "端口3000未被占用"

echo "端口1122占用情况:"
lsof -ti:1122 | xargs kill -9 2>/dev/null || echo "端口1122未被占用"

echo "端口1123占用情况:"
lsof -ti:1123 | xargs kill -9 2>/dev/null || echo "端口1123未被占用"

echo "6. 清理Docker网络..."
docker network prune -f 2>/dev/null || true

echo "7. 检查剩余容器..."
echo "运行中的容器:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "8. 检查端口状态..."
netstat -tlnp | grep -E ":(3000|1122|1123)" || echo "相关端口已释放"

echo ""
echo "=== 清理完成 ==="
echo "所有MCP相关服务已停止"
echo ""
echo "现在可以运行新的部署脚本:"
echo "  sudo ./deploy-native.sh        (推荐：纯宿主机部署)"
echo "  sudo ./deploy-host-build.sh    (宿主机构建+容器运行)"
echo "  sudo ./deploy-prebuilt-images.sh (Ubuntu镜像)"
