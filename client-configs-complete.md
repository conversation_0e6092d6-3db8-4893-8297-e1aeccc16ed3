# 完整的MCP客户端配置指南

服务器IP: ******************

## 🚀 传输方式概览

| 传输方式 | 端口 | 适用场景 | 配置复杂度 |
|---------|------|----------|------------|
| **stdio** | - | 本地客户端 | 简单 |
| **SSE** | 1122 | Web客户端 | 中等 |
| **Streamable** | 1123 | 高性能应用 | 中等 |

## 1. stdio传输配置（本地客户端）

### 1.1 Claude Desktop配置

#### macOS配置文件位置
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

#### Windows配置文件位置
```
%APPDATA%\Claude\claude_desktop_config.json
```

#### 配置内容（使用本地渲染服务）
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "mcp-server-chart",
      "args": ["--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://**************:3000/api/gpt-vis"
      }
    }
  }
}
```

#### 配置内容（使用npx，推荐）
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://**************:3000/api/gpt-vis"
      }
    }
  }
}
```

#### Windows系统配置
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "cmd",
      "args": ["/c", "npx", "-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://**************:3000/api/gpt-vis"
      }
    }
  }
}
```

#### 配置内容（使用在线服务，备用方案）
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "https://antv-studio.alipay.com/api/gpt-vis"
      }
    }
  }
}
```

### 1.2 VSCode + Cline配置

在VSCode的设置中添加：
```json
{
  "cline.mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://**************:3000/api/gpt-vis"
      }
    }
  }
}
```

### 1.3 Cursor配置

在Cursor的设置中添加：
```json
{
  "mcp.servers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://**************:3000/api/gpt-vis"
      }
    }
  }
}
```

### 1.4 Continue配置

在Continue的配置文件中添加：
```json
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart", "--transport", "stdio"],
      "env": {
        "VIS_REQUEST_SERVER": "http://**************:3000/api/gpt-vis"
      }
    }
  }
}
```

## 2. SSE传输配置（Web客户端）

### 2.1 Cherry Studio配置
```
服务器类型: MCP Server
传输方式: SSE
服务器URL: http://**************:1122/sse
名称: MCP Chart Server
描述: 图表生成服务
```

### 2.2 自定义Web客户端配置
```javascript
const mcpClient = new MCPClient({
  transport: 'sse',
  url: 'http://**************:1122/sse',
  headers: {
    'Content-Type': 'application/json'
  }
});
```

### 2.3 MCP Inspector配置
```bash
npx @modelcontextprotocol/inspector http://**************:1122/sse
```

## 3. Streamable传输配置（高性能）

### 3.1 Cherry Studio Streamable配置
```
服务器类型: MCP Server
传输方式: Streamable
服务器URL: http://**************:1123/mcp
名称: MCP Chart Server (Streamable)
描述: 高性能图表生成服务
```

### 3.2 自定义客户端配置
```javascript
const mcpClient = new MCPClient({
  transport: 'streamable',
  url: 'http://**************:1123/mcp',
  options: {
    timeout: 30000,
    retries: 3
  }
});
```

### 3.3 Node.js客户端示例
```javascript
const { Client } = require('@modelcontextprotocol/sdk/client/index.js');
const { StdioClientTransport } = require('@modelcontextprotocol/sdk/client/stdio.js');

// Streamable传输
const transport = new StdioClientTransport({
  command: 'curl',
  args: ['-X', 'POST', 'http://**************:1123/mcp']
});

const client = new Client({
  name: "chart-client",
  version: "1.0.0"
}, {
  capabilities: {}
});

await client.connect(transport);
```

## 4. 环境变量配置

### 4.1 VIS_REQUEST_SERVER
- **本地服务**: `http://**************:3000/api/gpt-vis`
- **在线服务**: `https://antv-studio.alipay.com/api/gpt-vis`

### 4.2 SERVICE_ID（可选）
用于保存图表生成记录，通过支付宝小程序获取。

### 4.3 DISABLED_TOOLS（可选）
禁用特定工具：
```
DISABLED_TOOLS=generate_fishbone_diagram,generate_mind_map
```

## 5. 测试和验证

### 5.1 服务健康检查
```bash
# 渲染服务
curl http://**************:3000/health

# SSE服务
curl http://**************:1122/sse

# Streamable服务
curl http://**************:1123/mcp
```

### 5.2 图表生成测试
```bash
curl -X POST http://**************:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "成功", "value": 70},
      {"category": "失败", "value": 30}
    ],
    "title": "测试结果"
  }'
```

### 5.3 stdio传输测试
```bash
# 测试命令行工具
mcp-server-chart --help

# 测试stdio通信
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{}}}' | mcp-server-chart --transport stdio
```

## 6. 故障排除

### 6.1 stdio传输问题

#### 检查Node.js环境
```bash
node --version
npm --version
npm list -g @antv/mcp-server-chart
```

#### 重新安装包
```bash
npm uninstall -g @antv/mcp-server-chart
npm install -g @antv/mcp-server-chart
```

#### 测试命令行工具
```bash
which mcp-server-chart
mcp-server-chart --version
```

### 6.2 SSE/Streamable传输问题

#### 检查服务状态
```bash
docker compose ps
docker compose logs mcp-sse-server
docker compose logs mcp-streamable-server
```

#### 检查端口占用
```bash
netstat -tlnp | grep -E ":(1122|1123|3000)"
```

#### 检查防火墙
```bash
sudo ufw status
sudo ufw allow 1122
sudo ufw allow 1123
sudo ufw allow 3000
```

### 6.3 图片URL问题

#### 检查图片访问
```bash
curl -I http://**************:3000/images/
```

#### 查看图片列表
```bash
curl http://**************:3000/api/images
```

## 7. 性能优化建议

### 7.1 传输方式选择
- **本地开发**: 使用stdio传输（最快）
- **远程访问**: 使用SSE传输（稳定）
- **高并发**: 使用Streamable传输（高效）

### 7.2 网络优化
- 确保客户端与服务器在同一网络
- 使用有线连接而非WiFi
- 配置适当的超时时间

### 7.3 缓存配置
图片自动缓存1天，可通过以下URL直接访问：
```
http://**************:3000/images/{filename}
```

## 8. 安全注意事项

### 8.1 网络安全
- 仅在可信网络中使用
- 考虑使用VPN或内网部署
- 定期更新依赖包

### 8.2 访问控制
- 配置防火墙规则
- 限制访问IP范围
- 监控服务日志

## 9. 快速配置脚本

### 9.1 一键部署
```bash
sudo ./deploy-all-transports-fixed.sh
```

### 9.2 测试所有传输
```bash
sudo ./test-all-transports-complete.sh
```

### 9.3 生成配置文件
```bash
sudo ./generate-client-configs.sh
```
