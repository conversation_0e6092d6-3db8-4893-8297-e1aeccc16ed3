#!/bin/bash

# 部署支持多种传输方式的MCP解决方案
# 支持SSE、Streamable和stdio传输

set -e

echo "=== 部署多传输方式MCP解决方案 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 安装Node.js（如果没有安装）
if ! command -v node &> /dev/null; then
    echo "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    apt-get install -y nodejs
fi

echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

APP_DIR="/opt/mcp-multi-transport"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# 停止现有服务
echo "停止现有Docker服务..."
for dir in "/opt/mcp-server-chart" "/opt/mcp-prebuilt" "/opt/mcp-npm-solution"; do
    if [ -d "$dir" ]; then
        cd "$dir" 2>/dev/null && docker compose down 2>/dev/null || true
    fi
done
docker stop mcp-all-in-one 2>/dev/null || true
docker rm mcp-all-in-one 2>/dev/null || true

cd $APP_DIR

# 全局安装mcp-server-chart
echo "全局安装mcp-server-chart..."
npm install -g @antv/mcp-server-chart

# 创建渲染服务目录
echo "创建渲染服务..."
mkdir -p render-service
cd render-service

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0",
    "cors": "^2.8.5"
  }
}
EOF

# 创建服务器代码
cat > server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();

// 启用CORS
app.use(cors());
app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr'
  });
});

app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log(`API端点: http://localhost:${PORT}/api/gpt-vis`);
});
EOF

cd $APP_DIR

# 创建Docker Compose配置
echo "创建Docker Compose配置..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        echo "安装系统依赖..."
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++ curl
        echo "安装npm依赖..."
        npm install
        echo "启动渲染服务..."
        npm start
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # SSE传输方式
  mcp-sse-server:
    image: node:18-alpine
    container_name: mcp-sse-server
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..30}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... ($i/30)"
          sleep 2
        done
        echo "启动MCP SSE服务器..."
        mcp-server-chart --transport sse --port 1122
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

  # Streamable传输方式
  mcp-streamable-server:
    image: node:18-alpine
    container_name: mcp-streamable-server
    working_dir: /app
    ports:
      - "1123:1123"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..30}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... ($i/30)"
          sleep 2
        done
        echo "启动MCP Streamable服务器..."
        mcp-server-chart --transport streamable --port 1123
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

# 创建图片目录
mkdir -p images

echo "启动Docker服务..."
docker compose up -d

echo "等待服务启动..."
sleep 60

echo "检查服务状态..."
docker compose ps

echo "查看服务日志..."
echo "--- 渲染服务日志 ---"
docker compose logs --tail=5 gpt-vis-ssr

echo ""
echo "--- SSE服务日志 ---"
docker compose logs --tail=5 mcp-sse-server

echo ""
echo "--- Streamable服务日志 ---"
docker compose logs --tail=5 mcp-streamable-server

echo ""
echo "=== 测试服务 ==="
echo "测试渲染服务..."
for i in {1..5}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务响应正常"
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "测试SSE服务..."
for i in {1..5}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✅ SSE服务响应正常"
        break
    else
        echo "⏳ 等待SSE服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "测试Streamable服务..."
for i in {1..5}; do
    if curl -s http://localhost:1123/mcp > /dev/null; then
        echo "✅ Streamable服务响应正常"
        break
    else
        echo "⏳ 等待Streamable服务启动... ($i/5)"
        sleep 15
    fi
done

echo ""
echo "=== 部署完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - 渲染服务健康检查: http://localhost:3000/health"
echo "  - MCP SSE服务: http://localhost:1122/sse"
echo "  - MCP Streamable服务: http://localhost:1123/mcp"
echo ""
echo "客户端配置示例请查看: mcp-client-configs.md"
