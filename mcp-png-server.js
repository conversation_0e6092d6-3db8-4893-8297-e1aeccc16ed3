#!/usr/bin/env node

/**
 * MCP服务器 - 支持PNG图片返回
 * 支持stdio、SSE、Streamable传输方式
 */

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { SSEServerTransport } = require('@modelcontextprotocol/sdk/server/sse.js');
const express = require('express');
const cors = require('cors');

// 图表生成工具定义
const CHART_TOOLS = [
  {
    name: "generate_png_chart",
    description: "生成PNG格式的图表",
    inputSchema: {
      type: "object",
      properties: {
        type: {
          type: "string",
          enum: ["pie", "column", "line", "scatter", "bar", "area", "radar", "funnel", "boxplot", "violin", "histogram", "treemap", "sankey", "word_cloud"],
          description: "图表类型"
        },
        data: {
          type: "array",
          description: "图表数据"
        },
        title: {
          type: "string",
          description: "图表标题"
        },
        width: {
          type: "number",
          default: 600,
          description: "图表宽度"
        },
        height: {
          type: "number", 
          default: 400,
          description: "图表高度"
        }
      },
      required: ["type", "data"]
    }
  },
  {
    name: "generate_url_chart",
    description: "生成图表并返回URL链接",
    inputSchema: {
      type: "object",
      properties: {
        type: {
          type: "string",
          enum: ["pie", "column", "line", "scatter", "bar", "area", "radar", "funnel", "boxplot", "violin", "histogram", "treemap", "sankey", "word_cloud"],
          description: "图表类型"
        },
        data: {
          type: "array",
          description: "图表数据"
        },
        title: {
          type: "string",
          description: "图表标题"
        },
        width: {
          type: "number",
          default: 600,
          description: "图表宽度"
        },
        height: {
          type: "number",
          default: 400,
          description: "图表高度"
        }
      },
      required: ["type", "data"]
    }
  }
];

// 创建MCP服务器
const server = new Server(
  {
    name: "mcp-png-chart-server",
    version: "1.0.0",
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// 注册工具
server.setRequestHandler('tools/list', async () => {
  return {
    tools: CHART_TOOLS,
  };
});

// 处理工具调用
server.setRequestHandler('tools/call', async (request) => {
  const { name, arguments: args } = request.params;
  
  try {
    const visRequestServer = process.env.VIS_REQUEST_SERVER || 'http://localhost:3000/api/gpt-vis';
    
    if (name === 'generate_png_chart') {
      // 调用PNG API
      const response = await fetch(`${visRequestServer}/png`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(args),
      });
      
      if (!response.ok) {
        throw new Error(`PNG API请求失败: ${response.status} ${response.statusText}`);
      }
      
      const buffer = await response.arrayBuffer();
      const base64 = Buffer.from(buffer).toString('base64');
      
      return {
        content: [
          {
            type: "text",
            text: `PNG图表生成成功！图表类型: ${args.type}`,
          },
          {
            type: "image",
            data: base64,
            mimeType: "image/png",
          },
        ],
      };
    }
    
    if (name === 'generate_url_chart') {
      // 调用URL API
      const response = await fetch(`${visRequestServer}/url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(args),
      });
      
      if (!response.ok) {
        throw new Error(`URL API请求失败: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.errorMessage || '图表生成失败');
      }
      
      return {
        content: [
          {
            type: "text",
            text: `图表生成成功！\n类型: ${args.type}\n标题: ${args.title || '无标题'}\n图片URL: ${result.resultObj}`,
          },
        ],
      };
    }
    
    throw new Error(`未知工具: ${name}`);
    
  } catch (error) {
    console.error('工具调用错误:', error);
    return {
      content: [
        {
          type: "text",
          text: `错误: ${error.message}`,
        },
      ],
      isError: true,
    };
  }
});

// 获取命令行参数
const args = process.argv.slice(2);
const transportType = args.includes('--transport') ? args[args.indexOf('--transport') + 1] : 'stdio';
const port = args.includes('--port') ? parseInt(args[args.indexOf('--port') + 1]) : 1122;

async function main() {
  if (transportType === 'stdio') {
    // stdio传输
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error('MCP PNG服务器已启动 (stdio传输)');
    
  } else if (transportType === 'sse') {
    // SSE传输
    const app = express();
    app.use(cors());
    app.use(express.json());
    
    const transport = new SSEServerTransport('/sse', server);
    app.use('/sse', transport.expressMiddleware);
    
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'ok', 
        transport: 'sse',
        service: 'mcp-png-chart-server',
        timestamp: new Date().toISOString()
      });
    });
    
    app.listen(port, '0.0.0.0', () => {
      console.error(`MCP PNG服务器已启动 (SSE传输) - 端口 ${port}`);
      console.error(`健康检查: http://localhost:${port}/health`);
      console.error(`SSE端点: http://localhost:${port}/sse`);
    });
    
  } else {
    console.error(`不支持的传输方式: ${transportType}`);
    process.exit(1);
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

main().catch((error) => {
  console.error('启动失败:', error);
  process.exit(1);
});
