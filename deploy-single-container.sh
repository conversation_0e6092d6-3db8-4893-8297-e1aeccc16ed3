#!/bin/bash

# 单容器MCP部署脚本
# 将渲染服务和MCP服务放在同一个容器中

set -e

echo "=== 单容器MCP部署脚本 ==="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 创建应用目录
APP_DIR="/opt/mcp-single"
echo "创建应用目录: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# 停止并清理现有容器
echo "清理现有容器..."
docker stop mcp-all-in-one 2>/dev/null || true
docker rm mcp-all-in-one 2>/dev/null || true

# 创建Dockerfile
echo "创建Dockerfile..."
cat > Dockerfile << 'EOF'
FROM node:18-alpine

# 安装系统依赖
RUN apk add --no-cache \
    cairo-dev \
    pango-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev \
    pkgconfig \
    python3 \
    make \
    g++ \
    git \
    curl

# 创建工作目录
WORKDIR /app

# 创建渲染服务
COPY render-service /app/render-service
WORKDIR /app/render-service
RUN npm install

# 克隆并构建MCP项目
WORKDIR /app
RUN git clone https://github.com/antvis/mcp-server-chart.git
WORKDIR /app/mcp-server-chart
RUN npm install && npm run build

# 创建启动脚本
WORKDIR /app
COPY start.sh /app/start.sh
RUN chmod +x /app/start.sh

# 暴露端口
EXPOSE 3000 1122

# 启动脚本
CMD ["/app/start.sh"]
EOF

# 创建渲染服务目录
mkdir -p render-service
cd render-service

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0"
  }
}
EOF

# 创建渲染服务器代码
cat > server.js << 'EOF'
const express = require('express');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();
app.use(express.json({ limit: '10mb' }));

// 创建图片存储目录
const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    // 生成唯一文件名
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    // 保存图片到文件
    fs.writeFileSync(filepath, buffer);
    
    // 返回base64编码的图片
    const imageUrl = `data:image/png;base64,${buffer.toString('base64')}`;
    
    console.log('图表生成成功:', filename);
    
    res.json({
      success: true,
      resultObj: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('渲染错误:', error);
    res.json({
      success: false,
      errorMessage: error.message
    });
  }
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 静态文件服务
app.use('/images', express.static(imageDir));

const PORT = process.env.PORT || 3000;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
});
EOF

# 返回主目录
cd $APP_DIR

# 创建启动脚本
cat > start.sh << 'EOF'
#!/bin/sh

echo "启动MCP All-in-One服务..."

# 启动渲染服务（后台运行）
echo "启动渲染服务..."
cd /app/render-service
npm start &
RENDER_PID=$!

# 等待渲染服务启动
sleep 10

# 检查渲染服务是否启动成功
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 渲染服务启动成功"
else
    echo "❌ 渲染服务启动失败"
    exit 1
fi

# 启动MCP服务
echo "启动MCP服务..."
cd /app/mcp-server-chart
export VIS_REQUEST_SERVER=http://localhost:3000/api/gpt-vis
node build/index.js --transport sse --port 1122 &
MCP_PID=$!

# 等待MCP服务启动
sleep 10

# 检查MCP服务是否启动成功
if curl -s http://localhost:1122/sse > /dev/null; then
    echo "✅ MCP服务启动成功"
else
    echo "❌ MCP服务启动失败"
fi

echo "所有服务已启动"
echo "渲染服务: http://localhost:3000"
echo "MCP服务: http://localhost:1122/sse"

# 保持容器运行
wait $RENDER_PID $MCP_PID
EOF

# 构建Docker镜像
echo "构建Docker镜像..."
docker build -t mcp-all-in-one .

# 运行容器
echo "启动容器..."
docker run -d \
  --name mcp-all-in-one \
  -p 3000:3000 \
  -p 1122:1122 \
  --restart unless-stopped \
  mcp-all-in-one

echo "等待服务启动..."
sleep 60

# 检查容器状态
echo "检查容器状态..."
docker ps | grep mcp-all-in-one

# 查看容器日志
echo "查看容器日志..."
docker logs mcp-all-in-one

# 测试服务
echo ""
echo "=== 测试服务 ==="
echo "测试渲染服务..."
for i in {1..5}; do
    if curl -s http://localhost:3000/health > /dev/null; then
        echo "✅ 渲染服务响应正常"
        curl -s http://localhost:3000/health
        break
    else
        echo "⏳ 等待渲染服务启动... ($i/5)"
        sleep 10
    fi
done

echo ""
echo "测试MCP服务..."
for i in {1..5}; do
    if curl -s http://localhost:1122/sse > /dev/null; then
        echo "✅ MCP服务响应正常"
        break
    else
        echo "⏳ 等待MCP服务启动... ($i/5)"
        sleep 10
    fi
done

echo ""
echo "=== 部署完成 ==="
echo "服务地址:"
echo "  - 渲染服务: http://localhost:3000"
echo "  - 渲染服务健康检查: http://localhost:3000/health"
echo "  - MCP服务: http://localhost:1122/sse"
echo ""
echo "管理命令:"
echo "  查看日志: docker logs -f mcp-all-in-one"
echo "  重启容器: docker restart mcp-all-in-one"
echo "  停止容器: docker stop mcp-all-in-one"
echo "  删除容器: docker rm mcp-all-in-one"
