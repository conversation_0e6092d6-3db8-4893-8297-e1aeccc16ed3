#!/bin/bash

# 诊断MCP连接问题
# 检查服务状态和网络连接

set -e

echo "=== MCP连接问题诊断 ==="

# 检查Docker服务状态
echo "1. 检查Docker服务状态..."
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "2. 检查端口占用情况..."
netstat -tlnp | grep -E ":(3000|1122)" || echo "没有找到相关端口"

echo ""
echo "3. 测试渲染服务连接..."
echo "测试本地连接..."
if curl -s --connect-timeout 5 http://localhost:3000/health; then
    echo "✅ 本地渲染服务连接正常"
else
    echo "❌ 本地渲染服务连接失败"
fi

echo ""
echo "测试容器内连接..."
if docker exec gpt-vis-ssr curl -s --connect-timeout 5 http://localhost:3000/health 2>/dev/null; then
    echo "✅ 容器内渲染服务连接正常"
else
    echo "❌ 容器内渲染服务连接失败"
fi

echo ""
echo "4. 测试MCP服务连接..."
if curl -s --connect-timeout 5 http://localhost:1122/sse; then
    echo "✅ MCP服务连接正常"
else
    echo "❌ MCP服务连接失败"
fi

echo ""
echo "5. 检查容器间网络连接..."
if docker exec mcp-chart-server curl -s --connect-timeout 5 http://gpt-vis-ssr:3000/health 2>/dev/null; then
    echo "✅ 容器间网络连接正常"
else
    echo "❌ 容器间网络连接失败"
fi

echo ""
echo "6. 查看服务日志..."
echo "--- 渲染服务日志 (最近20行) ---"
docker logs --tail=20 gpt-vis-ssr 2>/dev/null || echo "无法获取渲染服务日志"

echo ""
echo "--- MCP服务日志 (最近20行) ---"
docker logs --tail=20 mcp-chart-server 2>/dev/null || echo "无法获取MCP服务日志"

echo ""
echo "7. 测试图表生成API..."
echo "发送测试请求到渲染服务..."
curl -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "测试1", "value": 30},
      {"category": "测试2", "value": 70}
    ]
  }' \
  --connect-timeout 10 \
  --max-time 30 || echo "图表生成API测试失败"

echo ""
echo "8. 检查环境变量..."
echo "MCP容器环境变量:"
docker exec mcp-chart-server env | grep VIS_REQUEST_SERVER || echo "未找到VIS_REQUEST_SERVER环境变量"

echo ""
echo "9. 检查DNS解析..."
echo "容器内DNS解析测试:"
docker exec mcp-chart-server nslookup gpt-vis-ssr 2>/dev/null || echo "DNS解析失败"

echo ""
echo "=== 诊断完成 ==="
echo ""
echo "常见问题和解决方案："
echo "1. 如果容器间网络连接失败，重启Docker服务："
echo "   docker compose down && docker compose up -d"
echo ""
echo "2. 如果渲染服务启动失败，检查依赖安装："
echo "   docker compose logs gpt-vis-ssr"
echo ""
echo "3. 如果MCP服务无法连接渲染服务，检查环境变量："
echo "   docker exec mcp-chart-server env | grep VIS_REQUEST_SERVER"
echo ""
echo "4. 重新部署服务："
echo "   sudo ./deploy-npm-solution.sh"
