#!/bin/bash

# MCP服务测试脚本
# 使用方法: chmod +x test-mcp-services.sh && ./test-mcp-services.sh

echo "=== MCP服务测试脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_service() {
    local service_name=$1
    local url=$2
    local description=$3
    
    echo -e "\n${YELLOW}测试 $description...${NC}"
    
    if curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo -e "${GREEN}✓ $service_name 服务正常${NC}"
        return 0
    else
        echo -e "${RED}✗ $service_name 服务异常${NC}"
        return 1
    fi
}

# 测试渲染服务健康检查
test_service "渲染服务" "http://localhost:3000/health" "渲染服务健康检查"

# 测试MCP服务
test_service "MCP服务" "http://localhost:1122/sse" "MCP服务连接"

# 测试图表生成
echo -e "\n${YELLOW}测试图表生成功能...${NC}"

# 创建测试数据
test_data='{
  "type": "line",
  "data": [
    {"time": "2025-01", "value": 100},
    {"time": "2025-02", "value": 150},
    {"time": "2025-03", "value": 200},
    {"time": "2025-04", "value": 180},
    {"time": "2025-05", "value": 220}
  ]
}'

# 发送测试请求
response=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d "$test_data")

# 检查响应
if echo "$response" | grep -q '"success":true'; then
    echo -e "${GREEN}✓ 图表生成测试成功${NC}"
    
    # 提取文件名（如果有）
    filename=$(echo "$response" | grep -o '"filename":"[^"]*"' | cut -d'"' -f4)
    if [ ! -z "$filename" ]; then
        echo "生成的图片文件: $filename"
    fi
else
    echo -e "${RED}✗ 图表生成测试失败${NC}"
    echo "响应: $response"
fi

# 检查服务状态
echo -e "\n${YELLOW}检查systemd服务状态...${NC}"

services=("gpt-vis-ssr" "mcp-chart-server")

for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service"; then
        echo -e "${GREEN}✓ $service 服务运行中${NC}"
    else
        echo -e "${RED}✗ $service 服务未运行${NC}"
        echo "尝试查看服务状态:"
        systemctl status "$service" --no-pager -l
    fi
done

# 检查端口占用
echo -e "\n${YELLOW}检查端口占用情况...${NC}"

ports=(3000 1122)

for port in "${ports[@]}"; do
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        echo -e "${GREEN}✓ 端口 $port 已被占用${NC}"
    else
        echo -e "${RED}✗ 端口 $port 未被占用${NC}"
    fi
done

# 检查日志
echo -e "\n${YELLOW}最近的服务日志:${NC}"

echo -e "\n--- GPT-Vis-SSR 最近日志 ---"
journalctl -u gpt-vis-ssr --no-pager -n 5

echo -e "\n--- MCP Chart Server 最近日志 ---"
journalctl -u mcp-chart-server --no-pager -n 5

echo -e "\n=== 测试完成 ==="

# 提供有用的命令
echo -e "\n${YELLOW}有用的管理命令:${NC}"
echo "查看实时日志:"
echo "  sudo journalctl -u gpt-vis-ssr -f"
echo "  sudo journalctl -u mcp-chart-server -f"
echo ""
echo "重启服务:"
echo "  sudo systemctl restart gpt-vis-ssr"
echo "  sudo systemctl restart mcp-chart-server"
echo ""
echo "停止服务:"
echo "  sudo systemctl stop gpt-vis-ssr"
echo "  sudo systemctl stop mcp-chart-server"
echo ""
echo "查看服务状态:"
echo "  sudo systemctl status gpt-vis-ssr"
echo "  sudo systemctl status mcp-chart-server"
