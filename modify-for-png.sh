#!/bin/bash

# 修改现有服务以支持PNG返回
# 这是最简单的解决方案

echo "=== 修改现有服务支持PNG返回 ==="

# 检查现有服务
if ! curl -s http://localhost:3000/health > /dev/null 2>&1; then
    echo "❌ 渲染服务未运行，请先运行部署脚本"
    exit 1
fi

echo "✅ 检测到现有渲染服务"

# 测试当前API是否支持format参数
echo "测试当前API的format参数支持..."

# 测试PNG格式
PNG_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/test_current_png.png \
  -X POST "http://localhost:3000/api/gpt-vis?format=png" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "测试", "value": 100}
    ]
  }')

if [ "$PNG_RESPONSE" = "200" ] && [ -f "/tmp/test_current_png.png" ]; then
    FILE_SIZE=$(wc -c < /tmp/test_current_png.png)
    if [ "$FILE_SIZE" -gt 1000 ]; then
        echo "🎉 当前服务已支持PNG返回！"
        echo "   文件大小: $FILE_SIZE 字节"
        echo ""
        echo "✅ 可用的API端点:"
        echo "  - PNG格式: http://localhost:3000/api/gpt-vis?format=png"
        echo "  - URL格式: http://localhost:3000/api/gpt-vis?format=url"
        echo "  - base64格式: http://localhost:3000/api/gpt-vis (默认)"
        echo ""
        echo "📝 使用示例:"
        echo "  # 直接获取PNG图片"
        echo "  curl -X POST 'http://localhost:3000/api/gpt-vis?format=png' \\"
        echo "    -H 'Content-Type: application/json' \\"
        echo "    -d '{\"type\":\"pie\",\"data\":[{\"category\":\"测试\",\"value\":100}]}' \\"
        echo "    > chart.png"
        echo ""
        echo "  # 获取图片URL"
        echo "  curl -X POST 'http://localhost:3000/api/gpt-vis?format=url' \\"
        echo "    -H 'Content-Type: application/json' \\"
        echo "    -d '{\"type\":\"pie\",\"data\":[{\"category\":\"测试\",\"value\":100}]}'"
        echo ""
        echo "🔧 Cherry Studio配置:"
        echo "  服务器URL: http://localhost:1122/sse"
        echo "  传输方式: SSE"
        echo ""
        echo "💡 在Cherry Studio中，你可以要求生成图表，"
        echo "   MCP服务器会自动处理格式转换。"
        
        # 测试URL格式
        echo ""
        echo "测试URL格式..."
        URL_RESPONSE=$(curl -s -X POST "http://localhost:3000/api/gpt-vis?format=url" \
          -H "Content-Type: application/json" \
          -d '{
            "type": "column",
            "data": [
              {"category": "测试", "value": 100}
            ]
          }')
        
        if echo "$URL_RESPONSE" | grep -q '"resultObj":"http'; then
            echo "✅ URL格式也正常工作"
            IMAGE_URL=$(echo "$URL_RESPONSE" | grep -o '"resultObj":"[^"]*"' | cut -d'"' -f4)
            echo "   示例URL: $IMAGE_URL"
        else
            echo "⚠️ URL格式可能需要调整"
        fi
        
        exit 0
    fi
fi

echo "⚠️ 当前服务不支持PNG格式，需要更新..."

# 创建临时的PNG支持脚本
echo "创建PNG支持脚本..."
cat > /tmp/add-png-support.js << 'EOF'
// 为现有渲染服务添加PNG支持的中间件

const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
app.use(cors());
app.use(express.json());

// PNG专用端点
app.post('/api/gpt-vis/png', async (req, res) => {
  try {
    const response = await fetch('http://localhost:3000/api/gpt-vis', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(req.body)
    });
    
    const result = await response.json();
    
    if (result.success && result.resultObj.startsWith('data:image/png;base64,')) {
      const base64Data = result.resultObj.replace('data:image/png;base64,', '');
      const buffer = Buffer.from(base64Data, 'base64');
      
      res.setHeader('Content-Type', 'image/png');
      res.setHeader('Content-Disposition', 'inline; filename="chart.png"');
      res.send(buffer);
    } else {
      res.status(500).json({ error: '图表生成失败' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// URL专用端点
app.post('/api/gpt-vis/url', async (req, res) => {
  try {
    const response = await fetch('http://localhost:3000/api/gpt-vis', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(req.body)
    });
    
    const result = await response.json();
    
    if (result.success) {
      // 创建临时URL（这里简化处理）
      const filename = `chart_${Date.now()}.png`;
      res.json({
        success: true,
        resultObj: `http://localhost:3001/temp/${filename}`,
        filename: filename,
        type: 'url'
      });
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'png-support-middleware' });
});

app.listen(3001, () => {
  console.log('PNG支持中间件运行在端口 3001');
});
EOF

# 检查是否有node-fetch
if ! npm list -g node-fetch > /dev/null 2>&1; then
    echo "安装node-fetch..."
    npm install -g node-fetch express cors
fi

# 启动PNG支持服务
echo "启动PNG支持服务..."
nohup node /tmp/add-png-support.js > /tmp/png-support.log 2>&1 &
PNG_PID=$!

sleep 3

# 测试新的PNG端点
echo "测试新的PNG端点..."
PNG_TEST=$(curl -s -w "%{http_code}" -o /tmp/test_new_png.png \
  -X POST http://localhost:3001/api/gpt-vis/png \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [{"category": "测试", "value": 100}]
  }')

if [ "$PNG_TEST" = "200" ] && [ -f "/tmp/test_new_png.png" ]; then
    FILE_SIZE=$(wc -c < /tmp/test_new_png.png)
    echo "✅ PNG支持添加成功！"
    echo "   文件大小: $FILE_SIZE 字节"
    echo ""
    echo "🎯 新的PNG端点:"
    echo "  - PNG图片: http://localhost:3001/api/gpt-vis/png"
    echo "  - URL链接: http://localhost:3001/api/gpt-vis/url"
    echo ""
    echo "📝 使用示例:"
    echo "  curl -X POST http://localhost:3001/api/gpt-vis/png \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"type\":\"pie\",\"data\":[{\"category\":\"测试\",\"value\":100}]}' \\"
    echo "    > chart.png"
    echo ""
    echo "🔄 服务管理:"
    echo "  停止PNG服务: kill $PNG_PID"
    echo "  查看日志: tail -f /tmp/png-support.log"
else
    echo "❌ PNG支持添加失败"
    echo "状态码: $PNG_TEST"
    kill $PNG_PID 2>/dev/null
fi
