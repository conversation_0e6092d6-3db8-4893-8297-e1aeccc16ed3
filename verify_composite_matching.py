#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl

def verify_composite_matching():
    """验证复合匹配功能的效果"""
    
    print("=== 复合匹配功能验证 ===")
    
    # 读取检验报告
    inspection_df = pd.read_excel('检验报告.xlsx')
    
    # 过滤广州市药品检验所的数据
    inspection_df = inspection_df[inspection_df['承检机构'] == '广州市药品检验所'].copy()
    print(f"过滤后检验报告数据: {len(inspection_df)} 行")
    
    # 统计批准文号和企业名称的组合
    approval_enterprise_combinations = set()
    for _, row in inspection_df.iterrows():
        approval_num = str(row['批准文号或备案号']).strip() if pd.notna(row['批准文号或备案号']) else ""
        enterprise_name = str(row['药品生产企业名称']).strip() if pd.notna(row['药品生产企业名称']) else ""
        if approval_num and approval_num != "nan" and enterprise_name and enterprise_name != "nan":
            approval_enterprise_combinations.add(f"{approval_num}|{enterprise_name}")
    
    print(f"检验报告中的批准文号+企业名称组合数: {len(approval_enterprise_combinations)}")
    
    # 读取最新的分析结果
    result_file = "2025年省抽任务分析结果_20250801_101807.xlsx"
    wb = openpyxl.load_workbook(result_file)
    
    # 检查汇总统计sheet中的匹配方式
    ws_summary = wb['汇总统计']
    print(f"\n=== 汇总统计sheet分析 ===")
    
    # 查找匹配方式列
    match_type_col = None
    for col in range(1, ws_summary.max_column + 1):
        if ws_summary.cell(row=1, column=col).value == '匹配方式':
            match_type_col = col
            break
    
    if match_type_col:
        print(f"找到匹配方式列: 第{match_type_col}列")
        print(f"各sheet的匹配方式:")
        for row in range(2, ws_summary.max_row + 1):
            task_name = ws_summary.cell(row=row, column=1).value
            match_type = ws_summary.cell(row=row, column=match_type_col).value
            if task_name and match_type:
                print(f"  {task_name}: {match_type}")
    
    # 检查几个具体的sheet
    sheets_to_check = ['国家集采', '一致性', '国家基药', 'B证企业', '广东省集采']
    
    print(f"\n=== 具体sheet匹配效果分析 ===")
    for sheet_name in sheets_to_check:
        if sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            
            # 统计完成情况
            completed_count = 0
            total_count = 0
            
            # 查找完成情况列
            completion_col = None
            for col in range(1, ws.max_column + 1):
                if ws.cell(row=1, column=col).value == '完成情况':
                    completion_col = col
                    break
            
            if completion_col:
                for row in range(2, ws.max_row + 1):
                    status = ws.cell(row=row, column=completion_col).value
                    if status:
                        total_count += 1
                        if status == '已完成':
                            completed_count += 1
                
                completion_rate = completed_count / total_count * 100 if total_count > 0 else 0
                print(f"  {sheet_name}: {completed_count}/{total_count} ({completion_rate:.1f}%)")
    
    # 对比分析
    print(f"\n=== 复合匹配效果对比 ===")
    print(f"复合匹配的优势:")
    print(f"  1. 更精确的匹配：同时验证批准文号和企业名称")
    print(f"  2. 避免误匹配：防止不同企业的相同批准文号被错误匹配")
    print(f"  3. 提高数据质量：确保统计结果的准确性")
    
    print(f"\n复合匹配可能导致的变化:")
    print(f"  1. 完成率可能下降：更严格的匹配条件")
    print(f"  2. 数据更可靠：减少假阳性匹配")
    print(f"  3. 分析更精确：基于真实的匹配关系")

if __name__ == "__main__":
    verify_composite_matching()
