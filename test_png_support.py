#!/usr/bin/env python3
"""
测试PNG图片返回功能
"""

import requests
import json
import base64
import os
from datetime import datetime

def test_png_support():
    """测试PNG支持功能"""
    print("=== 测试PNG图片返回功能 ===\n")
    
    # 测试数据
    test_data = {
        "type": "pie",
        "data": [
            {"category": "苹果", "value": 30},
            {"category": "香蕉", "value": 25},
            {"category": "橙子", "value": 20},
            {"category": "葡萄", "value": 15},
            {"category": "其他", "value": 10}
        ],
        "title": "水果销量分布"
    }
    
    base_url = "http://localhost:3000"
    
    # 1. 测试健康检查
    print("1. 测试渲染服务健康状态...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ 渲染服务正常")
            print(f"   服务信息: {health_data}")
        else:
            print(f"❌ 渲染服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到渲染服务: {e}")
        return False
    
    # 2. 测试format=png参数
    print("\n2. 测试format=png参数...")
    try:
        response = requests.post(
            f"{base_url}/api/gpt-vis",
            params={"format": "png"},
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            if 'image/png' in content_type:
                # 直接返回PNG图片
                with open('test_format_png.png', 'wb') as f:
                    f.write(response.content)
                file_size = len(response.content)
                print("✅ format=png 参数测试成功（直接返回PNG）")
                print(f"   文件大小: {file_size} 字节")
                print(f"   内容类型: {content_type}")
            else:
                # 返回JSON
                result = response.json()
                if result.get('success') and 'resultObj' in result:
                    print("✅ format=png 参数测试成功（返回JSON）")
                    print(f"   返回类型: {result.get('type', 'unknown')}")
                    
                    # 如果是base64，保存为文件
                    if result['resultObj'].startswith('data:image/png;base64,'):
                        base64_data = result['resultObj'].replace('data:image/png;base64,', '')
                        image_data = base64.b64decode(base64_data)
                        with open('test_format_png_from_base64.png', 'wb') as f:
                            f.write(image_data)
                        print(f"   已保存为PNG文件，大小: {len(image_data)} 字节")
                else:
                    print(f"❌ format=png 参数测试失败: {result}")
        else:
            print(f"❌ format=png 参数测试失败，状态码: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ format=png 参数测试出错: {e}")
    
    # 3. 测试format=url参数
    print("\n3. 测试format=url参数...")
    try:
        response = requests.post(
            f"{base_url}/api/gpt-vis",
            params={"format": "url"},
            json={
                "type": "column",
                "data": [
                    {"category": "一月", "value": 100},
                    {"category": "二月", "value": 120},
                    {"category": "三月", "value": 90}
                ],
                "title": "月度销售额"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('type') == 'url':
                print("✅ format=url 参数测试成功")
                print(f"   图片URL: {result['resultObj']}")
                
                # 测试URL是否可访问
                try:
                    url_response = requests.get(result['resultObj'], timeout=10)
                    if url_response.status_code == 200:
                        print("   ✅ URL可正常访问")
                        with open('test_from_url.png', 'wb') as f:
                            f.write(url_response.content)
                        print(f"   下载文件大小: {len(url_response.content)} 字节")
                    else:
                        print(f"   ❌ URL无法访问，状态码: {url_response.status_code}")
                except Exception as e:
                    print(f"   ❌ URL访问出错: {e}")
            else:
                print(f"❌ format=url 参数测试失败: {result}")
        else:
            print(f"❌ format=url 参数测试失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ format=url 参数测试出错: {e}")
    
    # 4. 测试默认base64返回
    print("\n4. 测试默认base64返回...")
    try:
        response = requests.post(
            f"{base_url}/api/gpt-vis",
            json={
                "type": "line",
                "data": [
                    {"time": "2024-01", "value": 100},
                    {"time": "2024-02", "value": 120},
                    {"time": "2024-03", "value": 90}
                ],
                "title": "趋势分析"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result['resultObj'].startswith('data:image/png;base64,'):
                print("✅ 默认base64返回测试成功")
                base64_length = len(result['resultObj'])
                print(f"   base64长度: {base64_length} 字符")
                
                # 保存为PNG文件
                base64_data = result['resultObj'].replace('data:image/png;base64,', '')
                image_data = base64.b64decode(base64_data)
                with open('test_default_base64.png', 'wb') as f:
                    f.write(image_data)
                print(f"   已保存为PNG文件，大小: {len(image_data)} 字节")
            else:
                print(f"❌ 默认base64返回测试失败: {result}")
        else:
            print(f"❌ 默认base64返回测试失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 默认base64返回测试出错: {e}")
    
    # 5. 测试Accept头
    print("\n5. 测试Accept头...")
    try:
        response = requests.post(
            f"{base_url}/api/gpt-vis",
            json={
                "type": "scatter",
                "data": [
                    {"x": 10, "y": 15},
                    {"x": 20, "y": 25},
                    {"x": 30, "y": 35}
                ],
                "title": "散点图测试"
            },
            headers={"Accept": "image/png"},
            timeout=30
        )
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            if 'image/png' in content_type:
                print("✅ Accept: image/png 测试成功")
                with open('test_accept_png.png', 'wb') as f:
                    f.write(response.content)
                print(f"   文件大小: {len(response.content)} 字节")
            else:
                print("⚠️ Accept头未生效，返回了JSON格式")
                result = response.json()
                print(f"   响应类型: {result.get('type', 'unknown')}")
        else:
            print(f"❌ Accept头测试失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ Accept头测试出错: {e}")
    
    # 6. 显示生成的文件
    print("\n6. 生成的文件列表:")
    png_files = [f for f in os.listdir('.') if f.endswith('.png') and f.startswith('test_')]
    if png_files:
        for file in png_files:
            size = os.path.getsize(file)
            print(f"   {file}: {size} 字节")
    else:
        print("   没有PNG文件生成")
    
    print("\n=== 测试完成 ===")
    print("\n📊 支持的返回格式:")
    print("  1. PNG图片 - 使用 format=png 参数或 Accept: image/png 头")
    print("  2. URL链接 - 使用 format=url 参数")
    print("  3. base64编码 - 默认格式")
    print("\n🔗 API端点:")
    print(f"  - 通用API: {base_url}/api/gpt-vis")
    print(f"  - 健康检查: {base_url}/health")
    print("\n💡 使用建议:")
    print("  - 如需直接显示图片，使用 format=png")
    print("  - 如需在网页中引用，使用 format=url")
    print("  - 如需嵌入数据，使用默认的base64格式")

if __name__ == "__main__":
    test_png_support()
