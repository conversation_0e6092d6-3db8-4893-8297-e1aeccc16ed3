#!/bin/bash

# 修复外部IP配置
# 专门为服务器IP ************** 配置图片URL返回

set -e

SERVER_IP="**************"
echo "=== 修复外部IP配置 (服务器IP: $SERVER_IP) ==="

# 查找工作目录
WORK_DIR=""
for dir in "/opt/mcp-multi-transport" "/opt/mcp-npm-solution" "/opt/mcp-prebuilt" "/opt/mcp-server-chart"; do
    if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" ]; then
        WORK_DIR="$dir"
        break
    fi
done

if [ -z "$WORK_DIR" ]; then
    echo "错误：找不到MCP部署目录"
    exit 1
fi

echo "使用工作目录: $WORK_DIR"
cd "$WORK_DIR"

# 确保render-service目录存在
if [ ! -d "render-service" ]; then
    echo "创建render-service目录..."
    mkdir -p render-service
fi

# 更新渲染服务代码，强制使用指定的外部IP
echo "1. 更新渲染服务代码（强制使用外部IP: $SERVER_IP）..."
cat > render-service/server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const { render } = require('@antv/gpt-vis-ssr');
const fs = require('fs');
const path = require('path');

const app = express();

// 强制设置外部IP地址
const EXTERNAL_IP = '**************';
const PORT = process.env.PORT || 3000;

console.log(`配置外部IP地址: ${EXTERNAL_IP}`);

// 配置CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false
}));

app.use(express.json({ limit: '10mb' }));

const imageDir = path.join(__dirname, 'images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

// 生成外部可访问的URL
function getExternalImageUrl(filename) {
  return `http://${EXTERNAL_IP}:${PORT}/images/${filename}`;
}

app.post('/api/gpt-vis', async (req, res) => {
  try {
    console.log('收到渲染请求:', JSON.stringify(req.body, null, 2));
    
    const options = req.body;
    
    if (!options.type) {
      throw new Error('缺少图表类型');
    }
    
    const vis = await render(options);
    const buffer = vis.toBuffer();
    
    const filename = `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(imageDir, filename);
    
    fs.writeFileSync(filepath, buffer);
    
    // 生成外部可访问的图片URL
    const imageUrl = getExternalImageUrl(filename);
    
    console.log('图表生成成功:', filename);
    console.log('外部图片URL:', imageUrl);
    
    // 确保返回正确的响应格式
    const response = {
      success: true,
      resultObj: imageUrl,           // 主要字段，返回图片URL
      imageUrl: imageUrl,            // 明确的图片URL字段
      filename: filename,
      size: buffer.length,
      timestamp: new Date().toISOString(),
      externalIP: EXTERNAL_IP,
      // 兼容性：同时提供base64
      base64Url: `data:image/png;base64,${buffer.toString('base64')}`
    };
    
    console.log('返回响应:', {
      success: response.success,
      resultObj: response.resultObj,
      imageUrl: response.imageUrl,
      filename: response.filename
    });
    
    res.json(response);
  } catch (error) {
    console.error('渲染错误:', error);
    res.status(500).json({
      success: false,
      errorMessage: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gpt-vis-ssr',
    externalIP: EXTERNAL_IP,
    port: PORT,
    imageBaseUrl: `http://${EXTERNAL_IP}:${PORT}/images/`,
    imageCount: fs.readdirSync(imageDir).filter(f => f.endsWith('.png')).length,
    version: '1.0.0-external-ip-fixed'
  });
});

// 静态文件服务
app.use('/images', express.static(imageDir, {
  maxAge: '1d',
  setHeaders: (res, path) => {
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cache-Control', 'public, max-age=86400');
  }
}));

// 图片列表API
app.get('/api/images', (req, res) => {
  try {
    const files = fs.readdirSync(imageDir)
      .filter(f => f.endsWith('.png'))
      .map(f => {
        const filepath = path.join(imageDir, f);
        const stats = fs.statSync(filepath);
        return {
          filename: f,
          url: getExternalImageUrl(f),
          size: stats.size,
          created: stats.birthtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));
    
    res.json({
      success: true,
      images: files,
      total: files.length,
      externalIP: EXTERNAL_IP,
      baseUrl: `http://${EXTERNAL_IP}:${PORT}/images/`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      errorMessage: error.message
    });
  }
});

// 测试端点
app.get('/test', (req, res) => {
  res.json({
    message: 'MCP渲染服务测试',
    externalIP: EXTERNAL_IP,
    port: PORT,
    imageBaseUrl: `http://${EXTERNAL_IP}:${PORT}/images/`,
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`GPT-Vis-SSR服务运行在端口 ${PORT}`);
  console.log(`外部访问地址: http://${EXTERNAL_IP}:${PORT}`);
  console.log(`健康检查: http://${EXTERNAL_IP}:${PORT}/health`);
  console.log(`API端点: http://${EXTERNAL_IP}:${PORT}/api/gpt-vis`);
  console.log(`图片访问: http://${EXTERNAL_IP}:${PORT}/images/`);
  console.log(`测试端点: http://${EXTERNAL_IP}:${PORT}/test`);
});
EOF

# 更新package.json
echo "2. 更新package.json..."
cat > render-service/package.json << 'EOF'
{
  "name": "gpt-vis-ssr-service",
  "version": "1.0.0",
  "description": "GPT-Vis SSR rendering service with external IP",
  "main": "server.js",
  "scripts": {
    "start": "node server.js"
  },
  "dependencies": {
    "@antv/gpt-vis-ssr": "latest",
    "express": "^4.18.0",
    "cors": "^2.8.5"
  }
}
EOF

# 更新Docker Compose配置
echo "3. 更新Docker Compose配置..."
cat > docker-compose.yml << EOF
version: '3.8'

services:
  gpt-vis-ssr:
    image: node:18-alpine
    container_name: gpt-vis-ssr
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./render-service:/app
      - ./images:/app/images
    environment:
      - NODE_ENV=production
      - PORT=3000
      - EXTERNAL_IP=$SERVER_IP
    entrypoint: /bin/sh
    command: 
      - -c
      - |
        echo "安装系统依赖..."
        apk add --no-cache cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev pixman-dev pkgconfig python3 make g++ curl
        echo "安装npm依赖..."
        npm install
        echo "启动渲染服务..."
        npm start
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mcp-sse-server:
    image: node:18-alpine
    container_name: mcp-sse-server
    working_dir: /app
    ports:
      - "1122:1122"
    environment:
      - NODE_ENV=production
      - VIS_REQUEST_SERVER=http://gpt-vis-ssr:3000/api/gpt-vis
    entrypoint: /bin/sh
    command:
      - -c
      - |
        echo "安装系统工具..."
        apk add --no-cache curl
        echo "全局安装mcp-server-chart..."
        npm install -g @antv/mcp-server-chart
        echo "等待渲染服务启动..."
        for i in {1..30}; do
          if curl -s http://gpt-vis-ssr:3000/health > /dev/null 2>&1; then
            echo "渲染服务已就绪"
            break
          fi
          echo "等待渲染服务... (\$i/30)"
          sleep 2
        done
        echo "启动MCP SSE服务器..."
        mcp-server-chart --transport sse --port 1122
    depends_on:
      gpt-vis-ssr:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
EOF

# 确保图片目录存在
mkdir -p images

# 重启服务
echo "4. 重启服务..."
docker compose down
docker compose up -d

echo "等待服务启动..."
sleep 45

# 测试服务
echo "5. 测试服务..."
echo "健康检查:"
curl -s http://localhost:3000/health | jq . 2>/dev/null || curl -s http://localhost:3000/health

echo ""
echo "测试外部访问:"
curl -s http://$SERVER_IP:3000/test | jq . 2>/dev/null || curl -s http://$SERVER_IP:3000/test

echo ""
echo "测试图表生成:"
RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "成功", "value": 70},
      {"category": "失败", "value": 30}
    ],
    "title": "外部IP测试"
  }')

echo "生成响应:"
echo "$RESPONSE" | jq . 2>/dev/null || echo "$RESPONSE"

# 检查响应中的字段
if echo "$RESPONSE" | grep -q '"imageUrl"'; then
    echo "✅ 找到imageUrl字段"
    IMAGE_URL=$(echo "$RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    echo "图片URL: $IMAGE_URL"
    
    # 测试图片URL访问
    if curl -s --head "$IMAGE_URL" | grep -q "200 OK"; then
        echo "✅ 图片URL可访问"
    else
        echo "❌ 图片URL无法访问"
        echo "尝试本地访问..."
        FILENAME=$(echo "$RESPONSE" | jq -r '.filename' 2>/dev/null)
        if [ "$FILENAME" != "null" ]; then
            LOCAL_URL="http://localhost:3000/images/$FILENAME"
            if curl -s --head "$LOCAL_URL" | grep -q "200 OK"; then
                echo "✅ 本地URL可访问: $LOCAL_URL"
            else
                echo "❌ 本地URL也无法访问"
            fi
        fi
    fi
else
    echo "❌ 未找到imageUrl字段"
    echo "响应内容: $RESPONSE"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "🌐 服务地址 (外部IP: $SERVER_IP):"
echo "  渲染服务: http://$SERVER_IP:3000"
echo "  健康检查: http://$SERVER_IP:3000/health"
echo "  MCP SSE服务: http://$SERVER_IP:1122/sse"
echo "  图片访问: http://$SERVER_IP:3000/images/"
echo "  测试端点: http://$SERVER_IP:3000/test"
echo ""
echo "🎯 Cherry Studio配置:"
echo "  服务器URL: http://$SERVER_IP:1122/sse"
echo ""
echo "🔧 如果仍有问题，请检查:"
echo "1. 防火墙设置: sudo ufw allow 3000 && sudo ufw allow 1122"
echo "2. 网络连接: ping $SERVER_IP"
echo "3. 服务状态: docker compose ps"
echo "4. 服务日志: docker compose logs gpt-vis-ssr"
EOF
