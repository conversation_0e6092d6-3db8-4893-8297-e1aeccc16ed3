#!/bin/bash

# 快速修复502错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "========================================"
echo "  快速修复502错误"
echo "========================================"
echo

# 1. 检查当前状态
log_step "1. 检查当前服务状态"
docker-compose ps
echo

# 2. 重启有问题的服务
log_step "2. 重启MCP服务器"
docker-compose restart mcp-server-chart-streamable
sleep 10

log_step "3. 重启nginx"
docker-compose restart nginx
sleep 5

# 4. 检查服务是否正常
log_step "4. 检查服务状态"
docker-compose ps
echo

# 5. 测试连接
log_step "5. 测试连接"

echo "测试直接连接MCP服务器..."
if curl -s http://192.168.50.105:1123 >/dev/null 2>&1; then
    log_info "✓ 直接连接MCP服务器成功"
else
    log_error "✗ 直接连接MCP服务器失败"
fi

echo "测试通过nginx代理..."
response=$(curl -s -w "%{http_code}" http://192.168.50.105/mcp -o /dev/null)
if [ "$response" = "200" ] || [ "$response" = "405" ]; then
    log_info "✓ nginx代理连接成功 (HTTP $response)"
else
    log_error "✗ nginx代理连接失败 (HTTP $response)"
fi

# 6. 测试JSON-RPC
log_step "6. 测试JSON-RPC协议"

echo "测试initialize方法..."
response=$(curl -s -X POST http://192.168.50.105:1123 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2024-11-05",
      "capabilities": {},
      "clientInfo": {"name": "test", "version": "1.0.0"}
    }
  }' 2>/dev/null)

if echo "$response" | jq . >/dev/null 2>&1; then
    log_info "✓ JSON-RPC协议测试成功"
    echo "响应: $(echo "$response" | jq -c .)"
else
    log_error "✗ JSON-RPC协议测试失败"
    echo "响应: $response"
fi

echo

# 7. 提供配置建议
log_step "7. LobeChat配置建议"

echo "如果直接连接成功，请在LobeChat中使用："
echo '{'
echo '  "name": "Chart Generator",'
echo '  "url": "http://192.168.50.105:1123",'
echo '  "type": "streamable-http"'
echo '}'
echo

echo "如果nginx代理成功，请在LobeChat中使用："
echo '{'
echo '  "name": "Chart Generator",'
echo '  "url": "http://192.168.50.105/mcp",'
echo '  "type": "streamable-http"'
echo '}'
echo

# 8. 检查日志
log_step "8. 检查最新日志"
echo "=== MCP Streamable 最新日志 ==="
docker-compose logs --tail=5 mcp-server-chart-streamable
echo
echo "=== Nginx 最新日志 ==="
docker-compose logs --tail=5 nginx

echo
echo "========================================"
echo "  修复完成"
echo "========================================"
