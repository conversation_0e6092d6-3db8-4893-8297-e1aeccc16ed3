#!/bin/bash

# 测试外部IP配置
# 专门测试服务器IP ************** 的图片URL返回

SERVER_IP="**************"
echo "=== 测试外部IP配置 (服务器IP: $SERVER_IP) ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_result() {
    local test_name="$1"
    local result="$2"
    
    if [ "$result" = "success" ]; then
        echo -e "${GREEN}✅ $test_name${NC}"
    else
        echo -e "${RED}❌ $test_name${NC}"
    fi
}

echo "1. 检查基础连接..."

# 检查本地服务
if curl -s --connect-timeout 5 http://localhost:3000/health > /dev/null; then
    print_result "本地服务连接 (localhost:3000)" "success"
else
    print_result "本地服务连接 (localhost:3000)" "fail"
fi

# 检查外部IP服务
if curl -s --connect-timeout 5 http://$SERVER_IP:3000/health > /dev/null; then
    print_result "外部IP服务连接 ($SERVER_IP:3000)" "success"
else
    print_result "外部IP服务连接 ($SERVER_IP:3000)" "fail"
fi

echo ""
echo "2. 检查服务配置..."

# 获取健康检查信息
echo "本地健康检查:"
LOCAL_HEALTH=$(curl -s http://localhost:3000/health 2>/dev/null)
echo "$LOCAL_HEALTH" | jq . 2>/dev/null || echo "$LOCAL_HEALTH"

echo ""
echo "外部IP健康检查:"
EXTERNAL_HEALTH=$(curl -s http://$SERVER_IP:3000/health 2>/dev/null)
echo "$EXTERNAL_HEALTH" | jq . 2>/dev/null || echo "$EXTERNAL_HEALTH"

# 检查是否配置了正确的外部IP
if echo "$LOCAL_HEALTH" | grep -q "**************"; then
    print_result "外部IP配置正确" "success"
else
    print_result "外部IP配置正确" "fail"
fi

echo ""
echo "3. 测试图表生成..."

# 测试1：饼图
echo "测试饼图生成..."
PIE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "pie",
    "data": [
      {"category": "外部访问", "value": 60},
      {"category": "本地访问", "value": 40}
    ],
    "title": "访问方式分布"
  }' 2>/dev/null)

echo "饼图响应:"
echo "$PIE_RESPONSE" | jq . 2>/dev/null || echo "$PIE_RESPONSE"

# 检查响应字段
echo ""
echo "4. 检查响应字段..."

if echo "$PIE_RESPONSE" | grep -q '"success":true'; then
    print_result "图表生成成功" "success"
else
    print_result "图表生成成功" "fail"
    echo "错误信息: $(echo "$PIE_RESPONSE" | jq -r '.errorMessage' 2>/dev/null)"
fi

if echo "$PIE_RESPONSE" | grep -q '"imageUrl"'; then
    print_result "包含imageUrl字段" "success"
    IMAGE_URL=$(echo "$PIE_RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
    echo "图片URL: $IMAGE_URL"
else
    print_result "包含imageUrl字段" "fail"
fi

if echo "$PIE_RESPONSE" | grep -q '"resultObj".*http'; then
    print_result "resultObj包含URL" "success"
    RESULT_OBJ=$(echo "$PIE_RESPONSE" | jq -r '.resultObj' 2>/dev/null)
    echo "resultObj: $RESULT_OBJ"
else
    print_result "resultObj包含URL" "fail"
fi

# 检查URL格式
if echo "$PIE_RESPONSE" | grep -q "**************"; then
    print_result "URL包含正确的外部IP" "success"
else
    print_result "URL包含正确的外部IP" "fail"
fi

echo ""
echo "5. 测试图片URL访问..."

# 提取图片URL
IMAGE_URL=$(echo "$PIE_RESPONSE" | jq -r '.imageUrl' 2>/dev/null)
FILENAME=$(echo "$PIE_RESPONSE" | jq -r '.filename' 2>/dev/null)

if [ "$IMAGE_URL" != "null" ] && [ -n "$IMAGE_URL" ]; then
    echo "测试图片URL: $IMAGE_URL"
    
    # 测试URL访问
    if curl -s --head --connect-timeout 10 "$IMAGE_URL" | grep -q "200 OK"; then
        print_result "图片URL可访问" "success"
        
        # 获取文件大小
        SIZE=$(curl -s --head "$IMAGE_URL" | grep -i content-length | cut -d' ' -f2 | tr -d '\r')
        echo "文件大小: $SIZE bytes"
    else
        print_result "图片URL可访问" "fail"
        
        # 尝试本地访问
        if [ "$FILENAME" != "null" ]; then
            LOCAL_URL="http://localhost:3000/images/$FILENAME"
            echo "尝试本地URL: $LOCAL_URL"
            if curl -s --head "$LOCAL_URL" | grep -q "200 OK"; then
                print_result "本地图片URL可访问" "success"
            else
                print_result "本地图片URL可访问" "fail"
            fi
        fi
    fi
else
    echo "❌ 无法提取图片URL"
fi

echo ""
echo "6. 测试多种图表类型..."

# 测试柱状图
echo "测试柱状图..."
BAR_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "column",
    "data": [
      {"category": "测试1", "value": 100},
      {"category": "测试2", "value": 150},
      {"category": "测试3", "value": 120}
    ],
    "title": "柱状图测试"
  }' 2>/dev/null)

if echo "$BAR_RESPONSE" | grep -q '"imageUrl".***************'; then
    print_result "柱状图返回正确URL" "success"
else
    print_result "柱状图返回正确URL" "fail"
fi

# 测试折线图
echo "测试折线图..."
LINE_RESPONSE=$(curl -s -X POST http://localhost:3000/api/gpt-vis \
  -H "Content-Type: application/json" \
  -d '{
    "type": "line",
    "data": [
      {"time": "2024-01", "value": 100},
      {"time": "2024-02", "value": 120},
      {"time": "2024-03", "value": 150}
    ],
    "title": "折线图测试"
  }' 2>/dev/null)

if echo "$LINE_RESPONSE" | grep -q '"imageUrl".***************'; then
    print_result "折线图返回正确URL" "success"
else
    print_result "折线图返回正确URL" "fail"
fi

echo ""
echo "7. 检查图片目录..."

# 检查图片列表
echo "获取图片列表..."
IMAGES_LIST=$(curl -s http://localhost:3000/api/images 2>/dev/null)
echo "$IMAGES_LIST" | jq . 2>/dev/null || echo "$IMAGES_LIST"

IMAGE_COUNT=$(echo "$IMAGES_LIST" | jq -r '.total' 2>/dev/null)
if [ "$IMAGE_COUNT" != "null" ] && [ "$IMAGE_COUNT" -gt 0 ]; then
    print_result "图片目录有文件 ($IMAGE_COUNT 个)" "success"
else
    print_result "图片目录有文件" "fail"
fi

echo ""
echo "8. 测试MCP服务..."

# 检查MCP SSE服务
if curl -s --connect-timeout 5 http://localhost:1122/sse > /dev/null; then
    print_result "MCP SSE服务 (localhost:1122)" "success"
else
    print_result "MCP SSE服务 (localhost:1122)" "fail"
fi

if curl -s --connect-timeout 5 http://$SERVER_IP:1122/sse > /dev/null; then
    print_result "MCP SSE服务 ($SERVER_IP:1122)" "success"
else
    print_result "MCP SSE服务 ($SERVER_IP:1122)" "fail"
fi

echo ""
echo "=== 测试结果汇总 ==="

# 生成测试报告
echo "📊 服务状态:"
echo "  - 渲染服务: http://$SERVER_IP:3000"
echo "  - MCP服务: http://$SERVER_IP:1122/sse"
echo "  - 图片访问: http://$SERVER_IP:3000/images/"

echo ""
echo "📋 响应格式检查:"
if echo "$PIE_RESPONSE" | grep -q '"imageUrl".***************'; then
    echo -e "  ${GREEN}✅ imageUrl字段包含正确的外部IP${NC}"
else
    echo -e "  ${RED}❌ imageUrl字段不包含正确的外部IP${NC}"
fi

if echo "$PIE_RESPONSE" | grep -q '"resultObj".***************'; then
    echo -e "  ${GREEN}✅ resultObj字段包含正确的外部IP${NC}"
else
    echo -e "  ${RED}❌ resultObj字段不包含正确的外部IP${NC}"
fi

echo ""
echo "🎯 Cherry Studio配置:"
echo "  服务器URL: http://$SERVER_IP:1122/sse"

echo ""
echo "🔧 如果测试失败，请运行修复脚本:"
echo "  sudo ./fix-external-ip.sh"

echo ""
echo "📖 在浏览器中测试:"
echo "  健康检查: http://$SERVER_IP:3000/health"
echo "  图片列表: http://$SERVER_IP:3000/api/images"
echo "  测试端点: http://$SERVER_IP:3000/test"

# 创建简单的测试页面
echo ""
echo "9. 创建测试页面..."
cat > /tmp/test_page.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>MCP图表测试 - $SERVER_IP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 5px; }
        #result { margin-top: 20px; padding: 10px; background: #f5f5f5; }
        img { max-width: 100%; height: auto; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>MCP图表生成测试</h1>
    <p>服务器IP: $SERVER_IP</p>
    
    <div class="test-section">
        <h3>测试图表生成</h3>
        <button onclick="testChart()">生成测试图表</button>
        <div id="result"></div>
    </div>
    
    <script>
        async function testChart() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在生成图表...';
            
            try {
                const response = await fetch('http://$SERVER_IP:3000/api/gpt-vis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: 'pie',
                        data: [
                            {category: '成功', value: 70},
                            {category: '失败', value: 30}
                        ],
                        title: '测试结果分布'
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.imageUrl) {
                    resultDiv.innerHTML = \`
                        <h4>生成成功！</h4>
                        <p><strong>图片URL:</strong> <a href="\${data.imageUrl}" target="_blank">\${data.imageUrl}</a></p>
                        <img src="\${data.imageUrl}" alt="生成的图表">
                        <p><strong>文件名:</strong> \${data.filename}</p>
                        <p><strong>大小:</strong> \${data.size} bytes</p>
                    \`;
                } else {
                    resultDiv.innerHTML = \`<p style="color: red;">生成失败: \${data.errorMessage || '未知错误'}</p>\`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<p style="color: red;">请求失败: \${error.message}</p>\`;
            }
        }
    </script>
</body>
</html>
EOF

echo "测试页面已创建: /tmp/test_page.html"
echo "可以将此文件复制到Web服务器进行测试"
